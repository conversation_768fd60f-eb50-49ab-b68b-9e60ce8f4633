package api

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get List Roles", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := testResultPassed
		if desc.Failed {
			result = testResultFailed
		}
		fmt.Printf("Get List Roles: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 when success", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		body := hcl.JSON(`{}`)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// Mock Get Count Roles
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountRoles)).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		// Mock Get List Roles
		user := resources.SampleDataUser()
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListRoles)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(user.ID, user.Name, user.Status, user.CreatedAt, user.UpdatedAt, user.CreatedBy, user.UpdatedBy))

		// Perform request
		res, err := permissionManagemeneClient.Post(GetListRoles, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		// Validate response
		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).Should(Equal(200))
		// Validate JSON Body
		count, exists := responseBody["count"]
		Expect(exists).Should(BeTrue())
		Expect(count).Should(Equal(float64(1))) // Assumes count is a float64 when unmarshaled

		data, exists := responseBody["data"]
		Expect(exists).Should(BeTrue())
		Expect(data).Should(HaveLen(1)) // Assuming there is one role in the data

		// Validate the first item in the "data" array
		roleData := data.([]interface{})[0].(map[string]interface{})

		Expect(int64(roleData["id"].(float64))).Should(Equal(user.ID))
		Expect(roleData["name"]).Should(Equal(user.Name))
		Expect(int64(roleData["status"].(float64))).Should(Equal(user.Status))
		Expect(roleData).To(HaveKey("createdAt"))
		Expect(roleData).To(HaveKey("updatedAt"))
		Expect(roleData).To(HaveKey("updatedBy"))
	})

	It("should return 400: when offset is more than total data", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		body := hcl.JSON(`{
			"offset": 2
		}`)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// Mock Get Count Roles
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountRoles)).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		expectedResponse := `
		{
			"code":"badRequest",
			"message":"offset is more than total data"
		}
		`

		// Perform request
		res, err := permissionManagemeneClient.Post(GetListRoles, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 403: failed to create permission due to invalid access, when user lacks ROLE_MANAGEMENT permission", func() {
		db, _, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		body := hcl.JSON(`{}`)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		delete(permissionDetail.Permissions, string(constants.RoleManagement)) // Simulate missing permission
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// Perform request
		res, err := permissionManagemeneClient.Post(GetListRoles, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
	})
})
