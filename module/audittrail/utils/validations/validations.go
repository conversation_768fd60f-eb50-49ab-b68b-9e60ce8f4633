package validations

import (
	"fmt"
	"strings"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
)

// ValidateGetAuditTrailsRequest ...
func ValidateGetAuditTrailsRequest(req *api.GetAuditTrailsRequest) error {
	if req.WithPagination != nil && *req.WithPagination {
		if req.ActivityType == nil || strings.TrimSpace(*req.ActivityType) == "" {
			return servus.ServiceError{
				HTTPCode: apiError.FieldMissing.HTTPStatusCode(),
				Code:     string(apiError.FieldMissing),
				Message:  "ActivityType is missing or invalid in request",
			}
		}
		if !isValidAuditTrailActivityType(*req.ActivityType) {
			return servus.ServiceError{
				HTTPCode: apiError.FieldMissing.HTTPStatusCode(),
				Code:     string(apiError.FieldMissing),
				Message: fmt.Sprintf(
					"ActivityType must be one of: %s, %s, %s, %s",
					api.AuditTrailActivityType_CUSTOMER_SEARCH,
					api.AuditTrailActivityType_DATA_SEGREGATION,
					api.AuditTrailActivityType_MODULE_CONFIG,
					api.AuditTrailActivityType_FEATURE_FLAG,
				),
			}
		}
	} else {
		if req.Identifier == nil || *req.Identifier == "" {
			return servus.ServiceError{
				HTTPCode: apiError.FieldMissing.HTTPStatusCode(),
				Code:     string(apiError.FieldMissing),
				Message:  "Identifier is missing in request",
			}
		}
		if req.IdentifierType == nil || *req.IdentifierType == "" {
			return servus.ServiceError{
				HTTPCode: apiError.FieldMissing.HTTPStatusCode(),
				Code:     string(apiError.FieldMissing),
				Message:  "IdentifierType is missing in request",
			}
		}
	}
	return nil
}

func isValidAuditTrailActivityType(activityType string) bool {
	switch api.AuditTrailActivityType(activityType) {
	case api.AuditTrailActivityType_CUSTOMER_SEARCH,
		api.AuditTrailActivityType_DATA_SEGREGATION,
		api.AuditTrailActivityType_MODULE_CONFIG,
		api.AuditTrailActivityType_FEATURE_FLAG:
		return true
	default:
		return false
	}
}
