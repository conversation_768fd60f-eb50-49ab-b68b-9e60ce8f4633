// Package jumpcloud is for handle sso capability with jumpcloud
package jumpcloud

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	constant "gitlab.super-id.net/bersama/opsce/onedash-be/constants"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"
)

// VerifyEmail ...
func (c *Client) VerifyEmail(ctx context.Context, token string, logTag string) (UserInfo, error) {
	var data UserInfo
	request, err := http.NewRequest("GET", c.BaseURL, nil)
	if err != nil {
		return UserInfo{}, err
	}

	request.Header.Set("Authorization", "BEARER "+token)
	response, err := c.Client.Do(request)
	if err != nil {
		return UserInfo{}, err
	}
	defer func() {
		err = response.Body.Close()
		if err != nil {
			slogwrapper.FromContext(ctx).Warn(logTag, "failed to close response body")
		}
	}()

	// Read response body for debugging
	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		slog.FromContext(ctx).Warn(constant.JumpCloudLogTag, "failed to read response body for logging")
	} else {
		slog.FromContext(ctx).Info(constant.JumpCloudLogTag, fmt.Sprintf("[DEBUG] Jumpcloud Response Status: %d, Body: %s", response.StatusCode, string(bodyBytes)))
		// Recreate the body reader since we consumed it
		response.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	if response.StatusCode != 200 {
		return UserInfo{}, errorwrapper.Error(apiError.Idem, "Couldn't login using jumpcloud")
	}
	err = json.NewDecoder(response.Body).Decode(&data)
	if err != nil {
		return UserInfo{}, err
	}
	return data, nil
}
