package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	customerMasterAPI "gitlab.myteksi.net/bersama/customer-master/api/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"
)

// GetCustomers handles getting multiple customers
func (p *process) GetCustomers(ctx context.Context, req *api.GetCustomersRequest) (*api.GetCustomersResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.CustomerSearch, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	var resultErr error
	defer func() {
		status := "VALID"
		if resultErr != nil {
			status = "INVALID"
		}
		if writeErr := p.writeLogAuditCustomerSearch(ctx, user, req, status); writeErr != nil {
			slog.FromContext(ctx).Warn(constants.GetCustomersLogTag, writeErr.Error(), utils.GetTraceID(ctx))
		}
	}()

	// validate request
	resultErr = p.validateGetCustomersRequest(req)
	if resultErr != nil {
		return nil, resultErr
	}

	items, isLastPage, resultErr := p.getCustomers(ctx, req)
	if resultErr != nil {
		return nil, resultErr
	}

	if len(items) == 0 {
		resultErr = errorwrapper.Error(apiError.ResourceNotFound, "customer not found")
		return nil, resultErr
	}

	// merge customer data
	customersResp := &api.GetCustomersResponse{
		Customers:  items,
		IsLastPage: isLastPage,
	}

	return customersResp, nil
}

// validateGetCustomersRequest is to validate identifier
func (p *process) validateGetCustomersRequest(req *api.GetCustomersRequest) error {
	if req == nil {
		return errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	if req.Identifier == "" {
		return errorwrapper.Error(apiError.BadRequest, "identifier is empty")
	}
	if req.IdentifierType == "" {
		return errorwrapper.Error(apiError.BadRequest, "identifier type is empty")
	}
	if req.Page == 0 {
		return errorwrapper.Error(apiError.BadRequest, "page is empty")
	}
	switch req.IdentifierType {
	case api.IdentifierType_NAME:
		if !utils.IsValidName(req.Identifier) {
			return errorwrapper.Error(apiError.BadRequest, "Fill with 3 - 100 characters")
		}
	case api.IdentifierType_CIF:
		validCIF, errorMessage := utils.IsValidCIF(req.Identifier)
		if !validCIF {
			return errorwrapper.Error(apiError.BadRequest, errorMessage)
		}
	case api.IdentifierType_ACCOUNT_NUMBER:
		validAccountNumber, errorMessage := utils.IsValidAccountID(req.Identifier)
		if !validAccountNumber {
			return errorwrapper.Error(apiError.BadRequest, errorMessage)
		}
	case api.IdentifierType_ID_NUMBER:
		validNIK, errorMessage := utils.IsValidNIK(req.Identifier)
		if !validNIK {
			return errorwrapper.Error(apiError.BadRequest, errorMessage)
		}
	case api.IdentifierType_SAFE_ID:
		if !utils.IsValidSafeID(req.Identifier) {
			return errorwrapper.Error(apiError.BadRequest, "Fill with 36 characters")
		}
	case api.IdentifierType_PHONE_NUMBER:
		validPhoneNumber, errorMessage := utils.IsValidPhoneNumber(req.Identifier)
		if !validPhoneNumber {
			return errorwrapper.Error(apiError.BadRequest, errorMessage)
		}
	default:
		return errorwrapper.Error(apiError.BadRequest, "identifier type is invalid")
	}

	return nil
}

func (p *process) writeLogAuditCustomerSearch(ctx context.Context, user *permissionManagementStorage.UserDTO, req *api.GetCustomersRequest, status string) error {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     req.Identifier,
		IdentifierType: string(req.IdentifierType),
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		Title:        "Customer Search",
		Description:  status,
		ActivityType: constants.CustomerSearchActivity,
	}
	_, err := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return err
}

func (p *process) getCustomers(ctx context.Context, req *api.GetCustomersRequest) ([]interface{}, bool, error) {
	if req.IdentifierType == api.IdentifierType_NAME || req.IdentifierType == api.IdentifierType_PHONE_NUMBER {
		return p.useCustomerMaster(ctx, req)
	}
	return p.useCustomerLean(ctx, req)
}

func (p *process) useCustomerMaster(ctx context.Context, req *api.GetCustomersRequest) ([]interface{}, bool, error) {
	resp, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), int(req.Page))
	if err != nil {
		return nil, false, err
	}

	items, err := processCustomerData(ctx, resp.Items)
	if err != nil {
		return nil, false, err
	}

	return items, resp.IsLastPage, nil
}

func (p *process) useCustomerLean(ctx context.Context, req *api.GetCustomersRequest) ([]interface{}, bool, error) {
	leanResp, err := p.GetCustomerLean(ctx, req.Identifier, string(req.IdentifierType), []string{constants.PersonalInfoType, constants.ContactsType, constants.IdentitiesType})
	if err != nil {
		return nil, false, err
	}

	items, err := processLeanCustomerData(ctx, leanResp)
	if err != nil {
		return nil, false, err
	}

	return items, true, nil
}

func processLeanCustomerData(ctx context.Context, resp *customerMasterAPI.GetCustomerByIdentifierResponse) ([]interface{}, error) {
	var customerData map[string]interface{}
	customerJSON, err := json.Marshal(resp.Customer.Data)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to marshall customer data", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	if err := json.Unmarshal(customerJSON, &customerData); err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to unmarshall customer data", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	result := map[string]interface{}{
		"fullName":    customerData["name"],
		"safeID":      customerData["ID"],
		"dateOfBirth": customerData["dateOfBirth"],
		"cif":         customerData["publicID"],
		"status":      customerData["status"],
	}

	extractContactInfo(customerData, result)
	extractIdentityInfo(customerData, result)

	return []interface{}{result}, nil
}

func processCustomerData(ctx context.Context, data []customerExperienceAPI.CustomerOpsResponse) ([]interface{}, error) {
	var items []interface{}
	for _, customerData := range data {
		filteredMap, err := processCustomer(ctx, customerData)
		if err != nil {
			return nil, err
		}
		items = append(items, filteredMap)
	}
	return items, nil
}

func processCustomer(ctx context.Context, customerData customerExperienceAPI.CustomerOpsResponse) (map[string]interface{}, error) {
	customerMap, err := convertToMap(ctx, customerData)
	if err != nil {
		return nil, err
	}

	filteredMap := make(map[string]interface{})
	extractCustomerInfo(customerMap, filteredMap)
	extractApplicationInfo(customerMap, filteredMap)

	return filteredMap, nil
}

func convertToMap(ctx context.Context, data interface{}) (map[string]interface{}, error) {
	customerJSON, err := json.Marshal(data)
	if err != nil {
		slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to marshall customer.Customer.Data to customerJSON", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	var customerMap map[string]interface{}
	if err = json.Unmarshal(customerJSON, &customerMap); err != nil {
		slogwrapper.FromContext(ctx).Error(constants.GetCustomersLogTag, "Unable to unmarshall customerJSON to customerMap", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	return customerMap, nil
}

func extractCustomerInfo(customerMap map[string]interface{}, filteredMap map[string]interface{}) {
	customer, ok := customerMap["customer"].(map[string]interface{})
	if !ok {
		return
	}

	fields := map[string]string{
		"fullName":    "fullName",
		"ID":          "safeID",
		"dateOfBirth": "dateOfBirth",
		"NIK":         "nik",
		"CIF":         "cif",
		"status":      "customerStatus",
	}

	for sourceKey, targetKey := range fields {
		if value, exists := customer[sourceKey]; exists && value != nil {
			filteredMap[targetKey] = value
		}
	}

	extractContactInfo(customer, filteredMap)
}

func extractContactInfo(customer map[string]interface{}, filteredMap map[string]interface{}) {
	contacts, ok := customer["contacts"].([]interface{})
	if !ok || len(contacts) == 0 {
		return
	}

	if contact, ok := contacts[0].(map[string]interface{}); ok {
		if phoneNumber, exists := contact["phoneNumber"]; exists && phoneNumber != nil {
			filteredMap["phoneNumber"] = phoneNumber
		}
	}
}

func extractIdentityInfo(customer map[string]interface{}, filteredMap map[string]interface{}) {
	identities, ok := customer["identities"].([]interface{})
	if !ok || len(identities) == 0 {
		return
	}

	if identity, ok := identities[0].(map[string]interface{}); ok {
		if nik, exists := identity["IDNumber"]; exists && nik != nil {
			filteredMap["nik"] = nik
		}
	}
}

func extractApplicationInfo(customerMap map[string]interface{}, filteredMap map[string]interface{}) {
	applications, ok := customerMap["applications"].([]interface{})
	if !ok || len(applications) == 0 {
		return
	}

	application, ok := applications[0].(map[string]interface{})
	if !ok {
		return
	}

	if status, exists := application["status"]; exists && status != nil {
		filteredMap["applicationStatus"] = status
	}

	extractTimeFields(application, filteredMap)
}

func extractTimeFields(application map[string]interface{}, filteredMap map[string]interface{}) {
	timeFields := []string{"createdAt", "updatedAt"}

	for _, field := range timeFields {
		if timeStr, exists := application[field].(string); exists && timeStr != "" {
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				filteredMap[field] = utils.DateAsString(t)
			}
		}
	}
}
