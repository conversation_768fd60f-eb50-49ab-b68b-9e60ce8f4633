package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Update User", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("success update user", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUser)).WithArgs(sqlmock.AnyArg(), 1, "New User", "<EMAIL>", 1, user.UserID).WillReturnResult(sqlmock.NewResult(2, 1))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).WithArgs(user.ID).WillReturnRows(
					mocker.NewRows([]string{"id", "name"}).AddRow(1, "IAM").AddRow(2, "Core Banking Maker"))

				// remove existing row
				mocker.ExpectExec(regexp.QuoteMeta(TestQueryDeleteUserRole)).WithArgs(user.ID, 2).WillReturnResult(sqlmock.NewResult(3, 1))

				// add new row
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUserRole)).WithArgs(3, sqlmock.AnyArg(), 1, user.ID).WillReturnResult(sqlmock.NewResult(3, 1))

				// reset user permission
				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.NewUserID).Return(true, nil)

				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(user.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				// audit trail
				auditTrail := resources.SampleDataUpdateUserAuditTrail(2)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(resources.SuccessResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("user id is not found", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnError(sql.ErrNoRows)

				expectedRes := `{
					"code":"fieldInvalid",
					"message":"failed to update user",
					"errors":[
						{
							"errorCode":"fieldInvalid",
							"message":"user doesn't exist"
						}
					]
				}`
				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.FieldInvalid.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("missing required fields", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnError(sql.ErrNoRows)

				expectedRes := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
					  {
						"errorCode": "badRequest",
						"message": "failed to validate request Key: 'UpdateUserRequest.Name' Error:Field validation for 'Name' failed on the 'required' tag"
					  }
					]
				  }`
				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("user is not having access to the element", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				permDetail.Permissions["USER_MANAGEMENT"] = 1
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("user is not valid", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
	})
	Context("Error request", func() {
		When("email already used", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				email := "<EMAIL>"
				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUser)).WithArgs(sqlmock.AnyArg(), 1, "New User", "<EMAIL>", 1, user.UserID).WillReturnError(resources.GetErrorDuplicateEmailUsers(email))

				expectedRes := `{
					"code": "internalServerError",
					"message": "failed to update user",
					"errors": [
						{
							"errorCode": "internalServerError",
							"message": "error 1062: Duplicate entry '<EMAIL>' for key 'users.users_unique_email'"
						}
					]
				}`
				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error when update user", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUser)).WithArgs(sqlmock.AnyArg(), 1, "New User", "<EMAIL>", 1, user.UserID).WillReturnError(sql.ErrConnDone)

				expectedRes := `{
					"code":"internalServerError",
					"message":"failed to update user",
					"errors":[
						{
							"errorCode":"internalServerError",
							"message":"sql: connection is already closed"
						}
					]
				}`
				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error when update user role", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,3],
					"userId": "new-user-id"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUser)).WithArgs(sqlmock.AnyArg(), 1, "New User", "<EMAIL>", 1, user.UserID).WillReturnResult(sqlmock.NewResult(2, 1))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).WithArgs(user.ID).WillReturnRows(
					mocker.NewRows([]string{"id", "name"}).AddRow(1, "IAM").AddRow(2, "Core Banking Maker"))

				// remove existing row
				mocker.ExpectExec(regexp.QuoteMeta(TestQueryDeleteUserRole)).WithArgs(user.ID, 2).WillReturnResult(sqlmock.NewResult(3, 1))

				// add new row
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUserRole)).WithArgs(3, sqlmock.AnyArg(), 1, user.ID).WillReturnError(sql.ErrConnDone)

				// reset user permission
				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.NewUserID).Return(true, nil)

				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(user.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				// audit trail
				auditTrail := resources.SampleDataUpdateUserAuditTrail(2)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				expectedRes := `{
					"code": "internalServerError",
					"message": "failed to create role",
					"errors": [
					  {
						"errorCode": "internalServerError",
						"message": "sql: connection is already closed"
					  }
					]
				  }`
				res, err := permissionManagemeneClient.Put(UpdateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})
