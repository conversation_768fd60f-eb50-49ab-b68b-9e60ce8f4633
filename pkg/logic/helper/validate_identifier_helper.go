package helper

import (
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func ValidateIdentifier(idType string, identifier string, page int64) error {
	if err := validateBaseRequest(idType, identifier, page); err != nil {
		return err
	}
	return validateIdentifierByType(idType, identifier)
}

func validateBaseRequest(idType, identifier string, page int64) error {
	if identifier == "" {
		return errorwrapper.Error(apiError.BadRequest, "identifier is empty")
	}
	if idType == "" {
		return errorwrapper.Error(apiError.BadRequest, "identifier type is empty")
	}
	if page == 0 {
		return errorwrapper.Error(apiError.BadRequest, "page is empty")
	}
	return nil
}

func validateIdentifierByType(idType string, identifier string) error {
	switch idType {
	case string(api.IdentifierType_NAME):
		if !utils.IsValidName(identifier) {
			return errorwrapper.Error(apiError.BadRequest, "Fill with 3 - 100 characters")
		}
	case string(api.IdentifierType_CIF):
		if valid, msg := utils.IsValidCIF(identifier); !valid {
			return errorwrapper.Error(apiError.BadRequest, msg)
		}
	case string(api.IdentifierType_ACCOUNT_NUMBER):
		if valid, msg := utils.IsValidAccountID(identifier); !valid {
			return errorwrapper.Error(apiError.BadRequest, msg)
		}
	case string(api.IdentifierType_ID_NUMBER):
		if valid, msg := utils.IsValidNIK(identifier); !valid {
			return errorwrapper.Error(apiError.BadRequest, msg)
		}
	case string(api.IdentifierType_SAFE_ID):
		if !utils.IsValidSafeID(identifier) {
			return errorwrapper.Error(apiError.BadRequest, "Fill with 36 characters")
		}
	case string(api.IdentifierType_PHONE_NUMBER):
		if valid, msg := utils.IsValidPhoneNumber(identifier); !valid {
			return errorwrapper.Error(apiError.BadRequest, msg)
		}
	default:
		return errorwrapper.Error(apiError.BadRequest, "identifier type is invalid")
	}
	return nil
}
