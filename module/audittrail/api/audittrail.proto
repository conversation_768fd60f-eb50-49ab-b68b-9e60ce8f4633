syntax = "proto3";

package onedash;

option go_package = "gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api";

import "gxs/api/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

enum SortOrder {
  ASC = 0;
  DESC = 1;
}

message Sort {
  string column = 1;
  SortOrder sort = 2;
}

message GetAuditTrailsRequest {
  optional string identifier = 1;
  optional string identifierType = 2;
  optional int64 limit = 4;
  optional string startingBefore = 5;
  optional string endingAfter = 6;
  optional string activityType = 7;
  optional Sort sortBy = 8 [(gxs.api.validate) = "omitempty,audit_trails_sort"];
  optional bool withPagination = 9;
}

message GetAuditTrailsResponse {
  repeated AuditTrails auditTrails = 1;
  map<string, string> links = 2;
}

message AuditTrails {
  int64 id = 1;
  string createdAt = 2;
  int64 createdBy = 3;
  string identifier = 4;
  string identifierType = 5;
  string title = 6;
  string description = 7;
  string activityType = 8;
  string referenceID = 9;
  google.protobuf.Any extraParams = 10;
  string userName = 11;
}

enum AuditTrailActivityType {
  CUSTOMER_SEARCH = 0;
  DATA_SEGREGATION = 1;
  MODULE_CONFIG = 2;
  FEATURE_FLAG = 3;
}

message AuditTrailRequest {
  string identifier = 1 [(gxs.api.validate) = "required"];
  string identifierType = 2 [(gxs.api.validate) = "required"];
  optional string title = 3;
  optional string description = 4;
  string activityType = 5 [(gxs.api.validate) = "required"];
  optional string referenceID = 6;
  map<string, google.protobuf.Any> extraParams = 7;
  int64 createdBy = 8 [(gxs.api.validate) = "required"];
}

message AuditTrailResponse {
  int64 id = 1;
}

service AuditTrail {
  // GetAuditTrails is API to get audit trails
  rpc GetAuditTrails(GetAuditTrailsRequest) returns (GetAuditTrailsResponse) {
    option (google.api.http) = {
      get: "/api/v1/get-audit-trails",
      body: "*",
    };
  }

  // [Deprecated] GetAuditTrails is API to get audit trails
  rpc DeprecatedGetAuditTrails(GetAuditTrailsRequest) returns (GetAuditTrailsResponse) {
    option (google.api.http) = {
      post: "/api/v1/get-audit-trails",
      body: "*",
    };
  }
  // CreateAuditTrails is API to create Audit Trail
  rpc CreateAuditTrails (AuditTrailRequest) returns (AuditTrailResponse){
    option (google.api.http) = {
      post : "/api/v1/audit-trails",
      body : "*",
    };
  }
}
