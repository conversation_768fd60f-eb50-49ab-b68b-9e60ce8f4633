package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// UpdatePermissionStatus ...
//
// nolint: dupl
func (p *PermissionManagementImpl) UpdatePermissionStatus(ctx context.Context, req *api.UpdatePermissionStatusRequest) (*api.UpdatePermissionStatusResponse, error) {
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	perm, err := storage.GetPermissionByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get permission")
	}

	if perm.Status == req.Status {
		return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("failed to update the permission status due to already %v", constants.StatusWordingMap[int(req.Status)]))
	}

	if perm.Status != req.Status && req.Status == 0 {
		roles, errCheck := storage.GetRolesByPermissionUsed(ctx, db, perm)
		if errCheck != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to validate permission")
		}
		if len(roles) > 0 {
			return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("failed to update permission due to still used in roles: %v.", strings.Join(roles, ", ")))
		}
	}

	perm.Status = req.Status
	perm.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	perm.UpdatedBy = sql.NullInt64{Int64: user.ID, Valid: true}

	err = storage.UpdatePermissionStatus(ctx, db, perm)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update permission")
	}

	title := fmt.Sprintf("Update Permission Status to %v", constants.StatusWordingMap[int(req.Status)])
	auditTrailReq, auditErr := helper.WriteLogAuditTrailPermission(ctx, perm, title, user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.UpdatePermissionStatusResponse{
		Status: "Success",
	}, nil
}
