package handlers

import (
	"context"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
)

// GetAuditTrails is API to get audit trails
func (a *AuditTrailService) GetAuditTrails(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error) {
	// check permission
	// need to be here to break circular dependency
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.AuditTrail, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// Get the audit trails
	auditTrails, err := auditTrailLogic.AuditTrailProcess.GetAuditTrails(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get audit trails")
	}

	// Return the audit trails
	return auditTrails, nil
}
