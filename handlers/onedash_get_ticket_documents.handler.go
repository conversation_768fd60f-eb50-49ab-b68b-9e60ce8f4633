package handlers

import (
	"context"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetTicketDocuments is api to get ticket documents by ticket id
func (o *OnedashService) GetTicketDocuments(ctx context.Context, req *api.GetTicketDocumentsRequest) (*api.GetTicketDocumentsResponse, error) {
	res, err := logic.Process.GetTicketDocuments(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket documents")
	}
	return res, nil
}
