package storage

import (
	"context"
	"database/sql"
	"encoding/json"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateBulkFileProcessorDumpRow creates a new row in the bulk_file_processor_dump table.
func CreateBulkFileProcessorDumpRow(ctx context.Context, db *sql.DB, dto *BulkFileProccessorDumpDTO) (int64, error) {
	var query = `
		INSERT INTO bulk_file_processor_dumps (identifier, identifier_type, type, status, data, created_at, reference_id, file_name)
		VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)
	`

	// marshall the data
	data, err := json.Marshal(dto.Data)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	var id int64
	result, err := db.ExecContext(ctx, query, dto.Identifier, dto.IdentifierType, dto.Type, dto.Status, data, dto.ReferenceID, dto.FileName)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.Idem, "failed to create bulk_file_processor_dump row")
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}
