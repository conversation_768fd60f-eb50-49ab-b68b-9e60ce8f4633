package constants

// Define constants related with customer search
const (
	// SectionArrayType ...
	SectionArrayType = "sectionArray"
	// SectionType ...
	SectionType = "section"
	// SectionQnA ...
	SectionQnA = "sectionQnA"
	// SectionCustom ...
	SectionCustom = "sectionCustom"
	// TabType ...
	TabType = "tab"
	// SubTabType ...
	SubTabType = "subTab"
	// DropdownType ...
	DropdownType = "dropdown"
	// TableType ...
	TableType = "table"
	// EyeButton ...
	EyeButton = "eye"
	// TablePreselectType ...
	TablePreselectType = "tablePreselect"
	// OnelinerType ...
	OnelinerType = "oneliner"
	// SectionTableType ...
	SectionTableType = "sectionTable"
	// SectionDetailType ...
	SectionDetailType = "sectionDetail"
	// SectionDetailTableType
	SectionDetailTableType = "sectionDetailTable"
)

// HasChildrenTypes ...
var HasChildrenTypes = map[string]bool{
	SectionArrayType:   true,
	SectionType:        true,
	TabType:            true,
	TableType:          true,
	TablePreselectType: true,
	SubTabType:         true,
}

// List of children types QnA
const (
	// KeyQuestion ...
	KeyQuestion = "question"
	// KeyAnswer ...
	KeyAnswer = "answer"
	// LabelQuestion ...
	LabelQuestion = "Question"
	// LabelAnswer ...
	LabelAnswer = "Answer"
)

// List of fetched data type in customer lean
const (
	// PersonalInfoType ...
	PersonalInfoType = "personalInfo"
	// AddressesType ...
	AddressesType = "addresses"
	// ContactsType ...
	ContactsType = "contacts"
	// IdentitiesType ...
	IdentitiesType = "identities"
)

// List of tab
const (
	// KeyCustomerHomepage ...
	KeyCustomerHomepage = "customerHomepage"

	// KeyFunding ...
	KeyFunding = "funding"

	// KeyLending ...
	KeyLending = "lending"

	// KeyCustomerTransactionList ...
	KeyCustomerTransactionList = "customerTransactionList"
)

// List of Sub Tab
const (
	// KeyListOfFundingAccount ...
	KeyListOfFundingAccount = "listOfFundingAccount"

	// KeyTransactionConfiguration ...
	KeyTransactionConfiguration = "transactionConfiguration"

	// KeyExternalLinkages ...
	KeyExternalLinkages = "externalLinkages"

	// KeyOnboardingHistory ...
	KeyOnboardingHistory = "keyOnboardingHistory"

	// KeyMainCasaAndSakuAccount ...
	KeyMainCasaAndSakuAccount = "mainCasaAndSakuAccount"

	// KeyPiggybank ...
	KeyPiggybank = "piggybank"

	// KeyTermDeposit ...
	KeyTermDeposit = "termDeposit"

	// KeyMainCasa ...
	KeyMainCasa = "mainCasa"

	// KeySaku ...
	KeySaku = "saku"

	// KeyOVONabung ...
	KeyOVONabung = "ovoNabung"

	// KeyTransactionList ...
	KeyTransactionList = "transactionList"

	// KeyTransactionDetails ...
	KeyTransactionDetails = "transactionDetails"

	// KeyLendingPAS ...
	KeyLendingPAS = "lendingPAS"

	// KeyLOCAccount ...
	KeyLOCAccount = "locAccount"

	// KeyLoanAccount ...
	KeyLoanAccount = "loanAccount"
)

// List of sections
const (
	// KeyAddressDetails ...
	KeyAddressDetails = "addressDetails"
	// KeyContactDetails ...
	KeyContactDetails = "contactDetails"
	// KeyOCRDetails ...
	KeyOCRDetails = "ocrDetails"
	// KeyBeneficiaryOwnerDetails ...
	KeyBeneficiaryOwnerDetails = "beneficiaryOwnerDetails"
	// KeyOccupationDetails ...
	KeyOccupationDetails = "occupationDetails"
	// KeyCustomerEnhancedDueDiligence ...
	KeyCustomerEnhancedDueDiligence = "customerEnhancedDueDiligence"
	// KeyCustomerReEnhancedDueDiligence ...
	KeyCustomerReEnhancedDueDiligence = "recdd"
	// KeyCustomerDetails ...
	KeyCustomerDetails = "customerDetails"
	// KeyOnboardingDetails ...
	KeyOnboardingDetails = "onboardingDetails"
	// KeyChannelAccess ...
	KeyChannelAccess = "channelAccess"
	// KeyOnboardingDetailsVerificationDetails ...
	KeyOnboardingDetailsVerificationDetails = "onboardingVerificationDetails"
	// KeyCustomerLevelTxLimit ...
	KeyCustomerLevelTxLimit = "customerLevelTxLimit"
	// KeyBankWideLevelTxLimit ...
	KeyBankWideLevelTxLimit = "bankWideLevelTxLimit"
	// KeyPartnerLinkage ...
	KeyPartnerLinkage = "partnerLinkage"
	// KeyLinkingHistory ...
	KeyLinkingHistory = "linkingHistory"
	// KeyReauthHistory ...
	KeyReauthHistory = "reauthHistory"
	// KeyActivityLog ...
	KeyActivityLog = "activityLog"
	// KeyMfaLog ...
	KeyMfaLog = "mfaLog"
	// KeyDeviceLoginInfo ...
	KeyDeviceLoginInfo = "deviceLoginInfo"
	// KeyFacialMatchingLivenessCheckLog ...
	KeyFacialMatchingLivenessCheckLog = "facialMatchingLivenessCheckLog"
	// KeyInteractionLog ...
	KeyInteractionLog = "interactionLog"
	// KeyMarketingPreference ...
	KeyMarketingPreference = "marketingPreference"
	// KeyMarketingPreferenceCurrent ...
	KeyMarketingPreferenceCurrent = "marketingPreferenceCurrent"
	// KeyMarketingPreferenceLog ...
	KeyMarketingPreferenceLog = "marketingPreferenceLog"
	// KeyVideoCallInformation ...
	KeyVideoCallInformation = "videoCallInformation"
	// KeyOnboardingHistorySection ...
	KeyOnboardingHistorySection = "onboardingHistorySection"
)

// Onboarding Details Data Keys
const (
	// KeyOnboardingDetailsKtpFile ...
	KeyOnboardingDetailsKtpFile = "ktpFile"
	// KeyOnboardingDetailsSelfieFile ...
	KeyOnboardingDetailsSelfieFile = "selfieFile"
	// KeyOnboardingDetailsApplicationStatus ...
	KeyOnboardingDetailsApplicationStatus = "onboardingApplicationStatus"
	// KeyOnboardingDetailsStatusRemarks ...
	KeyOnboardingDetailsStatusRemarks = "statusRemarks"
	// KeyOnboardingDetailsChannel ...
	KeyOnboardingDetailsChannel = "onboardingChannel"
	// KeyOnboardingDetailsApplicationID ...
	KeyOnboardingDetailsApplicationID = "applicationID"
	// KeyOnboardingDetailsReferralCode ...
	KeyOnboardingDetailsReferralCode = "referralCode"
	// KeyOnboardingDetailsAttemptTime ...
	KeyOnboardingDetailsAttemptTime = "onboardingAttemptTime"
	// KeyOnboardingDetailsQCFlag ...
	KeyOnboardingDetailsQCFlag = "qcFlag"
	// KeyOnboardingDetailsLivenessAttemptTime ...
	KeyOnboardingDetailsLivenessAttemptTime = "livenessAttemptTime"
	// KeyOnboardingDetailsOcrAttemptTime ...
	KeyOnboardingDetailsOcrAttemptTime = "ocrAttemptTime"
	// KeyOnboardingDetailsSubmittedAt ...
	KeyOnboardingDetailsSubmittedAt = "submittedAt"
	// KeyOnboardingDetailsCreatedAt ...
	KeyOnboardingDetailsCreatedAt = "createdAt"
	// KeyOnboardingDetailsApprovedAt ...
	KeyOnboardingDetailsApprovedAt = "approvedAt"
	// KeyOnboardingDetailsUpdatedAt ...
	KeyOnboardingDetailsUpdatedAt = "updatedAt"
	// KeyOnboardingDetailsExpiresAt ...
	KeyOnboardingDetailsExpiresAt = "expiresAt"
	// KeyOnboardingDetailsAMLCheck ...
	KeyOnboardingDetailsAMLCheck = "AMLCheck"
	// KeyOnboardingDetailsDuplicateCheckNIK ...
	KeyOnboardingDetailsDuplicateCheckNIK = "duplicateCheckNIK"
	// KeyOnboardingDetailsDukcapilFacialMatch ...
	KeyOnboardingDetailsDukcapilFacialMatch = "dukcapilFacialMatch"
	// KeyOnboardingDetailsDukcapilDataMatchNIK ...
	KeyOnboardingDetailsDukcapilDataMatchNIK = "dukcapilDataMatchNIK"
	// KeyOnboardingDetailsDukcapilDataMatchFullName ...
	KeyOnboardingDetailsDukcapilDataMatchFullName = "dukcapilDataMatchFullName"
	// KeyOnboardingDetailsDukcapilDataMatchDOB ...
	KeyOnboardingDetailsDukcapilDataMatchDOB = "dukcapilDataMatchDOB"
	// KeyOnboardingDetailsDukcapilDataMatchSelfie ...
	KeyOnboardingDetailsDukcapilDataMatchSelfie = "dukcapilDataMatchSelfie"
	// KeyOnboardingDetailsDukcapilMotherMaidenName ...
	KeyOnboardingDetailsDukcapilMotherMaidenName = "dukcapilMotherMaidenName"
	// KeyOnboardingDetailsAcceptedConsent ...
	KeyOnboardingDetailsAcceptedConsent = "acceptedConsent"
	// KeyOnboardingDetailsUnacceptedConsent ...
	KeyOnboardingDetailsUnacceptedConsent = "unacceptedConsent"
	// KeyOnboardingDetailsLivenessCheck ...
	KeyOnboardingDetailsLivenessCheck = "livenessCheck"
	// KeyOnboardingDetailsRiskRatingCheck ...
	KeyOnboardingDetailsRiskRatingCheck = "riskRatingCheck"
)

// Customer Details Keys
const (
	// KeyCustomerDetailsFullName ...
	KeyCustomerDetailsFullName = "fullName"
	// KeyCustomerDetailsAlias ...
	KeyCustomerDetailsAlias = "alias"
	// KeyCustomerDetailsNik ...
	KeyCustomerDetailsNik = "nik"
	// KeyCustomerDetailsNationality ...
	KeyCustomerDetailsNationality = "nationality"
	// KeyCustomerDetailsGender ...
	KeyCustomerDetailsGender = "gender"
	// KeyCustomerDetailsAmlOngoingScreening ...
	KeyCustomerDetailsAmlOngoingScreening = "amlOngoingScreening"
	// KeyCustomerDetailsLatestRiskRating ...
	KeyCustomerDetailsLatestRiskRating = "latestRiskRating"
	// KeyCustomerDetailsStartDate ...
	KeyCustomerDetailsStartDate = "startDate"
	// KeyCustomerDetailsDateOfBirth ...
	KeyCustomerDetailsDateOfBirth = "dateOfBirth"
	// KeyCustomerDetailsPlaceOfBirth ...
	KeyCustomerDetailsPlaceOfBirth = "placeOfBirth"
	// KeyCustomerDetailsMotherMaidenName ...
	KeyCustomerDetailsMotherMaidenName = "motherMaidenName"
	// KeyCustomerDetailsMaritalStatus ...
	KeyCustomerDetailsMaritalStatus = "maritalStatus"
	// KeyCustomerDetailsIsBO ...
	KeyCustomerDetailsIsBO = "isBO"
	// KeyCustomerDetailsIsECDD ...
	KeyCustomerDetailsIsECDD = "ecddFlag"
	// KeyCustomerDetailsPurposeOfAccount ...
	KeyCustomerDetailsPurposeOfAccount = "purposeOfAccount"
	// KeyCustomerDetailsCif ...
	KeyCustomerDetailsCif = "cif"
	// KeyCustomerDetailsStatus ...
	KeyCustomerDetailsStatus = "status"
	// KeyCustomerDetailsSafeID ...
	KeyCustomerDetailsSafeID = "safeID"
	// KeyCustomerDetailsTotalCasaAccountRemainingBalance ...
	KeyCustomerDetailsTotalCasaAccountRemainingBalance = "totalCasaAccountRemainingBalance"
	// KeyCustomerDetailsSourceOfFunds ...
	KeyCustomerDetailsSourceOfFunds = "sourceOfFunds"
	// KeyCustomerDetailsMonthlyIncome ...
	KeyCustomerDetailsMonthlyIncome = "monthlyIncome"
)

// OCR Response Data Keys ...
const (
	// KeyOCRTransactionID ...
	KeyOCRTransactionID = "ocrTransactionId"
	// KeyOCRSource ...
	KeyOCRSource = "ocrSource"
	// KeyOCRFullName ...
	KeyOCRFullName = "ocrFullName"
	// KeyOCRNIK ...
	KeyOCRNIK = "ocrNik"
	// KeyOCRDateOfBirth ...
	KeyOCRDateOfBirth = "ocrDateOfBirth"
	// KeyOCRMaritalStatus ...
	KeyOCRMaritalStatus = "ocrMaritalStatus"
	// KeyOCRPlaceOfBirth ...
	KeyOCRPlaceOfBirth = "ocrPlaceOfBirth"
	// KeyOCRGender ...
	KeyOCRGender = "ocrGender"
	// KeyOCROccupation ...
	KeyOCROccupation = "ocrOccupation"
	// KeyOCRNationality ...
	KeyOCRNationality = "ocrNationality"
	// KeyOCRStreet ...
	KeyOCRStreet = "ocrStreet"
	// KeyOCRCity ...
	KeyOCRCity = "ocrCity"
	// KeyOCRProvince ...
	KeyOCRProvince = "ocrProvince"
	// KeyOCRSubdistrict ...
	KeyOCRSubdistrict = "ocrSubdistrict"
	// KeyOCRVillage ...
	KeyOCRVillage = "ocrVillage"
	// KeyOCRRT ...
	KeyOCRRT = "ocrRt"
	// KeyOCRRW ...
	KeyOCRRW = "ocrRw"
)

// Onboarding History Section Keys ...
const (
	// KeyOnboardingHistorySectionID ...
	KeyOnboardingHistorySectionID = "onboardingHistorySectionApplicationID"
	// KeyOnboardingHistorySectionStatus ...
	KeyOnboardingHistorySectionStatus = "onboardingHistorySectionStatus"
	// KeyOnboardingHistorySectionSubmittedAt ...
	KeyOnboardingHistorySectionSubmittedAt = "onboardingHistorySectionSubmittedAt"
	// KeyOnboardingHistorySectionRuleID ...
	KeyOnboardingHistorySectionRuleID = "onboardingHistorySectionRuleID"
	// KeyOnboardingHistorySectionStatusRemarks ...
	KeyOnboardingHistorySectionStatusRemarks = "onboardingHistorySectionStatusRemark"
	// KeyOnboardingHistorySectionExpiredAt ...
	KeyOnboardingHistorySectionExpiredAt = "onboardingHistorySectionExpiredAt"
	// KeyOnboardingHistorySectionDetails ...
	KeyOnboardingHistorySectionDetails = "onboardingHistorySectionDetails"
	// KeyOnboardingHistorySectionDetailType ...
	KeyOnboardingHistorySectionDetailType = "ohsDetailType"
	// KeyOnboardingHistorySectionDetailURL ...
	KeyOnboardingHistorySectionDetailURL = "ohsDetailUrl"
	// KeyOnboardingHistorySectionDetailCreatedAt ...
	KeyOnboardingHistorySectionDetailCreatedAt = "ohsDetailCreatedAt"
	// KeyOnboardingHistorySectionDetailStatus ...
	KeyOnboardingHistorySectionDetailStatus = "ohsDetailStatus"
	// KeyOnboardingHistorySectionDetailReason ...
	KeyOnboardingHistorySectionDetailReason = "ohsDetailReason"
	// KeyOnboardingHistorySectionDetailLivenessID ...
	KeyOnboardingHistorySectionDetailLivenessID = "ohsDetailLivenessID"
)

// Beneficiary Owner Details Keys
const (
	// KeyBeneficiaryOwnerDetailsFullName ...
	KeyBeneficiaryOwnerDetailsFullName = "beneficiaryFullName"
	// KeyBeneficiaryOwnerDetailsPlaceOfBirth ...
	KeyBeneficiaryOwnerDetailsPlaceOfBirth = "beneficiaryPlaceOfBirth"
	// KeyBeneficiaryOwnerDetailsDateOfBirth ...
	KeyBeneficiaryOwnerDetailsDateOfBirth = "beneficiaryDateOfBirth"
	// KeyBeneficiaryOwnerDetailsGender ...
	KeyBeneficiaryOwnerDetailsGender = "beneficiaryGender"
	// KeyBeneficiaryOwnerDetailsMaritalStatus ...
	KeyBeneficiaryOwnerDetailsMaritalStatus = "beneficiaryMaritalStatus"
	// KeyBeneficiaryOwnerDetailsSourceOfIncome ...
	KeyBeneficiaryOwnerDetailsSourceOfIncome = "beneficiarySourceOfIncome"
	// KeyBeneficiaryOwnerDetailsOccupation ...
	KeyBeneficiaryOwnerDetailsOccupation = "beneficiaryOccupation"
	// KeyBeneficiaryOwnerDetailsIndustry ...
	KeyBeneficiaryOwnerDetailsIndustry = "beneficiaryIndustry"
	// KeyBeneficiaryOwnerDetailsMonthIncome ...
	KeyBeneficiaryOwnerDetailsMonthIncome = "beneficiaryMonthlyIncome"
	// KeyBeneficiaryOwnerDetailsPosition ...
	KeyBeneficiaryOwnerDetailsPosition = "beneficiaryPosition"
	// KeyBeneficiaryOwnerDetailsDomicile ...
	KeyBeneficiaryOwnerDetailsDomicile = "beneficiaryDomicile"
	// KeyBeneficiaryOwnerDetailsWorkAddress ...
	KeyBeneficiaryOwnerDetailsWorkAddress = "beneficiaryWorkAddress"
	// KeyBeneficiaryOwnerDetailsAverageIncome ...
	KeyBeneficiaryOwnerDetailsAverageIncome = "beneficiaryAverageIncomePerYear"
	// KeyBeneficiaryOwnerDetailsPurposeOfAccount ...
	KeyBeneficiaryOwnerDetailsPurposeOfAccount = "beneficiaryPurposeOfAccount"
	// KeyBeneficiaryOwnerDetailsNik ...
	KeyBeneficiaryOwnerDetailsNik = "beneficiaryNik"
	// KeyBeneficiaryOwnerDetailsNationality ...
	KeyBeneficiaryOwnerDetailsNationality = "beneficiaryNationality"
	// KeyBeneficiaryOwnerDetailsAddress ...
	KeyBeneficiaryOwnerDetailsAddress = "beneficiaryAddress"
	// KeyBeneficiaryOwnerDetailsContactAddress ...
	KeyBeneficiaryOwnerDetailsContactAddress = "beneficiaryContactAddress"
	// KeyBeneficiaryOwnerDetailsPassport ...
	KeyBeneficiaryOwnerDetailsPassport = "beneficiaryPassport"
	// KeyBeneficiaryOwnerDetailsCountry ...
	KeyBeneficiaryOwnerDetailsCountry = "beneficiaryCountry"
)

// Occupation Details Keys
const (
	// KeyOccupationDetailsEmployerName ...
	KeyOccupationDetailsEmployerName = "employerName"
	// KeyOccupationDetailsWorkPhoneNumber ...
	KeyOccupationDetailsWorkPhoneNumber = "workPhoneNumber"
	// KeyOccupationDetailsEmploymentAddress ...
	KeyOccupationDetailsEmploymentAddress = "employmentAddress"
	// KeyOccupationDetailsOccupation ...
	KeyOccupationDetailsOccupation = "occupation"
	// KeyOccupationDetailsJobPosition ...
	KeyOccupationDetailsJobPosition = "jobPosition"
	// KeyOccupationDetailsIndustrySector ...
	KeyOccupationDetailsIndustrySector = "industrySector"
	// KeyOccupationDetailsNPWP ...
	KeyOccupationDetailsNPWP = "NPWP"
)

// Address Details Keys
const (
	// KeyAddressDetailsType ...
	KeyAddressDetailsType = "addressType"
	// KeyAddressDetailsVillage ...
	KeyAddressDetailsVillage = "village"
	// KeyAddressDetailsSubdistrict ...
	KeyAddressDetailsSubdistrict = "subdistrict"
	// KeyAddressDetailsProvince ...
	KeyAddressDetailsProvince = "province"
	// KeyAddressDetailsPostalCode ...
	KeyAddressDetailsPostalCode = "postalCode"
	// KeyAddressDetailsStreet ...
	KeyAddressDetailsStreet = "street"
	// KeyAddressDetailsRW ...
	KeyAddressDetailsRW = "rw"
	// KeyAddressDetailsRT ...
	KeyAddressDetailsRT = "rt"
	// KeyAddressDetailsCity ...
	KeyAddressDetailsCity = "city"
	// KeyAddressDetailsCountry ...
	KeyAddressDetailsCountry = "country"
)

// Video Call Information Keys ...
const (
	// KeyVideoCallInformationTicketID ...
	KeyVideoCallInformationTicketID = "videoCallInformationTicketID"
	// KeyVideoCallInformationCreatedOn ...
	KeyVideoCallInformationCreatedOn = "videoCallInformationCreatedOn"
	// KeyVideoCallInformationURL ...
	KeyVideoCallInformationURL = "videoCallInformationUrl"
	// KeyVideoCallInformationStatus ...
	KeyVideoCallInformationStatus = "videoCallInformationStatus"
	// KeyVideoCallInformationAssessmentRemark ...
	KeyVideoCallInformationAssessmentRemark = "videoCallInformationAssessmentRemark"
	// KeyVideoCallInformationMarkerInfo ...
	KeyVideoCallInformationMarkerInfo = "videoCallInformationMarkerInfo"
	// KeyVideoCallInformationCheckerInfo ...
	KeyVideoCallInformationCheckerInfo = "videoCallInformationCheckerInfo"
	// KeyVideoCallInformationDocument ...
	KeyVideoCallInformationDocument = "videoCallInformationDocument"
)

// Contact Details Keys ...
const (
	// KeyContactDetailsType ...
	KeyContactDetailsType = "contactType"
	// KeyContactDetailsEmail ...
	KeyContactDetailsEmail = "email"
	// KeyContactDetailsPhoneNumber ...
	KeyContactDetailsPhoneNumber = "phoneNumber"
)

// KeyGroups defines the grouping of keys for different processing paths
var KeyGroups = struct {
	CustomerLean       map[string]bool
	CustomerOps        map[string]bool
	AmlData            map[string]bool
	FundingTab         map[string]bool
	AccountList        map[string]bool
	AccountDetail      map[string]bool
	LendingTab         map[string]bool
	TransactionsTab    map[string]bool
	AppActivity        map[string]bool
	Summary            map[string]bool
	LendingTermLoanTab map[string]string
}{
	CustomerLean: map[string]bool{
		KeyCustomerHomepage: true,
		KeyAddressDetails:   true,
		KeyContactDetails:   true,
		KeyFunding:          true,
		KeyLending:          true,
	},
	CustomerOps: map[string]bool{
		KeyCustomerDetails:                      true,
		KeyBeneficiaryOwnerDetails:              true,
		KeyOccupationDetails:                    true,
		KeyOnboardingDetails:                    true,
		KeyOnboardingDetailsVerificationDetails: true,
		KeyChannelAccess:                        true,
		KeyOCRDetails:                           true,
		KeyOnboardingHistorySection:             true,
		KeyVideoCallInformation:                 true,
		KeyCustomerEnhancedDueDiligence:         true,
		KeyCustomerReEnhancedDueDiligence:       true,
	},
	AmlData: map[string]bool{
		KeyCustomerDetails:                true,
		KeyBeneficiaryOwnerDetails:        true,
		KeyCustomerEnhancedDueDiligence:   true,
		KeyCustomerReEnhancedDueDiligence: true,
	},
	FundingTab: map[string]bool{
		KeyListOfFundingAccount:        true,
		KeyMainCasaAndSakuAccount:      true,
		KeyPiggybank:                   true,
		KeyTermDeposit:                 true,
		KeyTermDepositRenewalHistory:   true,
		KeyTermDepositParameterHistory: true,
		KeyTransactionConfiguration:    true,
		KeyCustomerLevelTxLimit:        true,
		KeyBankWideLevelTxLimit:        true,
		KeyExternalLinkages:            true,
		KeyPartnerLinkage:              true,
		KeyLinkingHistory:              true,
		KeyReauthHistory:               true,
	},
	TransactionsTab: map[string]bool{
		KeyCustomerTransactionList: true,
		KeyTransactionList:         true,
		KeyTransactionDetails:      true,
	},
	AppActivity: map[string]bool{
		KeyActivityLog:                    true,
		KeyMfaLog:                         true,
		KeyDeviceLoginInfo:                true,
		KeyFacialMatchingLivenessCheckLog: true,
		KeyInteractionLog:                 true,
		KeyMarketingPreference:            true,
		KeyMarketingPreferenceCurrent:     true,
		KeyMarketingPreferenceLog:         true,
	},
	LendingTab: map[string]bool{
		KeyLendingPAS:                  true,
		KeyActiveLoanListDetails:       true,
		KeyOnboardingLoan:              true,
		KeyEmergencyContact:            true,
		KeyAddressLoan:                 true,
		KeyLocInformation:              true,
		KeyPortfolioReviewLog:          true,
		KeyActiveLoanList:              true,
		KeyLocBlockTable:               true,
		KeyTermLoanPartner:             true,
		KeyTermActiveLoanList:          true,
		KeyInactiveLoanList:            true,
		KeyActiveLoanDetail:            true,
		KeyActiveLoanOnboardingData:    true,
		KeyActiveCustomerInformation:   true,
		KeyActiveAddressDetail:         true,
		KeyActiveEmergencyContact:      true,
		KeyActiveWorkInformation:       true,
		KeyActiveContractList:          true,
		KeyActiveDrawdownAccountInfo:   true,
		KeyActiveDeviceInformation:     true,
		KeyActiveLoanSummary:           true,
		KeyActiveInstallmentDetail:     true,
		KeyActiveRepaymentDetail:       true,
		KeyActiveVaGenerationHistory:   true,
		KeyActiveCreditInformation:     true,
		KeyInactiveLoanDetail:          true,
		KeyInactiveLoanOnboardingData:  true,
		KeyInactiveCustomerInformation: true,
		KeyInactiveAddressDetail:       true,
		KeyInactiveEmergencyContact:    true,
		KeyInactiveWorkInformation:     true,
		KeyInactiveContractList:        true,
		KeyInactiveDrawdownAccountInfo: true,
		KeyInactiveDeviceInformation:   true,
		KeyInactiveLoanSummary:         true,
		KeyInactiveInstallmentDetail:   true,
		KeyInactiveRepaymentDetail:     true,
		KeyInactiveVaGenerationHistory: true,
		KeyInactiveCreditInformation:   true,
	},
	Summary: map[string]bool{
		KeyCustomerSummary: true,
	},
	LendingTermLoanTab: map[string]string{
		KeyActiveLoanDetail:            KeyActiveLoanDetail,
		KeyActiveLoanOnboardingData:    KeyActiveLoanOnboardingData,
		KeyActiveCustomerInformation:   KeyActiveCustomerInformation,
		KeyActiveAddressDetail:         KeyActiveAddressDetail,
		KeyActiveEmergencyContact:      KeyActiveEmergencyContact,
		KeyActiveWorkInformation:       KeyActiveWorkInformation,
		KeyActiveContractList:          KeyActiveContractList,
		KeyActiveDrawdownAccountInfo:   KeyActiveDrawdownAccountInfo,
		KeyActiveDeviceInformation:     KeyActiveDeviceInformation,
		KeyActiveLoanOverview:          KeyActiveLoanOverview,
		KeyActiveLoanSummary:           KeyActiveLoanSummary,
		KeyActiveInstallmentDetail:     KeyActiveInstallmentDetail,
		KeyActiveRepaymentDetail:       KeyActiveRepaymentDetail,
		KeyActiveVaGenerationHistory:   KeyActiveVaGenerationHistory,
		KeyActiveCreditInformation:     KeyActiveCreditInformation,
		KeyInactiveLoanDetail:          KeyInactiveLoanDetail,
		KeyInactiveLoanOnboardingData:  KeyInactiveLoanOnboardingData,
		KeyInactiveCustomerInformation: KeyInactiveCustomerInformation,
		KeyInactiveAddressDetail:       KeyInactiveAddressDetail,
		KeyInactiveEmergencyContact:    KeyInactiveEmergencyContact,
		KeyInactiveWorkInformation:     KeyInactiveWorkInformation,
		KeyInactiveContractList:        KeyInactiveContractList,
		KeyInactiveDrawdownAccountInfo: KeyInactiveDrawdownAccountInfo,
		KeyInactiveDeviceInformation:   KeyInactiveDeviceInformation,
		KeyInactiveLoanOverview:        KeyInactiveLoanOverview,
		KeyInactiveLoanSummary:         KeyInactiveLoanSummary,
		KeyInactiveInstallmentDetail:   KeyInactiveInstallmentDetail,
		KeyInactiveRepaymentDetail:     KeyInactiveRepaymentDetail,
		KeyInactiveVaGenerationHistory: KeyInactiveVaGenerationHistory,
		KeyInactiveCreditInformation:   KeyInactiveCreditInformation,
	},
}

// FieldMappings ...
var FieldMappings = map[string]map[string]string{
	KeyAddressDetails: {
		"addressType": KeyAddressDetailsType,
		"city":        KeyAddressDetailsCity,
		"country":     KeyAddressDetailsCountry,
		"postalCode":  KeyAddressDetailsPostalCode,
		"province":    KeyAddressDetailsProvince,
		"rt":          KeyAddressDetailsRT,
		"rw":          KeyAddressDetailsRW,
		"street":      KeyAddressDetailsStreet,
		"subdistrict": KeyAddressDetailsSubdistrict,
		"village":     KeyAddressDetailsVillage,
	},
	KeyContactDetails: {
		"contactType": KeyContactDetailsType,
		"email":       KeyContactDetailsEmail,
		"phoneNumber": KeyContactDetailsPhoneNumber,
	},
}

const (
	// ProductVariantMainCasa ...
	ProductVariantMainCasa = "casa_account_default"
	// ProductVariantPocketCasa ...
	ProductVariantPocketCasa = "casa_pocket_default"
	// ProductVariantTermDeposit ...
	ProductVariantTermDeposit = "term_deposit_default"
	// ProductVariantPiggybank ...
	ProductVariantPiggybank = "microsaver"
	// ProductVariantOvoNabung ...
	ProductVariantOvoNabung = "pocket_ovo"
	// ProductVariantLineOfCredit ...
	ProductVariantLineOfCredit = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT" // nolint:gosec
	// ProductVariantLoanAccount ...
	ProductVariantLoanAccount = "DEFAULT_FLEXI_LOAN_TERM_LOAN"
)

// ProductVariantMappingBySubTab ...
var ProductVariantMappingBySubTab = map[string][]string{
	KeyMainCasaAndSakuAccount: {
		ProductVariantMainCasa,
		ProductVariantPocketCasa,
		ProductVariantOvoNabung,
	},
	KeyPiggybank: {
		ProductVariantPiggybank,
	},
	KeyTermDeposit: {
		ProductVariantTermDeposit,
	},
	KeyLendingPAS: {
		ProductVariantLineOfCredit,
		ProductVariantLoanAccount,
	},
	KeyMainCasa: {
		ProductVariantMainCasa,
	},
	KeySaku: {
		ProductVariantPocketCasa,
	},
	KeyOVONabung: {
		ProductVariantOvoNabung,
	},
}

// Main Account and Pocket Field Keys
const (
	// KeyMainCasaAccountID ...
	KeyMainCasaAccountID = "accountID"
	// KeyMainCasaAccountType ...
	KeyMainCasaAccountType = "accountType"
	// KeyMainCasaAccountStatus ...
	KeyMainCasaAccountStatus = "accountStatus"
	// KeyMainCasaAccountName ...
	KeyMainCasaAccountName = "accountName"
	// KeyMainCasaBlockHoldcodes ...
	KeyMainCasaBlockHoldcodes = "blockHoldcodes"
	// KeyMainCasaMakerName ...
	KeyMainCasaMakerName = "makerName"
	// KeyMainCasaMakerJustification ...
	KeyMainCasaMakerJustification = "makerJustification"
	// KeyMainCasaLastUpdated ...
	KeyMainCasaLastUpdated = "lastUpdated"
	// KeyMainCasaCurrency ...
	KeyMainCasaCurrency = "currency"
	// KeyMainCasaAvailableBalance ...
	KeyMainCasaAvailableBalance = "availableBalance"
	// KeyMainCasaBaseInterestRate ...
	KeyMainCasaBaseInterestRate = "baseInterestRate"
	// KeyMainCasaTotalInterestRate ...
	KeyMainCasaTotalInterestRate = "totalInterestRate"
	// KeyMainCasaRateSpreadInterestRate ...
	KeyMainCasaRateSpreadInterestRate = "rateSpreadInterestRate"
)

// Piggy bank field
const (
	// KeyPiggybankAccountID ...
	KeyPiggybankAccountID = "piggyAccountID"
	// KeyPiggybankInterestRate ...
	KeyPiggybankInterestRate = "piggyInterestRate"
	// KeyPiggybankBalanceWithInterest ...
	KeyPiggybankBalanceWithInterest = "balanceWithInterest"
	// KeyPiggybankOpeningTime ...
	KeyPiggybankOpeningTime = "openingTime"
	// KeyPiggybankBreakDate ...
	KeyPiggybankBreakDate = "breakDate"
	// KeyPiggybankNearestDeductionAmount ...
	KeyPiggybankNearestDeductionAmount = "nearestDeductionAmount"
	// KeyPiggybankStatus ...
	KeyPiggybankStatus = "piggyStatus"
	// KeyPiggybankLastSuccessDeduction ...
	KeyPiggybankLastSuccessDeduction = "piggyLastSuccessDeduction"
	// KeyPiggybankSourceOfFund ...
	KeyPiggybankSourceOfFund = "piggySourceOfFund"
	// KeyPiggybankLevel ...
	KeyPiggybankLevel = "piggyLevel"
	// KeyPiggybankBalance ...
	KeyPiggybankBalance = "piggyBalance"
	// KeyPiggybankNearestDeductionSettingDate ...
	KeyPiggybankNearestDeductionSettingDate = "nearestDeductSettingDate"
	// KeyPiggybankGoalAmount ...
	KeyPiggybankGoalAmount = "piggyGoalAmount"
	// KeyPiggybankAutoIsi ...
	KeyPiggybankAutoIsi = "autoIsi"
	// KeyPiggybankAutoIsiSettingDate ...
	KeyPiggybankAutoIsiSettingDate = "autoIsiSettingDate"
	// KeyPiggybankClosureType ...
	KeyPiggybankClosureType = "piggyClosureType"
	// KeyPiggybankNearestDeductionHistory ...
	KeyPiggybankNearestDeductionHistory = "nearestDeductionHistory"
)

// Term Deposit Field
const (
	// KeyTermDepositAccountNumber ...
	KeyTermDepositAccountNumber = "tdAccountID"
	// KeyTermDepositPrincipalAmount ...
	KeyTermDepositPrincipalAmount = "principalAmount"
	// KeyTermDepositBalanceWithEstimateInterest ...
	KeyTermDepositBalanceWithEstimateInterest = "balanceWithEstimateInterest"
	// KeyTermDepositEstimatedEarnedInterest ...
	KeyTermDepositEstimatedEarnedInterest = "estimatedEarnedInterest"
	// KeyTermDepositEstimatedTaxDeduction ...
	KeyTermDepositEstimatedTaxDeduction = "estimatedTaxDeduction"
	// KeyTermDepositSourceOfFund ...
	KeyTermDepositSourceOfFund = "tdSourceOfFund"
	// KeyTermDepositStatus ...
	KeyTermDepositStatus = "tdStatus"
	// KeyTermDepositInterestRate ...
	KeyTermDepositInterestRate = "tdInterestRate"
	// KeyTermDepositInterestRateType ...
	KeyTermDepositInterestRateType = "interestRateType"
	// KeyTermDepositAroMechanism ...
	KeyTermDepositAroMechanism = "aroMechanism"
	// KeyTermDepositEarliestManualBreakDate ...
	KeyTermDepositEarliestManualBreakDate = "earliestManualBreakDate"
	// KeyTermDepositClosureType ...
	KeyTermDepositClosureType = "tdClosureType"
	// KeyTermDepositIssuanceDate ...
	KeyTermDepositIssuanceDate = "tdIssuanceDate"
	// KeyTermDepositMaturityDate ...
	KeyTermDepositMaturityDate = "tdMaturityDate"
	// KeyTermDepositPlacementTenure ...
	KeyTermDepositPlacementTenure = "placementTenure"
	// KeyTermDepositPlacementTenureType ...
	KeyTermDepositPlacementTenureType = "placementTenureType"
	// KeyTermDepositBreakTimestamp ...
	KeyTermDepositBreakTimestamp = "tdBreakTimestamp"
	// KeyTermDepositParameterHistory ...
	KeyTermDepositParameterHistory = "depositParameterHistory"
	// KeyTermDepositRenewalHistory ...
	KeyTermDepositRenewalHistory = "renewalHistory"
)

// MappingWordingAccountType ...
var MappingWordingAccountType = map[string]string{
	ProductVariantMainCasa:     "Saving Account",
	ProductVariantPocketCasa:   "Pocket Account",
	ProductVariantOvoNabung:    "OVO Nabung",
	ProductVariantPiggybank:    "Celengan",
	ProductVariantTermDeposit:  "Term Deposit",
	ProductVariantLoanAccount:  "Loan Account",
	ProductVariantLineOfCredit: "Line Of Credit",
}

// TermDepositParameterHistory field
const (
	// KeyDepositParamTimestamp ...
	KeyDepositParamTimestamp = "instructionTimestamp"
	// KeyDepositParamMaturityBefore ...
	KeyDepositParamMaturityBefore = "maturityInstructionBefore"
	// KeyDepositParamMaturityAfter ...
	KeyDepositParamMaturityAfter = "maturityInstructionAfter"
	// KeyDepositParamUpdatedBy ...
	KeyDepositParamUpdatedBy = "instructionUpdatedBy"
)

// TermDepositRenewalHistory field
const (
	// KeyRenewalHistoryTimestamp ...
	KeyRenewalHistoryTimestamp = "renewTimestamp"
	// KeyRenewalHistoryAmount ...
	KeyRenewalHistoryAmount = "renewAmount"
	// KeyRenewalHistoryMaturityDate ...
	KeyRenewalHistoryMaturityDate = "renewMaturityDate"
	// KeyRenewalHistoryApplicableInterest ...
	KeyRenewalHistoryApplicableInterest = "applicableInterest"
)

const (
	// TermDepositParameterKeyMaturityInstruction ...
	TermDepositParameterKeyMaturityInstruction = "tdMaturityInstructionType"

	// PiggybankGoalAmountParameterKey ...
	PiggybankGoalAmountParameterKey = "hard_deposit_cap_limit"
)

// MappingPiggybankBooster ...
var MappingPiggybankBooster = map[string]string{
	"true":  "YES",
	"false": "NO",
}

// MappingKeyGroupDataPoint ...
var MappingKeyGroupDataPoint = struct {
	Funding map[string]bool
}{
	Funding: map[string]bool{
		KeyPiggybankGoalAmount:           true,
		KeyPiggybankLastSuccessDeduction: true,
	},
}

const (
	// DataTypeSafeID ...
	DataTypeSafeID string = "safeID"
	// DataTypeCif ...
	DataTypeCif string = "cif"

	// KeyPagination ...
	KeyPagination string = "pagination"

	// KeyMeta ...
	KeyMeta string = "meta"
)

// MappingGlobalDataCustomerLeanKey ...
var MappingGlobalDataCustomerLeanKey = map[string]string{
	DataTypeSafeID: "ID",
	DataTypeCif:    "publicID",
}

// MappingExpiryTimeForDataPoint ...
var MappingExpiryTimeForDataPoint = map[string]int64{
	KeyPiggybankGoalAmount:           300,
	KeyEcosystemPartnerLinkedAccount: 300,
	KeyEcosystemOnboardingChannel:    600,
}

const (
	// RedisFirstKeyCustomerDataPoint ...
	RedisFirstKeyCustomerDataPoint = "cust_data_point"

	// LimitStatusActive ...
	LimitStatusActive = "ACTIVE"
)

// fetch transfer limit names list ...
const (
	// FetchTransferLimitNameBIFast ...
	FetchTransferLimitNameBIFast = "RET_INTER_BIFAST"
)

// MappingTxLimitNameAndFrequency ...
var MappingTxLimitNameAndFrequency = map[string]string{
	"GRAB_ECOSYSTEM_LIMIT": "Grab",
	"RET_INTRA_MOB":        "Intrabank",
	"RET_INTER_BIFAST":     "BI-FAST",
	"RET_INTER_RTOL":       "RTOL",
	"DAILY_CALENDAR":       "Daily",
	"ONCE":                 "per Transaction",
}

// CustomerTxnLimitHistory field ...
const (
	// KeyCustLimitTimestamp ...
	KeyCustLimitTimestamp = "custLimitTimestamp"
	// KeyCustLimitType ...
	KeyCustLimitType = "custLimitType"
	// KeyCustLimitFrequency ...
	KeyCustLimitFrequency = "custLimitFrequency"
	// KeyCustLimitMinAmount ...
	KeyCustLimitMinAmount = "custLimitMinAmount"
	// KeyCustLimitMaxAmount ...
	KeyCustLimitMaxAmount = "custLimitMaxAmount"
	// KeyCustLimitMinAllowed ...
	KeyCustLimitMinAllowed = "custLimitMinAllowed"
	// KeyCustLimitMaxAllowed ...
	KeyCustLimitMaxAllowed = "custLimitMaxAllowed"
	// KeyCustCumulativeAmount ...
	KeyCustCumulativeAmount = "custLimitCumulativeAmount"
)

// BankTxnLimitHistory field ...
const (
	// KeyBankLimitTimestamp ...
	KeyBankLimitTimestamp = "bankLimitTimestamp"
	// KeyBankLimitType ...
	KeyBankLimitType = "bankLimitType"
	// KeyBankLimitFrequency ...
	KeyBankLimitFrequency = "bankLimitFrequency"
	// KeyBankLimitMinAmount ...
	KeyBankLimitMinAmount = "bankLimitMinAmount"
	// KeyBankLimitMaxAmount ...
	KeyBankLimitMaxAmount = "bankLimitMaxAmount"
	// KeyBankLimitMinAllowed ...
	KeyBankLimitMinAllowed = "bankLimitMinAllowed"
	// KeyBankLimitMaxAllowed ...
	KeyBankLimitMaxAllowed = "bankLimitMaxAllowed"
	// KeyBankLimitStatus ...
	KeyBankLimitStatus = "bankLimitStatus"
)

// CustomerJournalLogType ...
const (
	// CustomerJournalLogTypeLogin ...
	CustomerJournalLogTypeLogin = "login"
	// CustomerJournalLogTypeInteraction ...
	CustomerJournalLogTypeInteraction = "interaction"
	// CustomerJournalLogTypeActivity ...
	CustomerJournalLogTypeActivity = "activity"
	// CustomerJournalLogTypeFacialAndLiveness ...
	CustomerJournalLogTypeFacialAndLiveness = "facialAndLiveness"
	// CustomerJournalLogTypeMFA ...
	CustomerJournalLogTypeMFA = "mfa"
	// CustomerJournalLogTypeEcosystem ...
	CustomerJournalLogTypeEcosystem = "linkage"
	// CustomerJournalLogTypeTransactionLimit ...
	CustomerJournalLogTypeTransactionLimit = "transactionLimitChangesHistory"
	// CustomerJournalLogTypeMarketingPreference ...
	CustomerJournalLogTypeMarketingPreference = "marketingNotificationPreference"
	// CustomerJournalLogTypeOvoNabung ...
	CustomerJournalLogTypeOvoNabung = "ovo-nabung-event"
	// CustomerJournalLogTypeResetFMLC ...
	CustomerJournalLogTypeResetFMLC = "reset-fmlc"
	// CustomerJournalLogTypeQCFlag
	CustomerJournalLogTypeQCFlag = "qc-flag"
)

// CustomerAmlServiceDataType ...
const (
	// CustomerAmlServiceDataTypeNameScreening ...
	CustomerAmlServiceDataTypeNameScreening = "nameScreening"
	// CustomerAmlServiceDataTypeRiskRating ...
	CustomerAmlServiceDataTypeRiskRating = "riskRating"
	// CustomerAmlServiceDataTypeBo ...
	CustomerAmlServiceDataTypeBo = "bo"
	// CustomerAmlServiceDataTypeEcdd ...
	CustomerAmlServiceDataTypeEcdd = "ecdd"
	// CustomerAmlServiceDataTypeRecdd ...
	CustomerAmlServiceDataTypeRecdd = "recdd"
	// CustomerAmlServiceDataTypeCustomerFlag ...
	CustomerAmlServiceDataTypeCustomerFlag = "customerFlag"
)

// CustomerAmlServiceQuestion ...
const (
	CustomerAmlServiceSourceOfIncome                  = "sourceOfIncome"
	CustomerAmlServiceAlternativeYearlyAmountOfIncome = "alternativeYearlyAmountOfIncome"
	CustomerAmlServiceCompanyName                     = "companyName"
	CustomerAmlServiceAlternativeSourceOfIncome       = "alternativeSourceOfIncome"
	CustomerAmlServiceMonthlyAmountOfIncome           = "monthlyAmountOfIncome"
	CustomerAmlServiceMonthlyNumberOfTransaction      = "monthlyNumberOfTransaction"
	CustomerAmlServiceAuthorityFigure                 = "authorityFigure"
	CustomerAmlServiceWealthOwnership                 = "wealthOwnership"
	CustomerAmlServiceRelationshipType                = "relationshipType"
	CustomerAmlServiceAlternativeIncome               = "alternativeIncome"
)

// PartnerLinkageField ..
const (
	// KeyEcosystemPartnerName ...
	KeyEcosystemPartnerName = "partnerName"
	// KeyEcosystemPartnerID ...
	KeyEcosystemPartnerID = "partnerID"
	// KeyEcosystemPartnerUserID ...
	KeyEcosystemPartnerUserID = "partnerUserID"
	// KeyEcosystemPartnerDailyTxLimitSet ...
	KeyEcosystemPartnerDailyTxLimitSet = "partnerDailyTxLimitSet"
	// KeyEcosystemPartnerDailyTxLimitUsed ...
	KeyEcosystemPartnerDailyTxLimitUsed = "partnerDailyTxLimitUsed"
	// KeyEcosystemPartnerStatus ...
	KeyEcosystemPartnerStatus = "partnerStatus"
	// KeyEcosystemPartnerLinkedAccount ...
	KeyEcosystemPartnerLinkedAccount = "partnerLinkedAccount"
	// KeyEcosystemPartnerUnlinkReason ...
	KeyEcosystemPartnerUnlinkReason = "partnerUnlinkReason"
	// KeyEcosystemPartnerStatusNotes ...
	KeyEcosystemPartnerStatusNotes = "partnerStatusNotes"
	// KeyEcosystemOnboardingChannel ...
	KeyEcosystemOnboardingChannel = "partnerOnboardingChannel"
)

// BillingAgreementStatusNotesMap ...
var BillingAgreementStatusNotesMap = map[string]string{
	"ACTIVE":     "Linked",
	"TERMINATED": "Unlinked",
	"PENDING":    "In Progress Linking",
	"CONFIRMED":  "In Progress Linking",
	"REJECTED":   "Failed Linking",
}

const (
	// DefaultPaginationSize ...
	DefaultPaginationSize = 10
	// DefaultPaginationAppActivitySize ...
	DefaultPaginationAppActivitySize = 5
)

// LinkingHistoryField ...
const (
	// KeyLinkingHistoryTimestamp ...
	KeyLinkingHistoryTimestamp = "linkingTimestamp"
	// KeyLinkingHistoryPreviousLimit ...
	KeyLinkingHistoryPreviousLimit = "linkingPreviousLimit"
	// KeyLinkingHistoryUpdatedLimit ...
	KeyLinkingHistoryUpdatedLimit = "linkingUpdatedLimit"
	// KeyLinkingHistoryAccountID ...
	KeyLinkingHistoryAccountID = "linkingAccountID"
	// KeyLinkingHistoryAction ...
	KeyLinkingHistoryAction = "linkingAction"
	// KeyLinkingHistoryUnlinkReason ...
	KeyLinkingHistoryUnlinkReason = "linkingReason"
	// KeyLinkingHistoryActionBy ...
	KeyLinkingHistoryActionBy = "linkingActionBy"
	// KeyLinkingHistoryActionSystem ...
	KeyLinkingHistoryActionSystem = "linkingActionSystem"
	// KeyLinkingHistoryActionStatus ...
	KeyLinkingHistoryActionStatus = "linkingActionStatus"
	// KeyLinkingHistoryPartnerName ...
	KeyLinkingHistoryPartnerName = "linkingPartnerName"
	// KeyLinkingHistoryDeviceID ...
	KeyLinkingHistoryDeviceID = "linkingDeviceID"
	// KeyLinkingHistoryDeviceBrand ...
	KeyLinkingHistoryDeviceBrand = "linkingDeviceBrand"
	// KeyLinkingHistoryDeviceModel ...
	KeyLinkingHistoryDeviceModel = "linkingDeviceModel"
	// KeyLinkingHistoryOSName ...
	KeyLinkingHistoryOSName = "linkingOSName"
	// KeyLinkingHistoryOSVersion ...
	KeyLinkingHistoryOSVersion = "linkingOSVersion"
	// KeyLinkingErrorMessage ...
	KeyLinkingErrorMessage = "linkingErrorMessage"
)

// TableDataWithPagination ...
var TableDataWithPagination = map[string]bool{
	KeyLinkingHistory:                 true,
	KeyReauthHistory:                  true,
	KeyActivityLog:                    true,
	KeyMfaLog:                         true,
	KeyDeviceLoginInfo:                true,
	KeyFacialMatchingLivenessCheckLog: true,
	KeyInteractionLog:                 true,
	KeyMarketingPreferenceLog:         true,
	KeyTransactionList:                true,
	KeyCustomerEnhancedDueDiligence:   true,
}

// CustomDataSection ...
var CustomDataSection = map[string]bool{
	KeyCustomerEnhancedDueDiligence: true,
}

// Reauthentication history field ...
const (
	// KeyReauthHistoryTimestamp ...
	KeyReauthHistoryTimestamp = "reauthTimestamp"
	// KeyReauthHistoryPrevMobileNumber ...
	KeyReauthHistoryPrevMobileNumber = "reauthPrevMobileNumber"
	// KeyReauthHistoryNewMobileNumber ...
	KeyReauthHistoryNewMobileNumber = "reauthNewMobileNumber"
	// KeyReauthHistoryStatus ...
	KeyReauthHistoryStatus = "reauthStatus"
	// KeyReauthHistoryStatusReason ...
	KeyReauthHistoryStatusReason = "reauthStatusReason"
)

const (
	// PartnerNameGrab ...
	PartnerNameGrab = "Grab"
	// PartnerNameOvo ...
	PartnerNameOvo = "OVO"
	// AccountStatusClosed ...
	AccountStatusClosed = "CLOSED"
)

const (
	// ISO8601Layout ...
	ISO8601Layout = "2006-01-02T15:04:05Z"

	// OnedashTimeLayout ...
	OnedashTimeLayout = "02 Jan 2006 15:04:05"
)

// Channel Access Keys
const (
	// KeyChannelAccessCustomerAccessStatus ...
	KeyChannelAccessCustomerAccessStatus = "customerAccessStatus"
	// KeyChannelAccessAccessStatusMaker ...
	KeyChannelAccessAccessStatusMaker = "accessStatusMaker"
	// KeyChannelAccessReasonJustification ...
	KeyChannelAccessReasonJustification = "reasonJustification"
)

// Transaction List Fields ...
const (
	// KeyTransactionID is the batch transaction ID on the transaction history/list
	KeyTransactionID = "transactionID"
	// KeyTransactionBatchID is the batch transaction ID on the transaction history/list
	KeyTransactionBatchID = "transactionBatchID"
	// KeyPreTransactionID is unique identifier for griffin ID
	KeyPreTransactionID = "preTransactionID"
	// KeyTransactionDescription is the description of the transaction
	KeyTransactionDescription = "transactionDescription"
	// KeyTransactionType is the type of transaction e.g, send money, receive money, etc
	KeyTransactionType = "transactionType"
	// KeyTransactionSubType is the sub type of transaction, more on the counterparty e.g. RTOL, OVO, etc
	KeyTransactionSubType = "transactionSubType"
	// KeyCreationDate is the date when the transaction was created
	KeyCreationDate = "creationDate"
	// KeyPostingDate is the date when the transaction was posted to TM
	KeyPostingDate = "postingDate"
	// KeyTransactionAmount is the amount of the transaction
	KeyTransactionAmount = "transactionAmount"
	// KeyTransactionStatus is the status of the transaction
	KeyTransactionStatus = "transactionStatus"
	// KeyCounterpartyName is the name of the counterparty
	KeyCounterpartyName = "counterpartyName"
	// KeyCounterpartyAccountName is the account name of the counterparty
	KeyCounterpartyAccountName = "counterpartyAccountName"
	// KeyCounterpartyAccountID is the account ID of the counterparty
	KeyCounterpartyAccountID = "counterpartyAccountID"
	// KeyEndingBalance is the ending balance after the transaction
	KeyEndingBalance = "endingBalance"

	// KeyPurposeOfTransaction is the purpose of the transaction
	KeyPurposeOfTransaction = "purposeOfTransaction"
	// KeyPrincipleAmount is the principle amount of the transaction
	KeyPrincipleAmount = "principleAmount"
	// KeyTotalDiscountAmount is the total discount amount of the transaction
	KeyTotalDiscountAmount = "totalDiscountAmount"
	// KeyErrorDescription is the error description of the transaction
	KeyErrorDescription = "errorDescription"
	// KeyCPAN is customer payment account number
	KeyCPAN = "cpan"
	// KeyMerchantLocation is the location of the merchant
	KeyMerchantLocation = "merchantLocation"
	// KeyTerminalID is the ID of the terminal
	KeyTerminalID = "terminalID"
	// KeyQRISType is the type of QRIS
	KeyQRISType = "qrisType"
	// KeyRemarks provides additional information about the transaction submitted by the user
	KeyRemarks = "remarks"
	// KeyPartnerReferenceID is the reference ID from the partner system
	KeyPartnerReferenceID = "partnerReferenceID"
	// KeyClientTransactionID is the transaction ID from the client system
	KeyClientTransactionID = "clientTransactionID"
	// KeyExternalID is the external ID of the transaction
	KeyExternalID = "externalID"
	// KeyOriginalReferenceID is the original transaction ID of the transaction
	KeyOriginalReferenceID = "originalReferenceID"
	// KeyOriginalExternalID is the original external ID of the transaction, this used on refund transaction
	KeyOriginalExternalID = "originalExternalID"
	// KeyActivityType is the type of activity
	KeyActivityType = "activityType"
)

// ActivityLogField ...
const (
	// KeyActivityLogTriggeredDate ...
	KeyActivityLogTriggeredDate = "activityLogTriggeredDate"
	// KeyActivityLogEvent ...
	KeyActivityLogEvent = "activityLogEvent"
	// KeyActivityLogActivity ...
	KeyActivityLogActivity = "activityLogActivity"
	// KeyActivityLogBeforeValue ...
	KeyActivityLogBeforeValue = "activityLogBeforeValue"
	// KeyActivityLogAfterValue ...
	KeyActivityLogAfterValue = "activityLogAfterValue"
	// KeyActivityLogTrigger ...
	KeyActivityLogTrigger = "activityLogTrigger"
	// KeyActivityLogStatus ...
	KeyActivityLogStatus = "activityLogStatus"
	// KeyActivityLogFailedReason ...
	KeyActivityLogFailedReason = "activityLogFailedReason"
)

// MFALogField ...
const (
	// KeyMfaLogTimestamp ...
	KeyMfaLogTimestamp = "mfaLogTimestamp"
	// KeyMfaLogMfaType ...
	KeyMfaLogMfaType = "mfaLogMfaType"
	// KeyMfaLogStatus ...
	KeyMfaLogStatus = "mfaLogStatus"
	// KeyMfaLogCooldownPeriod ...
	KeyMfaLogCooldownPeriod = "mfaLogCooldownPeriod"
	// KeyMfaLogFmlcFailedReason ...
	KeyMfaLogFmlcFailedReason = "mfaLogFmlcFailedReason"
	// KeyMfaLogFmlcTransactionID ...
	KeyMfaLogFmlcTransactionID = "mfaLogFmlcTransactionId"
	// KeyMfaLogFmlcMatchScore ...
	KeyMfaLogFmlcMatchScore = "mfaLogFmlcMatchScore"
	// KeyMfaLogControlTriggered ...
	KeyMfaLogControlTriggered = "mfaLogControlTriggered"
	// KeyMfaLogSelfieImage ...
	KeyMfaLogSelfieImage = "mfaLogSelfieImage"
	// KeyMfaLogBaseSelfie ...
	KeyMfaLogBaseSelfie = "mfaLogBaseSelfie"
)

// MFA Type ...
const (
	// MFATypeFMLC ...
	MFATypeFMLC = "FMLC"
	// MFATypePIN ...
	MFATypePIN = "PIN"
	// MFATypeOTP ...
	MFATypeOTP = "OTP"
	// MFATypeOTPRequest ...
	MFATypeOTPRequest = "OTP Request"
	// MFATypeOTPValidation ...
	MFATypeOTPValidation = "OTP Validation"
	// MFATypeBiometric ...
	MFATypeBiometric = "BIOMETRIC"
)

// MFA Trigger Path ...
const (
	// MFATriggerLogin ...
	MFATriggerLogin = "login"
	// MFATriggerPhone ...
	MFATriggerPhone = "phone"
	// MFATriggerPinReset ...
	MFATriggerPinReset = "pin-reset"
	// MFATriggerOnboarding ...
	MFATriggerOnboarding = "onboarding"
	// MFATriggerTransfers ...
	MFATriggerTransfers = "transfers"
	// MFATriggerEmail ...
	MFATriggerEmail = "email"
	// MFATriggerPin ...
	MFATriggerPin = "pin"
	// MFATriggerEnrollMFA ...
	MFATriggerEnrollMFA = "enroll_mfa"
	// MFATriggerPhoneNumber ...
	MFATriggerPhoneNumber = "phonenumber"
	// MFATriggerProof ...
	MFATriggerProof = "proof"
	// MFATriggerResendOTP ...
	MFATriggerResendOTP = "resend-otp"
	// MFATriggerVerifyLogin ...
	MFATriggerVerifyLogin = "verify-login"
	// MFATriggerDrawdown
	MFATriggerDrawdown = "drawdown"
	// MFATriggerPrompt
	MFATriggerPrompt = "prompt"
)

// MFA Description ...
const (
	// MFADescFMLCLogin ...
	MFADescFMLCLogin = "FMLC - Login"
	// MFADescFMLCUpdatePhone ...
	MFADescFMLCUpdatePhone = "FMLC - Update Phone Number"
	// MFADescFMLCResetPIN ...
	MFADescFMLCResetPIN = "FMLC - Reset PIN"
	// MFADescFMLCOnboarding ...
	MFADescFMLCOnboarding = "FMLC - Onboarding flow"
	// MFADescFMLCPayment ...
	MFADescFMLCPayment = "FMLC - Payment flow"
	// MFADescFMLCDrawdown
	MFADescFMLCDrawdown = "FMLC - Drawdown"
	// MFADescPINLogin ...
	MFADescPINLogin = "PIN - Login"
	// MFADescPINUpdateEmail ...
	MFADescPINUpdateEmail = "PIN - Update Email"
	// MFADescPINUpdatePhone ...
	MFADescPINUpdatePhone = "PIN - Update Phone Number"
	// MFADescPINChangePIN ...
	MFADescPINChangePIN = "PIN - Change PIN"
	// MFADescPINPayment ...
	MFADescPINPayment = "PIN - Payment Flow"
	// MFADescPINEnrollBiometric ...
	MFADescPINEnrollBiometric = "PIN - Enroll Biometric"
	// MFADescPINDrawdown
	MFADescPINDrawdown = "PIN - Drawdown"
	// MFADescOTPPrompt
	MFADescOTPPrompt = "OTP - Prompt"
	// MFADescOTPEmail
	MFADescOTPEmail = "OTP - Email"
	// MFADescOTPPhoneNumber
	MFADescOTPPhoneNumber = "OTP - Phonenumber"
	// MFADescOTPRequestEmail ...
	MFADescOTPRequestEmail = "OTP Request - Email"
	// MFADescOTPRequestSMS ...
	MFADescOTPRequestSMS = "OTP Request - SMS"
	// MFADescOTPRequestResetPIN ...
	MFADescOTPRequestResetPIN = "OTP Request - Reset PIN"
	// MFADescOTPRequestResetLogin ...
	MFADescOTPRequestResetLogin = "OTP Request - Reset Login"
	// MFADescOTPRequestResend ...
	MFADescOTPRequestResend = "OTP Request - Resend"
	// MFADescOTPRequestPrompt
	MFADescOTPRequestPrompt = "OTP Request - Prompt"
	// MFADescOTPValidationEmail ...
	MFADescOTPValidationEmail = "OTP Validation - Email"
	// MFADescOTPValidationSMS ...
	MFADescOTPValidationSMS = "OTP Validation - SMS"
	// MFADescOTPValidationResetPIN ...
	MFADescOTPValidationResetPIN = "OTP Validation - Reset PIN"
	// MFADescOTPValidationResetLogin ...
	MFADescOTPValidationResetLogin = "OTP Validation - Reset Login"
	// MFADescBiometricLogin ...
	MFADescBiometricLogin = "Biometric - Login"
)

// DeviceLoginSourceDescriptionKey ...
const (
	// DeviceLoginSourceKeyNative
	DeviceLoginSourceKeyNative = "native"
	// DeviceLoginSourceKeyEcosystemGrab
	DeviceLoginSourceKeyEcosystemGrab = "ecosystem-grab"
	// DeviceLoginSourceKeyEcosystemOvo
	DeviceLoginSourceKeyEcosystemOvo = "ecosystem-ovo"
)

// DeviceLoginSourceDescriptionValue ...
const (
	// DeviceLoginSourceValueNative
	DeviceLoginSourceValueNative = "Superbank"
	// DeviceLoginSourceValueEcosystemGrab
	DeviceLoginSourceValueEcosystemGrab = "Grab"
	// DeviceLoginSourceValueEcosystemOvo
	DeviceLoginSourceValueEcosystemOvo = "OVO"
)

// KeyFacialMatchingLivenessCheckLogField ...
const (
	// KeyFacialMatchingTriggeredDate ...
	KeyFacialMatchingTriggeredDate = "facialMatchingTriggeredDate"
	// KeyFacialMatchingTrigger ...
	KeyFacialMatchingTrigger = "facialMatchingTrigger"
	// KeyFacialMatchingType ...
	KeyFacialMatchingType = "facialMatchingType"
	// KeyFacialMatchingStatus ...
	KeyFacialMatchingStatus = "facialMatchingStatus"
	// KeyFacialMatchingFailedReason ...
	KeyFacialMatchingFailedReason = "facialMatchingFailedReason"
)

// Facial Matching Trigger Path ...
const (
	// FacialMatchingTriggerLogin
	FacialMatchingTriggerLogin = "login"
	// FacialMatchingTriggerPhone
	FacialMatchingTriggerPhone = "phone"
	// FacialMatchingTriggerPinReset
	FacialMatchingTriggerPinReset = "pin-reset"
	// FacialMatchingTriggerOnboarding
	FacialMatchingTriggerOnboarding = "onboarding"
	// FacialMatchingTriggerTransfers
	FacialMatchingTriggerTransfers = "transfers"
)

// Facial Matching Trigger Description ...
const (
	// FacialMatchingDescLogin
	FacialMatchingDescLogin = "Login"
	// FacialMatchingDescUpdatePhoneNumber
	FacialMatchingDescUpdatePhoneNumber = "Update Phone Number"
	// FacialMatchingDescForgotPinFlow
	FacialMatchingDescForgotPinFlow = "Forgot PIN Flow"
	// FacialMatchingDescOnboardingFlow
	FacialMatchingDescOnboardingFlow = "Onboarding Flow"
	// FacialMatchingDescPayment
	FacialMatchingDescPayment = "Payment"
)

// DeviceLoginInfoField ...
const (
	// KeyDeviceLoginInfoDeviceID ...
	KeyDeviceLoginInfoDeviceID = "deviceLoginInfoDeviceId"
	// KeyDeviceLoginInfoDeviceBrand ...
	KeyDeviceLoginInfoDeviceBrand = "deviceLoginInfoDeviceBrand"
	// KeyDeviceLoginInfoDeviceModel ...
	KeyDeviceLoginInfoDeviceModel = "deviceLoginInfoDeviceModel"
	// KeyDeviceLoginInfoOsName ...
	KeyDeviceLoginInfoOsName = "deviceLoginInfoOsName"
	// KeyDeviceLoginInfoOsVersion ...
	KeyDeviceLoginInfoOsVersion = "deviceLoginInfoOsVersion"
	// KeyDeviceLoginInfoAppVersion ...
	KeyDeviceLoginInfoAppVersion = "deviceLoginInfoAppVersion"
	// KeyDeviceLoginInfoDeliveryStatus ...
	KeyDeviceLoginInfoDeliveryStatus = "deviceLoginInfoDeliveryStatus"
	// KeyDeviceLoginInfoLoginStatus ...
	KeyDeviceLoginInfoLoginStatus = "deviceLoginInfoLoginStatus"
	// KeyDeviceLoginInfoFailedReason ...
	KeyDeviceLoginInfoFailedReason = "deviceLoginInfoFailedReason"
	// KeyDeviceLoginInfoLoginSource
	KeyDeviceLoginInfoLoginSource = "deviceLoginInfoLoginSource"
)

// InteractionLogField ...
const (
	// KeyInteractionLogDeliveryDate ...
	KeyInteractionLogDeliveryDate = "interactionLogDeliveryDate"
	// KeyInteractionLogTrigger ...
	KeyInteractionLogTrigger = "interactionLogTrigger"
	// KeyInteractionLogContent ...
	KeyInteractionLogContent = "interactionLogContent"
	// KeyInteractionLogType ...
	KeyInteractionLogType = "interactionLogType"
	// KeyInteractionLogChannel ...
	KeyInteractionLogChannel = "interactionLogChannel"
	// KeyInteractionLogDeliveryStatus ...
	KeyInteractionLogDeliveryStatus = "interactionLogDeliveryStatus"
	// KeyInteractionLogFailedReason ...
	KeyInteractionLogFailedReason = "interactionLogFailedReason"
)

// MarketingPreferenceCurrentField ...
const (
	// KeyMarketingPreferenceCurrentPushNotification ...
	KeyMarketingPreferenceCurrentPushNotification = "marketingPreferenceCurrentPushNotification"
	// KeyMarketingPreferenceCurrentEmail ...
	KeyMarketingPreferenceCurrentEmail = "marketingPreferenceCurrentEmail"
	// KeyMarketingPreferenceCurrentSms ...
	KeyMarketingPreferenceCurrentSms = "marketingPreferenceCurrentSms"
	// KeyMarketingPreferenceCurrentWhatsapp ...
	KeyMarketingPreferenceCurrentWhatsapp = "marketingPreferenceCurrentWhatsapp"
)

// MarketingPreferenceCurrentValue ...
const (
	// MarketingPreferenceCurrentValueOn ...
	MarketingPreferenceCurrentValueOn = "Turn On"
	// MarketingPreferenceCurrentValueOff ...
	MarketingPreferenceCurrentValueOff = "Turn Off"
)

// MarketingPreferenceLogField ...
const (
	// KeyMarketingPreferenceLogTimestamp ...
	KeyMarketingPreferenceLogTimestamp = "marketingPreferenceLogTimestamp"
	// KeyMarketingPreferenceLogChannel ...
	KeyMarketingPreferenceLogChannel = "marketingPreferenceLogChannel"
	// KeyMarketingPreferenceLogPreference ...
	KeyMarketingPreferenceLogPreference = "marketingPreferenceLogPreference"
	// KeyMarketingPreferenceLogStatus ...
	KeyMarketingPreferenceLogStatus = "marketingPreferenceLogStatus"
	// KeyMarketingPreferenceLogFailedReason ...
	KeyMarketingPreferenceLogFailedReason = "marketingPreferenceLogFailedReason"
)

// Lending Tab Keys
const (
	// KeyLendingTabLendingTabStatus ...
	KeyLendingTabLendingTabStatus = "lendingTabStatus"
	// KeyLendingTabLendingTabStatusMaker ...
	KeyLendingTabLendingTabStatusMaker = "lendingTabStatusMaker"
	// KeyLendingTabReasonJustification ...
	KeyLendingTabReasonJustification = "reasonJustification"
)

// Onboarding Loan Key
const (
	// KeyOnboardingLoanApplicationID ...
	KeyOnboardingLoanApplicationID = "loanApplicationID"
	// KeyOnboardingLoanApplicationSubmitted ...
	KeyOnboardingLoanApplicationSubmitted = "loanApplicationSubmitted"
	// KeyOnboardingLoanLastModifiedDate ...
	KeyOnboardingLoanLastModifiedDate = "loanLastModifiedDate"
	// KeyOnboardingLoanCustomerType ...
	KeyOnboardingLoanCustomerType = "loanCustomerType"
	// KeyOnboardingLoanCustomerEducation ...
	KeyOnboardingLoanCustomerEducation = "loanCustomerEducation"
	// KeyOnboardingLoanApplicationStatus ...
	KeyOnboardingLoanApplicationStatus = "loanApplicationStatus"
	// KeyOnboardingLoanStatusReason ...
	KeyOnboardingLoanStatusReason = "loanStatusReason"
	// KeyOnboardingLoanCooloffPeriod ...
	KeyOnboardingLoanCooloffPeriod = "loanCooloffPeriod"
	// KeyOnboardingLoanCooloffPeriodEndDate ...
	KeyOnboardingLoanCooloffPeriodEndDate = "loanCooloffPeriodEndDate"
	// KeyOnboardingLoanChannel ...
	KeyOnboardingLoanChannel = "loanChannel"
	// KeyOnboardingLoanWhitelistedStatus ...
	KeyOnboardingLoanWhitelistedStatus = "loanWhitelistedStatus"
	// KeyOnboardingLoanWhitelistedChannel ...
	KeyOnboardingLoanWhitelistedChannel = "loanWhitelistedChannel"
	// KeyOnboardingLoanWhitelistedTimestamp ...
	KeyOnboardingLoanWhitelistedTimestamp = "loanWhitelistedTimestamp"
	// KeyOnboardingLoanConsentID ...
	KeyOnboardingLoanConsentID = "loanConsentID"
	// KeyOnboardingLoanConsentTimestamp ...
	KeyOnboardingLoanConsentTimestamp = "loanConsentTimestamp"
)

// Address Loan Key
const (
	// KeyAddressLoanStreet ...
	KeyAddressLoanStreet = "loanStreet"
	// KeyAddressLoanRT ...
	KeyAddressLoanRT = "loanRT"
	// KeyAddressLoanRW ...
	KeyAddressLoanRW = "loanRW"
	// KeyAddressLoanKelurahan ...
	KeyAddressLoanKelurahan = "loanKelurahan"
	// KeyAddressLoanKecamatan ...
	KeyAddressLoanKecamatan = "loanKecamatan"
	// KeyAddressLoanKota ...
	KeyAddressLoanKota = "loanKota"
	// KeyAddressLoanProvinsi ...
	KeyAddressLoanProvinsi = "loanProvinsi"
	// KeyAddressLoanKodePos ...
	KeyAddressLoanKodePos = "loanKodePos"
	// KeyAddressLoanAddressType ...
	KeyAddressLoanAddressType = "loanAddressType"
)

// Emergency Contact Key
const (
	// KeyEmergencyContactName ...
	KeyEmergencyContactName = "emergencyContactName"
	// KeyEmergencyContactMobileNumber ...
	KeyEmergencyContactMobileNumber = "emergencyContactMobileNumber"
	// KeyEmergencyContactRelationship ...
	KeyEmergencyContactRelationship = "emergencyContactRelationship"
)

// Portfolio Review Log Key
const (
	// KeyPortfolioReviewLogApmReviewID ...
	KeyPortfolioReviewLogApmReviewID = "apmReviewID"
	// KeyPortfolioReviewLogApmEventTrigger ...
	KeyPortfolioReviewLogApmEventTrigger = "apmEventTrigger"
	// KeyPortfolioReviewLogApmReviewDate ...
	KeyPortfolioReviewLogApmReviewDate = "apmReviewDate"
	// KeyPortfolioReviewLogApmExecutionDate ...
	KeyPortfolioReviewLogApmExecutionDate = "apmExecutionDate"
	// KeyPortfolioReviewLogApmStrategyApplied ...
	KeyPortfolioReviewLogApmStrategyApplied = "apmStrategyApplied"
	// KeyPortfolioReviewLogApmRecommendation ...
	KeyPortfolioReviewLogApmRecommendation = "apmRecommendation"
	// KeyPortfolioReviewLogReviewLogTable ...
	KeyPortfolioReviewLogReviewLogTable = "reviewLog"
)

// Review Log Table Key
const (
	// KeyReviewLogTableReviewParameterChanges ...
	KeyReviewLogTableParameterChanges = "reviewParameterChanges"
	// KeyReviewLogTableReviewPreviousValue ...
	KeyReviewLogTablePreviousValue = "reviewPreviousValue"
	// KeyReviewLogTableReviewNewValue ...
	KeyReviewLogTableNewValue = "reviewNewValue"
)

// Line Of Credit Key
const (
	// KeyLocProductVariant ...
	KeyLocProductVariant = "locProductVariant"
	// KeyLocAccountID ...
	KeyLocAccountID = "locAccountID"
	// KeyLocStatus ...
	KeyLocStatus = "locStatus"
	// KeyLocCloseLocTimestamp
	KeyLocCloseLocTimestamp = "locCloseLocTimestamp"
	// KeyLocOpsReasonForCloseLOC ...
	KeyLocOpsReasonForCloseLOC = "locOpsReasonForCloseLOC"
	// KeyLocTotalCreditLimit ...
	KeyLocTotalCreditLimit = "locTotalCreditLimit"
	// KeyLocCreditLimitCreatedDate ...
	KeyLocCreditLimitCreatedDate = "locCreditLimitCreatedDate" // nolint:gosec
	// KeyLocAvailableCreditLimit ...
	KeyLocAvailableCreditLimit = "locAvailableCreditLimit"
	// KeyLocTotalRemainingPayable ...
	KeyLocTotalRemainingPayable = "locTotalRemainingPayable"
	// KeyLocCountOfActiveLoan ...
	KeyLocCountOfActiveLoan = "locCountOfActiveLoan"
	// KeyLocMaxTenor ...
	KeyLocMaxTenor = "locMaxTenor"
	// KeyLocNextDueDate ...
	KeyLocNextDueDate = "locNextDueDate"
	// KeyLocTotalNextInstallment ...
	KeyLocTotalNextInstallment = "locTotalNextInstallment"
	// KeyLocEffectiveRate ...
	KeyLocEffectiveRate = "locEffectiveRate"
	// KeyLocFlatRate ...
	KeyLocFlatRate = "locFlatRate"
	// KeyLocExpiryDate ...
	KeyLocExpiryDate = "locExpiryDate"
	// KeyLocBlockStatus ...
	KeyLocBlockStatus = "locBlockStatus"
	// KeyLocBlockTable ...
	KeyLocBlockTable = "locBlockTable"
	// KeyLocSubStatus ...
	KeyLocSubStatus = "locSubStatus"
)

// LOC Block Table Key
const (
	// KeyBlockReasonCodeForBlockLOC ...
	KeyBlockReasonCodeForBlockLOC = "blockReasonCodeForBlockLOC"
	// KeyBlockLOCTimestamp ...
	KeyBlockLOCTimestamp = "blockLOCTimestamp"
	// KeyBlockCRMReasonForBlockLOC ...
	KeyBlockCRMReasonForBlockLOC = "blockCRMReasonForBlockLOC"
	// KeyBlockOpsReasonForBlockLOC ...
	KeyBlockOpsReasonForBlockLOC = "blockOpsReasonForBlockLOC"
	// KeyBlockLOCMakerName ...
	KeyBlockLOCMakerName = "blockLOCMakerName"
)

// Active Loan List Key
const (
	// KeyActiveLoanListLoanAccountNumber ...
	KeyActiveLoanListLoanAccountNumber = "loanAccountNumber"
	// KeyActiveLoanListLoanPurpose ...
	KeyActiveLoanListLoanPurpose = "loanPurpose"
	// KeyActiveLoanListLoanCollectability ...
	KeyActiveLoanListLoanCollectability = "loanCollectability"
	// KeyActiveLoanListLoanStatus ...
	KeyActiveLoanListLoanStatus = "loanStatus"
	// KeyActiveLoanListLoanPaidAmount ...
	KeyActiveLoanListLoanPaidAmount = "loanPaidAmount"
	// KeyActiveLoanListLoanTotalRemainingOutstanding ...
	KeyActiveLoanListLoanTotalRemainingOutstanding = "loanTotalRemainingOutstanding"
	// KeyActiveLoanListLoanCreatedLoanAccount ...
	KeyActiveLoanListLoanCreatedLoanAccount = "loanCreatedLoanAccount"
	// KeyActiveLoanListLoanTenor ...
	KeyActiveLoanListLoanTenor = "loanTenor"
	// KeyActiveLoanListLoanMaturityDate ...
	KeyActiveLoanListLoanMaturityDate = "loanMaturityDate"
)

// Active Loan List Detail
const (
	// KeyLoanDetailInstallmentDetails ...
	KeyLoanDetailInstallmentDetails = "installmentDetails"
	// KeyLoanDetailProductVariant ...
	KeyLoanDetailProductVariant = "loanDetailProductVariant"
	// KeyLoanDetailAccountStatus ...
	KeyLoanDetailAccountStatus = "loanDetailAccountStatus"
	// KeyLoanDetailAccountSubStatus ...
	KeyLoanDetailAccountSubStatus = "loanDetailAccountSubStatus"
	// KeyLoanDetailDisbursementDate ...
	KeyLoanDetailDisbursementDate = "loanDetailDisbursementDate"
	// KeyLoanDetailDisbursementAmount ...
	KeyLoanDetailDisbursementAmount = "loanDetailDisbursementAmount"
	// KeyLoanDetailRemainingPayable ...
	KeyLoanDetailRemainingPayable = "loanDetailRemainingPayable"
	// KeyLoanDetailInterestSaved ...
	KeyLoanDetailInterestSaved = "loanDetailInterestSaved"
	// KeyLoanDetailPreferredPaymentDate ...
	KeyLoanDetailPreferredPaymentDate = "loanDetailPreferredPaymentDate"
	// KeyLoanDetailNextInstallment ...
	KeyLoanDetailNextInstallment = "loanDetailNextInstallment"
	// KeyLoanDetailNextInstallmentDueDate ...
	KeyLoanDetailNextInstallmentDueDate = "loanDetailNextInstallmentDueDate"
	// KeyLoanDetailLastUpdatedDate ...
	KeyLoanDetailLastUpdatedDate = "loanDetailLastUpdatedDate"
	// KeyLoanDetailRecoveryAmount ...
	KeyLoanDetailRecoveryAmount = "loanDetailRecoveryAmount"
	// KeyLoanDetailExpiryDate ...
	KeyLoanDetailExpiryDate = "loanDetailExpiryDate"
	// KeyLoanDetailDPD ...
	KeyLoanDetailDPD = "loanDetailDPD"
	// KeyLoanDetailWriteOffDate ...
	KeyLoanDetailWriteOffDate = "loanDetailWriteOffDate"
	// KeyLoanDetailWriteOffAmount ...
	KeyLoanDetailWriteOffAmount = "loanDetailWriteOffAmount"
	// KeyLoanDetailWriteOffReasonCode ...
	KeyLoanDetailWriteOffReasonCode = "loanDetailWriteOffReasonCode"
)

// Installment Detail
const (
	// KeyInstallmentDetailInstallmentNumber ...
	KeyInstallmentDetailInstallmentNumber = "installmentDetailInstallmentNumber"
	// KeyInstallmentDetailDueDate ...
	KeyInstallmentDetailDueDate = "installmentDetailDueDate"
	// KeyInstallmentDetailTotalOutstanding ...
	KeyInstallmentDetailTotalOutstanding = "installmentDetailTotalOutstanding"
	// KeyInstallmentDetailPrincipalOutstanding ...
	KeyInstallmentDetailPrincipalOutstanding = "installmentDetailPrincipalOutstanding"
	// KeyInstallmentDetailInterestOutstanding ...
	KeyInstallmentDetailInterestOutstanding = "installmentDetailInterestOutstanding"
	// KeyInstallmentDetailLateFeeOutstanding ...
	KeyInstallmentDetailLateFeeOutstanding = "installmentDetailLateFeeOutstanding"
	// KeyInstallmentDetailInstallmentDPD ...
	KeyInstallmentDetailInstallmentDPD = "installmentDetailInstallmentDPD"
	// KeyInstallmentDetailUpdatedAt
	KeyInstallmentDetailUpdatedAt = "installmentDetailUpdatedAt"
	// KeyInstallmentDetailStatus
	KeyInstallmentDetailStatus = "installmentDetailStatus"
	// KeyInstallmentDetailPaidAmount
	KeyInstallmentDetailPaidAmount = "installmentDetailPaidAmount"
	// KeyInstallmentDetailPaidPrinciple
	KeyInstallmentDetailPaidPrinciple = "installmentDetailPaidPrinciple"
	// KeyInstallmentDetailPaidInterest
	KeyInstallmentDetailPaidInterest = "installmentDetailPaidInterest"
	// KeyInstallmentDetailPaidLateFee
	KeyInstallmentDetailPaidLateFee = "installmentDetailPaidLateFee"
)

// RECDD
const (
	// KeyCustomerReEnhancedDueDiligenceFlag
	KeyCustomerReEnhancedDueDiligenceFlag = "recddFlag"
	// KeyCustomerReEnhancedDueDiligenceLatestDate
	KeyCustomerReEnhancedDueDiligenceLatestDate = "recddLatestDate"
	// KeyCustomerReEnhancedDueDiligenceNextDate
	KeyCustomerReEnhancedDueDiligenceNextDate = "recddNextDate"
)

// RECDD FLAG
const (
	// KeyTrue
	KeyTrue = "True"
	// KeyFalse
	KeyFalse = "False"
)

// list of lending pas section
const (
	// KeyOnboardingLoan ...
	KeyOnboardingLoan = "onboardingLoan"
	// KeyAddressLoan ...
	KeyAddressLoan = "addressLoan"
	// KeyEmergencyContact ...
	KeyEmergencyContact = "emergencyContact"
	// KeyPortfolioReviewLog ...
	KeyPortfolioReviewLog = "portfolioReviewLog"
	// KeyLocInformation ...
	KeyLocInformation = "locInformation"
)

// active loan table
const (
	// KeyActiveLoanList ...
	KeyActiveLoanList = "activeLoanList"
	// KeyActiveLoanListDetails ...
	KeyActiveLoanListDetails = "activeLoanListDetails"
)

// ConsentMap ...
var ConsentMap = map[string]string{
	"1": "Agreed TNC",
	"2": "Agreed Non US Tax Citizen",
	"3": "Agreed To Marketing Offers",
	"4": "Agreed To OVO Phone Sharing",
	"5": "Agreed To OVO Data Sharing",
	"6": "Agreed To TNC Lending",
}

// EducationMap ...
var EducationMap = map[int]string{
	1:  "SD / Sederajat",
	2:  "SLTP / Sederajat",
	3:  "SLTA / Sederajat",
	4:  "DI / Diploma I",
	5:  "DII / Diploma II",
	6:  "DIII / Diploma III",
	7:  "DIV / Diploma IV",
	8:  "S1 / Strata I",
	9:  "S2 / Strata II",
	10: "S3 / Strata III",
	11: "Tidak / Belum Sekolah",
}

// InstructionType ...
const (
	// InstructionTypeAccelerate ...
	InstructionTypeAccelerate = "ACCELERATE"
	// InstructionTypeWaiveOff ...
	InstructionTypeWaiveOff = "WAIVE_OFF"
	// InstructionTypeWriteOff ...
	InstructionTypeWriteOff = "WRITE_OFF"
	// InstructionTypeBlockUnblock ...
	InstructionTypeBlockUnblock = "BLOCK_UNBLOCK"
)

// LocBlockHoldCode ...
const (
	LocBlockHoldCodeTemporaryBlocked     = "TEMPORARY_BLOCKED"
	LocBlockHoldCodeNameTemporaryBlocked = "Temporary Blocked"
	LocBlockHoldCodePermanentBlocked     = "PERMANENT_BLOCKED"
	LocBlockHoldCodeNamePermanentBlocked = "Permanent Blocked"
)

// BlockInfo ...
type BlockInfo struct {
	BlockStatus       string   `json:"blockStatus"`
	BlockStatusCode   string   `json:"blockStatusCode"`
	IsDrawdownBlocked bool     `json:"isDrawdownBlocked"`
	ReasonCodes       []string `json:"reasonCode"`
	ReasonNames       []string `json:"reasonName"`
}

// LocBlockStatus ...
const (
	LocBlockStatusBlocked    = "Blocked"
	LocBlockStatusNotBlocked = "Not Blocked"
)

// LoanAccountStatus ...
const (
	LoanAccountStatusActive   = "ACTIVE"
	LoanAccountStatusInactive = "INACTIVE"
)

const (
	// FetchAccountDetailsRedisKey ...
	FetchAccountDetailsRedisKey = "account_details:%s:%s:%s"
)

// SelectedTableSegregations ...
var SelectedTableSegregations = map[string]bool{
	KeyActiveLoanListDetails:   true,
	KeyLocBlockTable:           true,
	KeyActiveInstallmentList:   true,
	KeyInactiveInstallmentList: true,
}

// InstallmentStatusUnpaid ...
const (
	InstallmentStatusUnpaid = "UNPAID"
)

// AroMechanismMap ...
var AroMechanismMap = map[string]string{
	"PRINCIPAL_AND_INTEREST_ARO": "ARO P+I",
	"PRINCIPAL_ONLY_ARO":         "ARO P",
	"NON_ARO":                    "Non-ARO",
}

// NonAROType ...
const NonAROType = "NON_ARO"

// ConsentTypeDescription
const (
	// ConsentTypePushDescription
	ConsentTypePushDescription = "Push Notification"
	// ConsentTypeEmailDescription
	ConsentTypeEmailDescription = "Email"
	// ConsentTypeSmsDescription
	ConsentTypeSmsDescription = "SMS"
	// ConsentTypeWhatsappDescription
	ConsentTypeWhatsappDescription = "Whatsapp"
)

const (
	ConstTypeCustomerInfo     = "customer_info"
	ConstTypeCustomerStatus   = "customer_status"
	ConstTypeCustomerAccounts = "accounts"
)

// ArrayTypeSegregation ...
var ArrayTypeSegregation = map[string]bool{
	SectionArrayType:       true,
	TableType:              true,
	SectionTableType:       true,
	SectionQnA:             true,
	TablePreselectType:     true,
	SectionDetailTableType: true,
}

const (
	// KeyCustomerSummary ...
	KeyCustomerSummary = "customerSummary"

	// Customer Summary Fields
	// KeyCustomerSummaryCustomerName ...
	KeyCustomerSummaryCustomerName = "custSummaryCustomerName"
	// KeyCustomerSummaryCif ...
	KeyCustomerSummaryCif = "custSummaryCif"
	// KeyCustomerSummaryCustomerStatus ...
	KeyCustomerSummaryCustomerStatus = "custSummaryCustomerStatus"
	// KeyCustomerSummaryApplicationStatus ...
	KeyCustomerSummaryApplicationStatus = "custSummaryApplicationStatus"
	// KeyCustomerSummarySafeId ...
	KeyCustomerSummarySafeId = "custSummarySafeId"
	// KeyCustomerSummaryPhoneNumber ...
	KeyCustomerSummaryPhoneNumber = "custSummaryPhoneNumber"
	// KeyCustomerSummaryAccessStatus ...
	KeyCustomerSummaryAccessStatus = "custSummaryAccessStatus"
	// KeyCustomerSummaryCasaAccountBalance ...
	KeyCustomerSummaryCasaAccountBalance = "custSummaryCasaAccountBalance"
	// KeyCustomerSummaryBlockedCasaAccount ...
	KeyCustomerSummaryBlockedCasaAccount = "custSummaryBlockedCasaAccount"
	// KeyCustomerSummaryActiveCasaAccount ...
	KeyCustomerSummaryActiveCasaAccount = "custSummaryActiveCasaAccount"
	// KeyCustomerSummaryCreditLimit ...
	KeyCustomerSummaryCreditLimit = "custSummaryCreditLimit"
	// KeyCustomerSummaryOutstandingLoan ...
	KeyCustomerSummaryOutstandingLoan = "custSummaryOutstandingLoan"
)

// Lending Sub Tab ...
const (
	KeyTermLoanPartner = "lending.termLoanPartner"
)

// Table Term Loan Partner ...
const (
	KeyTermActiveLoanList = "lending.termLoanPartner.activeLoanList"
	KeyInactiveLoanList   = "lending.termLoanPartner.inactiveLoanList"
)

// Active Loan Detail List Table Field
const (
	KeyActiveLoanAccountNumber    = "lending.termLoanPartner.activeLoanList.loanAccountNumber"
	KeyActiveAkulakuLoanId        = "lending.termLoanPartner.activeLoanList.akulakuLoanID"
	KeyActivePartner              = "lending.termLoanPartner.activeLoanList.partner"
	KeyActiveProductType          = "lending.termLoanPartner.activeLoanList.productType"
	KeyActiveLoanPeriod           = "lending.termLoanPartner.activeLoanList.loanPeriod"
	KeyActiveInterestRate         = "lending.termLoanPartner.activeLoanList.interestRate"
	KeyActiveDisbursementDate     = "lending.termLoanPartner.activeLoanList.disbursementDate"
	KeyActiveMaturityDate         = "lending.termLoanPartner.activeLoanList.maturityDate"
	KeyActivePrincipal            = "lending.termLoanPartner.activeLoanList.principal"
	KeyActiveTotalInterestAmount  = "lending.termLoanPartner.activeLoanList.totalInterestAmount"
	KeyActiveTotalInstallmentFee  = "lending.termLoanPartner.activeLoanList.totalInstallmentFee"
	KeyActiveOutstandingPrincipal = "lending.termLoanPartner.activeLoanList.outstandingPrincipal"
	KeyActiveOutstandingInterest  = "lending.termLoanPartner.activeLoanList.outstandingInterest"
	KeyActiveOutstandingFee       = "lending.termLoanPartner.activeLoanList.outstandingFee"
	KeyActiveInstallmentAmount    = "lending.termLoanPartner.activeLoanList.installmentAmount"
	KeyActiveNextInstallmentDate  = "lending.termLoanPartner.activeLoanList.nextInstallmentDate"
	KeyActiveOverdueAmount        = "lending.termLoanPartner.activeLoanList.overdueAmount"
	KeyActiveCollectibility       = "lending.termLoanPartner.activeLoanList.collectibility"
	KeyActiveDpd                  = "lending.termLoanPartner.activeLoanList.dpd"
	KeyActiveLoanStatus           = "lending.termLoanPartner.activeLoanList.loanStatus"
	KeyActiveLoanDetail           = "lending.termLoanPartner.activeLoanList.loanDetail"
)

// Section Custom From Active Detail Loan Account ...
const (
	KeyActiveLoanOnboardingData = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData"
	KeyActiveLoanOverview       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview"
)

// Section From Loan Onboarding Data ...
const (
	KeyActiveCustomerInformation = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation"
	KeyActiveEmergencyContact    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact"
	KeyActiveWorkInformation     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation"
	KeyActiveContractList        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList"
	KeyActiveDrawdownAccountInfo = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo"
	KeyActiveDeviceInformation   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation"
)

// Customer Information Field ...
const (
	KeyActiveAkulakuCustomerID       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.akulakuCustomerID"
	KeyActiveSuperbankCustomerID     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.superbankCustomerID"
	KeyActiveOnboardingApplicationID = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingApplicationID"
	KeyActiveCustomerName            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.customerName"
	KeyActiveKtpNumber               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.ktpNumber"
	KeyActivePhoneNumber             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.phoneNumber"
	KeyActivePartnerSelfieImage      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.partnerSelfieImage"
	KeyActiveEmail                   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.email"
	KeyActiveNationality             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.nationality"
	KeyActiveDateOfBirth             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.dateOfBirth"
	KeyActivePlaceOfBirth            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.placeOfBirth"
	KeyActiveMaritalStatus           = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.maritalStatus"
	KeyActiveGender                  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.gender"
	KeyActiveSelfieImage             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.selfieImage"
	KeyActiveMotherMaidenName        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.motherMaidenName"
	KeyActiveNpwpAvailability        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.npwpAvailability"
	KeyActiveRiskLevel               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.riskLevel"
	KeyActiveKtpFile                 = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.ktpFile"
	KeyActiveEducation               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.education"
	KeyActiveUserLevel               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.userLevel"
	KeyActiveUserType                = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.userType"
	KeyActiveOnboardingSelfie        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingImage"
)

// Section Array From Loan Onboarding Data ...
const (
	KeyActiveAddressDetail = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail"
)

// Address Detail Field ...
const (
	KeyActiveKtpAddress          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpAddress"
	KeyActiveKtpPostalCode       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpPostalCode"
	KeyActiveKtpProvince         = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpProvince"
	KeyActiveKtpCity             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCity"
	KeyActiveKtpDistrict         = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpDistrict"
	KeyActiveKtpSubDistrict      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpSubDistrict"
	KeyActiveKtpCountry          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCountry"
	KeyActiveDomicileAddress     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileAddress"
	KeyActiveDomicilePostalCode  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicilePostalCode"
	KeyActiveDomicileProvince    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileProvince"
	KeyActiveDomicileCity        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCity"
	KeyActiveDomicileDistrict    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileDistrict"
	KeyActiveDomicileSubDistrict = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileSubDistrict"
	KeyActiveDomicileCountry     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCountry"
)

// Emergency Contact Field ...
const (
	KeyActiveEmergencyContactName         = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactName"
	KeyActiveEmergencyContactPhoneNumber  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactPhoneNumber"
	KeyActiveEmergencyContactRelationship = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactRelationship"
)

// Work Information Field ...
const (
	KeyActiveWorkingMonths = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.workingMonths"
	KeyActiveStayMonths    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.stayMonths"
	KeyActiveIncomeSource  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.incomeSource"
	KeyActiveMonthlyIncome = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.monthlyIncome"
	KeyActiveCompanyName   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.companyName"
	KeyActiveBusinessType  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.businessType"
	KeyActiveWorkType      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.workType"
	KeyActiveJobPosition   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.position"
)

// Contract List Field ...
const (
	KeyActiveContractName      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractName"
	KeyActiveContractUrl       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractUrl"
	KeyActiveContractTimestamp = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractTimestamp"
)

// Drawdown Account Info Field ...
const (
	KeyActiveDrawdownAmount      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAmount"
	KeyActiveDrawdownBankName    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankName"
	KeyActiveDrawdownAccountName = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAccountName"
	KeyActiveDrawdownBankCode    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankCode"
)

// Device Information Field ...
const (
	KeyActiveDeviceID       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceID"
	KeyActiveAdjustID       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.adjustID"
	KeyActiveImei           = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.imei"
	KeyActiveIpAddress      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.ipAddress"
	KeyActiveLatitude       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.latitude"
	KeyActiveLongitude      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.longitude"
	KeyActiveAppPackageName = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.appPackageName"
	KeyActiveAppVersion     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.appVersion"
	KeyActiveDeviceBrand    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceBrand"
	KeyActiveDeviceModel    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceModel"
	KeyActiveOsSystem       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.osSystem"
	KeyActiveOsVersion      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.osVersion"
)

// Section From Loan Overview ...
const (
	KeyActiveLoanSummary       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary"
	KeyActiveCreditInformation = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation"
)

// Loan Summary Field ...
const (
	KeyActiveLoanProductID                  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanProductID"
	KeyActiveLoanSuperbankLoanID            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanID"
	KeyActiveLoanSuperbankLoanAccountNumber = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanAccountNumber"
	KeyActiveLoanApplicationStatus          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanApplicationStatus"
	KeyActiveLoanAccountStatus              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAccountStatus"
	KeyActiveLoanPartnerName                = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanPartnerName"
	KeyActiveLoanAkulakuLoanID              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAkulakuLoanID"
	KeyActiveLoanAmount                     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAmount"
	KeyActiveloanCommission                 = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCommission"
	KeyActiveLoanTenor                      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanTenor"
	KeyActiveLoanInterestRate               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanInterestRate"
	KeyActiveLoanInterestAmount             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanInterestAmount"
	KeyActiveLoanApplicationDate            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanApplicationDate"
	KeyActiveLoanDisbursementDate           = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanDsibursementDate"
	KeyActiveLoanFistDueDate                = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanFirstDueDate"
	KeyActiveLoanNextDueDate                = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanNextDueDate"
	KeyActiveLoanPurposeOfCredit            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanPurposeOfCredit"
	KeyActiveLoanMaturityDate               = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanMaturityDate"
	KeyActiveLoanCollectability             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCollectability"
	KeyActiveLoanDpd                        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanDpd"
	KeyActiveLoanRemainingOutstanding       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanRemainingOutstanding"
	KeyActiveLoanNextInstallment            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanNextInstallment"
	KeyActiveLoanAccountSubStatus           = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAccountSubStatus"
	KeyActiveLoanCode                       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCode"
	KeyActiveLoanReason                     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanReason"
)

// Credit Information Field ...
const (
	KeyActiveCreditUserLevel = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation.creditUserLevel"
	KeyActiveCreditUserType  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation.creditUserType"
)

// Table From Loan Overview ...
const (
	KeyActiveInstallmentDetail   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail"
	KeyActiveRepaymentDetail     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail"
	KeyActiveVaGenerationHistory = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory"
)

// Installment Detail Field ...
const (
	KeyActiveLoanInstallmentID        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentID"
	KeyActiveLoanInstallmentNumber    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentNumber"
	KeyActiveLoanDueDate              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanDueDate"
	KeyActiveLoanTotalOutstanding     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanTotalOutstanding"
	KeyActiveLoanPrincipalOutstanding = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanPrincipalOutstanding"
	KeyActiveLoanInterestOutstanding  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInterestOutstanding"
	KeyActiveLoanLateFeeOutstanding   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanLateFeeOutstanding"
	KeyActiveLoanInstallmentDpd       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDpd"
	KeyActiveLoanUpdatedAt            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanUpdatedAt"
	KeyActiveLoanInstallmentStatus    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentStatus"
	KeyActiveLoanInstallmentDetail    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail"
)

// Repayment Detail Field ...
const (
	KeyActiveLoanRepaymentID              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentID"
	KeyActiveLoanRepaymentDate            = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDate"
	KeyActiveLoanVaNumber                 = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanVaNumber"
	KeyActiveLoanVaIssuerName             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanIssuerName"
	KeyActiveLoanTotalAmount              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanTotalAmount"
	KeyActiveLoanRepaymentStatus          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentStatus"
	KeyActiveLoanPgVaTransactionID        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanPgVaTransactionID"
	KeyActiveLoanSuperbankVaTransactionID = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanSuperbankVaTransactionID"
	KeyActiveLoanRepaymentDetail          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail"
)

// VA Generation History Field ...
const (
	KeyActiveVaNumber              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaNumber"
	KeyActiveVaIssuerName          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaIssuerName"
	KeyActiveVaAmount              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaAmount"
	KeyActiveVaGenerationTimestamp = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaGenerationTimestamp"
	KeyActiveVaRequestTimestamp    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaRequestTimestamp"
	KeyActiveVaExpiryDate          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaExpiryDate"
	KeyActiveVaSendNotifTo         = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaSendNotifTo"
)

// Modal Installment Detail Field ...
const (
	KeyActiveLoanDetailInstallmentID          = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentID"
	KeyActiveLoanDetailUpdatedAt              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanUpdatedAt"
	KeyActiveLoanDetailInsuranceAmount        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInsuranceAmount"
	KeyActiveLoanDetailDueDate                = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanDueDate"
	KeyActiveLoanDetailPrincipalInstallment   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalInstallment"
	KeyActiveLoanDetailTotalInstallmentAmount = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalInstallmentAmount"
	KeyActiveLoanDetailInstallmentDpd         = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentDpd"
	KeyActiveLoanDetailInstallmentInterest    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentInterest"
	KeyActiveLoanDetailInstallmentStatus      = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentStatus"
	KeyActiveLoanDetailTotalOutstanding       = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalOutstanding"
	KeyActiveLoanDetailOutstandingInsurance   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInsurance"
	KeyActiveLoanDetailPrincipalOutstanding   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalOutstanding"
	KeyActiveLoanDetailLateFeeOutstanding     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanLateFeeOutstanding"
	KeyActiveLoanDetailOutstandingInterest    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInterest"
	KeyActiveLoanDetailAdminFeeOutstanding    = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanAdminFeeOutstanding"
)

// Modal Repayment Detail Field ...
const (
	KeyActiveRepaymentDate                     = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentDate"
	KeyActiveRepaymentTotalAmount              = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentTotalAmount"
	KeyActiveRepaymentSuperbankVaTransactionID = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentSuperbankVaTransactionID"
	KeyActiveRepaymentVaNumber                 = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaNumber"
	KeyActiveRepaymentStatus                   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentStatus"
	KeyActiveRepaymentVaIssuerName             = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaIssuerName"
	KeyActiveRepaymentPgVaTransactionID        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentPgVaTransactionID"
	KeyActiveInstallmentList                   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList"
)

// Table Installment List Repayment Detail
const (
	KeyActiveRepaymentInstallmentNumber        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentNumber"
	KeyActiveRepaymentInstallmentAmount        = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAmount"
	KeyActiveRepaymentInstallmentPrincipalPaid = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentPrincipalPaid"
	KeyActiveRepaymentInstallmentInterestPaid  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInterestPaid"
	KeyActiveRepaymentInstallmentAdminFeePaid  = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAdminFeePaid"
	KeyActiveRepaymentInstallmentLateFeePaid   = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentLateFeePaid"
	KeyActiveRepaymentInstallmentInsurancePaid = "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInsurancePaid"
)

// Inactive Loan Detail List Table Field
const (
	KeyInactiveLoanAccountNumber    = "lending.termLoanPartner.inactiveLoanList.loanAccountNumber"
	KeyInactiveAkulakuLoanId        = "lending.termLoanPartner.inactiveLoanList.akulakuLoanID"
	KeyInactivePartner              = "lending.termLoanPartner.inactiveLoanList.partner"
	KeyInactiveProductType          = "lending.termLoanPartner.inactiveLoanList.productType"
	KeyInactiveLoanPeriod           = "lending.termLoanPartner.inactiveLoanList.loanPeriod"
	KeyInactiveInterestRate         = "lending.termLoanPartner.inactiveLoanList.interestRate"
	KeyInactiveDisbursementDate     = "lending.termLoanPartner.inactiveLoanList.disbursementDate"
	KeyInactiveMaturityDate         = "lending.termLoanPartner.inactiveLoanList.maturityDate"
	KeyInactivePrincipal            = "lending.termLoanPartner.inactiveLoanList.principal"
	KeyInactiveTotalInterestAmount  = "lending.termLoanPartner.inactiveLoanList.totalInterestAmount"
	KeyInactiveTotalInstallmentFee  = "lending.termLoanPartner.inactiveLoanList.totalInstallmentFee"
	KeyInactiveOutstandingPrincipal = "lending.termLoanPartner.inactiveLoanList.outstandingPrincipal"
	KeyInactiveOutstandingInterest  = "lending.termLoanPartner.inactiveLoanList.outstandingInterest"
	KeyInactiveOutstandingFee       = "lending.termLoanPartner.inactiveLoanList.outstandingFee"
	KeyInactiveInstallmentAmount    = "lending.termLoanPartner.inactiveLoanList.installmentAmount"
	KeyInactiveNextInstallmentDate  = "lending.termLoanPartner.inactiveLoanList.nextInstallmentDate"
	KeyInactiveOverdueAmount        = "lending.termLoanPartner.inactiveLoanList.overdueAmount"
	KeyInactiveCollectibility       = "lending.termLoanPartner.inactiveLoanList.collectibility"
	KeyInactiveDpd                  = "lending.termLoanPartner.inactiveLoanList.dpd"
	KeyInactiveLoanStatus           = "lending.termLoanPartner.inactiveLoanList.loanStatus"
	KeyInactiveLoanDetail           = "lending.termLoanPartner.inactiveLoanList.loanDetail"
)

// Section Custom From Inactive Detail Loan Account ...
const (
	KeyInactiveLoanOnboardingData = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData"
	KeyInactiveLoanOverview       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview"
)

// Section From Loan Onboarding Data ...
const (
	KeyInactiveCustomerInformation = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation"
	KeyInactiveEmergencyContact    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact"
	KeyInactiveWorkInformation     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation"
	KeyInactiveContractList        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList"
	KeyInactiveDrawdownAccountInfo = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo"
	KeyInactiveDeviceInformation   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation"
)

// Customer Information Field ...
const (
	KeyInactiveAkulakuCustomerID       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.akulakuCustomerID"
	KeyInactiveSuperbankCustomerID     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.superbankCustomerID"
	KeyInactiveOnboardingApplicationID = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingApplicationID"
	KeyInactiveCustomerName            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.customerName"
	KeyInactiveKtpNumber               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.ktpNumber"
	KeyInactivePhoneNumber             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.phoneNumber"
	KeyInactivePartnerSelfieImage      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.partnerSelfieImage"
	KeyInactiveEmail                   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.email"
	KeyInactiveNationality             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.nationality"
	KeyInactiveDateOfBirth             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.dateOfBirth"
	KeyInactivePlaceOfBirth            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.placeOfBirth"
	KeyInactiveMaritalStatus           = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.maritalStatus"
	KeyInactiveGender                  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.gender"
	KeyInactiveSelfieImage             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.selfieImage"
	KeyInactiveMotherMaidenName        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.motherMaidenName"
	KeyInactiveNpwpAvailability        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.npwpAvailability"
	KeyInactiveRiskLevel               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.riskLevel"
	KeyInactiveKtpFile                 = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.ktpFile"
	KeyInactiveEducation               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.education"
	KeyInactiveUserLevel               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.userLevel"
	KeyInactiveUserType                = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.userType"
	KeyInactiveOnboardingSelfie        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingImage"
)

// Section Array From Loan Onboarding Data ...
const (
	KeyInactiveAddressDetail = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail"
)

// Address Detail Field ...
const (
	KeyInactiveKtpAddress          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpAddress"
	KeyInactiveKtpPostalCode       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpPostalCode"
	KeyInactiveKtpProvince         = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpProvince"
	KeyInactiveKtpCity             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCity"
	KeyInactiveKtpDistrict         = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpDistrict"
	KeyInactiveKtpSubDistrict      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpSubDistrict"
	KeyInactiveKtpCountry          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCountry"
	KeyInactiveDomicileAddress     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileAddress"
	KeyInactiveDomicilePostalCode  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicilePostalCode"
	KeyInactiveDomicileProvince    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileProvince"
	KeyInactiveDomicileCity        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCity"
	KeyInactiveDomicileDistrict    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileDistrict"
	KeyInactiveDomicileSubDistrict = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileSubDistrict"
	KeyInactiveDomicileCountry     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCountry"
)

// Emergency Contact Field ...
const (
	KeyInactiveEmergencyContactName         = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactName"
	KeyInactiveEmergencyContactPhoneNumber  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactPhoneNumber"
	KeyInactiveEmergencyContactRelationship = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactRelationship"
)

// Work Information Field ...
const (
	KeyInactiveWorkingMonths = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.workingMonths"
	KeyInactiveStayMonths    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.stayMonths"
	KeyInactiveIncomeSource  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.incomeSource"
	KeyInactiveMonthlyIncome = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.monthlyIncome"
	KeyInactiveCompanyName   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.companyName"
	KeyInactiveBusinessType  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.businessType"
	KeyInactiveWorkType      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.workType"
	KeyInactiveJobPosition   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.position"
)

// Contract List Field ...
const (
	KeyInactiveContractName      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractName"
	KeyInactiveContractUrl       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractUrl"
	KeyInactiveContractTimestamp = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractTimestamp"
)

// Drawdown Account Info Field ...
const (
	KeyInactiveDrawdownAmount      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAmount"
	KeyInactiveDrawdownBankName    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankName"
	KeyInactiveDrawdownAccountName = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAccountName"
	KeyInactiveDrawdownBankCode    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankCode"
)

// Device Information Field ...
const (
	KeyInactiveDeviceID       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceID"
	KeyInactiveAdjustID       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.adjustID"
	KeyInactiveImei           = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.imei"
	KeyInactiveIpAddress      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.ipAddress"
	KeyInactiveLatitude       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.latitude"
	KeyInactiveLongitude      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.longitude"
	KeyInactiveAppPackageName = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.appPackageName"
	KeyInactiveAppVersion     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.appVersion"
	KeyInactiveDeviceBrand    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceBrand"
	KeyInactiveDeviceModel    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceModel"
	KeyInactiveOsSystem       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.osSystem"
	KeyInactiveOsVersion      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.osVersion"
)

// Section From Loan Overview ...
const (
	KeyInactiveLoanSummary       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary"
	KeyInactiveCreditInformation = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation"
)

// Loan Summary Field ...
const (
	KeyInactiveLoanProductID                  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanProductID"
	KeyInactiveLoanSuperbankLoanID            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanID"
	KeyInactiveLoanSuperbankLoanAccountNumber = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanAccountNumber"
	KeyInactiveLoanApplicationStatus          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanApplicationStatus"
	KeyInactiveLoanAccountStatus              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAccountStatus"
	KeyInactiveLoanPartnerName                = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanPartnerName"
	KeyInactiveLoanAkulakuLoanID              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAkulakuLoanID"
	KeyInactiveLoanAmount                     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAmount"
	KeyInactiveloanCommission                 = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCommission"
	KeyInactiveLoanTenor                      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanTenor"
	KeyInactiveLoanInterestRate               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanInterestRate"
	KeyInactiveLoanInterestAmount             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanInterestAmount"
	KeyInactiveLoanApplicationDate            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanApplicationDate"
	KeyInactiveLoanDisbursementDate           = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanDsibursementDate"
	KeyInactiveLoanFistDueDate                = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanFirstDueDate"
	KeyInactiveLoanNextDueDate                = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanNextDueDate"
	KeyInactiveLoanPurposeOfCredit            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanPurposeOfCredit"
	KeyInactiveLoanMaturityDate               = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanMaturityDate"
	KeyInactiveLoanCollectability             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCollectability"
	KeyInactiveLoanDpd                        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanDpd"
	KeyInactiveLoanRemainingOutstanding       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanRemainingOutstanding"
	KeyInactiveLoanNextInstallment            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanNextInstallment"
	KeyInactiveLoanAccountSubStatus           = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAccountSubStatus"
	KeyInactiveLoanCode                       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCode"
	KeyInactiveLoanReason                     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanReason"
)

// Credit Information Field ...
const (
	KeyInactiveCreditUserLevel = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation.creditUserLevel"
	KeyInactiveCreditUserType  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation.creditUserType"
)

// Table From Loan Overview ...
const (
	KeyInactiveInstallmentDetail   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail"
	KeyInactiveRepaymentDetail     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail"
	KeyInactiveVaGenerationHistory = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory"
)

// Installment Detail Field ...
const (
	KeyInactiveLoanInstallmentID        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentID"
	KeyInactiveLoanInstallmentNumber    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentNumber"
	KeyInactiveLoanDueDate              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanDueDate"
	KeyInactiveLoanTotalOutstanding     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanTotalOutstanding"
	KeyInactiveLoanPrincipalOutstanding = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanPrincipalOutstanding"
	KeyInactiveLoanInterestOutstanding  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInterestOutstanding"
	KeyInactiveLoanLateFeeOutstanding   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanLateFeeOutstanding"
	KeyInactiveLoanInstallmentDpd       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDpd"
	KeyInactiveLoanUpdatedAt            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanUpdatedAt"
	KeyInactiveLoanInstallmentStatus    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentStatus"
	KeyInactiveLoanInstallmentDetail    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail"
)

// Repayment Detail Field ...
const (
	KeyInactiveLoanRepaymentID              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentID"
	KeyInactiveLoanRepaymentDate            = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDate"
	KeyInactiveLoanVaNumber                 = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanVaNumber"
	KeyInactiveLoanVaIssuerName             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanIssuerName"
	KeyInactiveLoanTotalAmount              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanTotalAmount"
	KeyInactiveLoanRepaymentStatus          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentStatus"
	KeyInactiveLoanPgVaTransactionID        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanPgVaTransactionID"
	KeyInactiveLoanSuperbankVaTransactionID = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanSuperbankVaTransactionID"
	KeyInactiveLoanRepaymentDetail          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail"
)

// VA Generation History Field ...
const (
	KeyInactiveVaNumber              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaNumber"
	KeyInactiveVaIssuerName          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaIssuerName"
	KeyInactiveVaAmount              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaAmount"
	KeyInactiveVaGenerationTimestamp = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaGenerationTimestamp"
	KeyInactiveVaRequestTimestamp    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaRequestTimestamp"
	KeyInactiveVaExpiryDate          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaExpiryDate"
	KeyInactiveVaSendNotifTo         = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaSendNotifTo"
)

// Modal Installment Detail Field ...
const (
	KeyInactiveLoanDetailInstallmentID          = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentID"
	KeyInactiveLoanDetailUpdatedAt              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanUpdatedAt"
	KeyInactiveLoanDetailInsuranceAmount        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInsuranceAmount"
	KeyInactiveLoanDetailDueDate                = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanDueDate"
	KeyInactiveLoanDetailPrincipalInstallment   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalInstallment"
	KeyInactiveLoanDetailTotalInstallmentAmount = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalInstallmentAmount"
	KeyInactiveLoanDetailInstallmentDpd         = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentDpd"
	KeyInactiveLoanDetailInstallmentInterest    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentInterest"
	KeyInactiveLoanDetailInstallmentStatus      = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentStatus"
	KeyInactiveLoanDetailTotalOutstanding       = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalOutstanding"
	KeyInactiveLoanDetailOutstandingInsurance   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInsurance"
	KeyInactiveLoanDetailPrincipalOutstanding   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalOutstanding"
	KeyInactiveLoanDetailLateFeeOutstanding     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanLateFeeOutstanding"
	KeyInactiveLoanDetailOutstandingInterest    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInterest"
	KeyInactiveLoanDetailAdminFeeOutstanding    = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanAdminFeeOutstanding"
)

// Modal Repayment Detail Field ...
const (
	KeyInactiveRepaymentDate                     = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentDate"
	KeyInactiveRepaymentTotalAmount              = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentTotalAmount"
	KeyInactiveRepaymentSuperbankVaTransactionID = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentSuperbankVaTransactionID"
	KeyInactiveRepaymentVaNumber                 = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaNumber"
	KeyInactiveRepaymentStatus                   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentStatus"
	KeyInactiveRepaymentVaIssuerName             = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaIssuerName"
	KeyInactiveRepaymentPgVaTransactionID        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentPgVaTransactionID"
	KeyInactiveInstallmentList                   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList"
)

// Table Installment List Repayment Detail
const (
	KeyInactiveRepaymentInstallmentNumber        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentNumber"
	KeyInactiveRepaymentInstallmentAmount        = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAmount"
	KeyInactiveRepaymentInstallmentPrincipalPaid = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentPrincipalPaid"
	KeyInactiveRepaymentInstallmentInterestPaid  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInterestPaid"
	KeyInactiveRepaymentInstallmentAdminFeePaid  = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAdminFeePaid"
	KeyInactiveRepaymentInstallmentLateFeePaid   = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentLateFeePaid"
	KeyInactiveRepaymentInstallmentInsurancePaid = "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInsurancePaid"
)
