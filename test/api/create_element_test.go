package api

import (
	"encoding/json"
	"errors"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Create Element", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("CreateElement", func() {
		When("create element", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "Test Element",
					"moduleID": 1,
					"defaultPriorityID": 1,
					"code": "code",
					"status": 1,
					"hasTicketing": true,
					"ticketChains": [
						{
							"name": "tiketchain_0",
							"prevStatusID": 1,
							"nextStatusID": 2,
							"permissionsIDs": [1, 2, 3],
							"actionName": "action_name"
						}
					],
					"defaultCustomerSegmentID": 1,
					"defaultTicketRequestorID": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsElementDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByCode)).
					WithArgs(element.Code).
					WillReturnRows(sqlmock.NewRows([]string{"name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesInsertElement)).WithArgs(
					element.Name, sqlmock.AnyArg(), element.CreatedBy.Int64, sqlmock.AnyArg(), element.UpdatedBy.Int64, element.ModuleID, element.DefaultPriorityID, element.Code, element.Status, element.HasTicketing, element.DefaultTicketRequestorID, element.DefaultCustomerSegmentID).WillReturnResult(
					sqlmock.NewResult(1, 1))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetTicketChainByElementID)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "created_by", "updated_at", "updated_by", "current_status_id", "next_status_id", "element_id", "action_name", "bitwise_required"}))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetPermissionByModuleID)).
					WithArgs(element.ModuleID).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "bitwise_value", "description", "module_id", "status", "created_at", "updated_at", "created_by", "updated_by"}))

				ticketChain := resources.SampleDataTicketChain()
				mocker.ExpectExec(regexp.QuoteMeta(TesCreateInsertTicketChain)).WithArgs(
					sqlmock.AnyArg(), int64(1), ticketChain.CurrentStatusID, ticketChain.NextStatusID, ticketChain.ElementID, ticketChain.ActionName, ticketChain.BitwiseRequired).WillReturnResult(
					sqlmock.NewResult(1, 1))

				expectedResponse := `{
            "id": 1
          }`

				res, err := client.Post(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("element code already exists", func() {
			It("should return bad request error", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "Test Element",
					"moduleID": 1,
					"defaultPriorityID": 1,
					"code": "code",
					"status": 1,
					"hasTicketing": true,
					"ticketChains": [
						{
							"name": "tiketchain_0",
							"prevStatusID": 1,
							"nextStatusID": 2,
							"permissionsIDs": [1, 2, 3],
							"actionName": "action_name"
						}
					],
					"defaultCustomerSegmentID": 1,
					"defaultTicketRequestorID": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsElementDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByCode)).
					WithArgs(element.Code).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "module_id", "code", "status", "created_at", "created_by", "updated_at", "updated_by", "default_priority_id", "has_ticketing", "default_customer_segment_id", "default_ticket_requestor_id"}).
						AddRow(element.ID, element.Name, element.ModuleID, element.Code, element.Status, element.CreatedAt.Time, element.CreatedBy.Int64, element.UpdatedAt.Time, element.UpdatedBy.Int64, element.DefaultPriorityID, element.HasTicketing, element.DefaultCustomerSegmentID, element.DefaultTicketRequestorID))

				expectedResponse := `{
					"code": "badRequest",
					"message": "failed to create element",
					"errors": [
					{
						"errorCode": "badRequest",
						"message": "element with code 'code' already exists"
					}
					]
				}`

				res, err := client.Post(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res).ShouldNot(BeNil())
				Expect(res.StatusCode).Should(Equal(400))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user does not have permission", func() {
			It("should return unauthorized error", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "Test Element",
					"moduleID": 1,
					"defaultPriorityID": 1,
					"code": "code",
					"status": 1,
					"hasTicketing": true,
					"ticketChains": [
						{
							"name": "tiketchain_0",
							"prevStatusID": 1,
							"nextStatusID": 2,
							"permissionsIDs": [1, 2, 3],
							"actionName": "action_name"
						}
					],
					"defaultCustomerSegmentID": 1,
					"defaultTicketRequestorID": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserNoPermissionDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "forbidden",
					"message": "failed to create element",
					"errors": [
					{
						"errorCode": "forbidden",
						"message": "User is not authorized to perform this element action"
					}
					]
				}`

				res, err := client.Post(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res).ShouldNot(BeNil())
				Expect(res.StatusCode).Should(Equal(403))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("database connection fails", func() {
			It("should return internal server error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "Test Element",
					"moduleID": 1,
					"defaultPriorityID": 1,
					"code": "code",
					"status": 1,
					"hasTicketing": true,
					"ticketChains": [
						{
							"name": "tiketchain_0",
							"prevStatusID": 1,
							"nextStatusID": 2,
							"permissionsIDs": [1, 2, 3],
							"actionName": "action_name"
						}
					],
					"defaultCustomerSegmentID": 1,
					"defaultTicketRequestorID": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(nil, errors.New("test error"))

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsElementDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "internalServerError",
					"message": "failed to create element",
					"errors": [
					{
						"errorCode": "internalServerError",
						"message": "test error"
					}
					]
				}`

				res, err := client.Post(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res).ShouldNot(BeNil())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("create element fails", func() {
			It("should return internal server error", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "Test Element",
					"moduleID": 1,
					"defaultPriorityID": 1,
					"code": "code",
					"status": 1,
					"hasTicketing": true,
					"ticketChains": [
						{
							"name": "tiketchain_0",
							"prevStatusID": 1,
							"nextStatusID": 2,
							"permissionsIDs": [1, 2, 3],
							"actionName": "action_name"
						}
					],
					"defaultCustomerSegmentID": 1,
					"defaultTicketRequestorID": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsElementDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByCode)).
					WithArgs(element.Code).
					WillReturnRows(sqlmock.NewRows([]string{"name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesInsertElement)).WithArgs(
					element.Name, sqlmock.AnyArg(), element.CreatedBy.Int64, sqlmock.AnyArg(), element.UpdatedBy.Int64, element.ModuleID, element.DefaultPriorityID, element.Code, element.Status, element.HasTicketing, element.DefaultTicketRequestorID, element.DefaultCustomerSegmentID).
					WillReturnError(errors.New("test error"))

				expectedResponse := `{
					"code": "internalServerError",
					"message": "failed to create element",
					"errors": [
					{
						"errorCode": "internalServerError",
						"message": "test error"
					},
					{
						"errorCode": "internalServerError",
						"message": "failed to create element"
					}
					]
				}`

				res, err := client.Post(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
