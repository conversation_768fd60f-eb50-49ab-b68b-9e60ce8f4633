// Package customerexperiencehttp provides functionalities to call customer-experience services
package customerexperiencehttp

import (
	"context"
	"net/http"
	"strings"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	getVideoCallOpsDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "getVideoCallOps",
		Method:  http.MethodGet,
		Path:    "/api/v1/video-call/ops/:applicationID",
	}
)

// VideoCallRecordingFile represents a video recording with its type and URL.
type VideoCallRecordingFile struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

// VideoCallDetails contains information about the video call session.
type VideoCallDetails struct {
	Purpose          string                 `json:"purpose"`
	Description      string                 `json:"description"`
	Reason           string                 `json:"reason"`
	Assignee         string                 `json:"assignee"`
	VideoCallCreated string                 `json:"videoCallCreated"`
	VideoCallEnded   string                 `json:"videoCallEnded"`
	Duration         int                    `json:"duration"`
	RecordingFile    VideoCallRecordingFile `json:"recordingFile"`
}

// VideoCallApprovalHistory captures approval actions for a video call request.
type VideoCallApprovalHistory struct {
	User      string `json:"user"`
	Ticket    string `json:"ticket"`
	Role      string `json:"role"`
	Status    string `json:"status"`
	Notes     string `json:"notes"`
	Timestamp string `json:"timestamp"`
}

// VideoCallSupportingDocument represents a document that supports a ticket.
type VideoCallSupportingDocument struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

// VideoCallTicket represents a video call ticket and its associated data.
type VideoCallTicket struct {
	CustomerID          string                        `json:"customerID"`
	ApplicationID       string                        `json:"applicationID"`
	TicketNumber        string                        `json:"ticketNumber"`
	TicketStatus        string                        `json:"ticketStatus"`
	SupportingDocuments []VideoCallSupportingDocument `json:"supportingDocuments"`
	ApprovalHistory     []VideoCallApprovalHistory    `json:"approvalHistory"`
	Questions           []interface{}                 `json:"questions"` // Replace with actual type if available
	Details             VideoCallDetails              `json:"details"`
}

// GetVideoCallOpsResponse wraps the result of a GetVideoCallOps request.
type GetVideoCallOpsResponse struct {
	Items []VideoCallTicket
}

const (
	// UserIDHeaderKey is the HTTP header key for the user ID.
	UserIDHeaderKey = "X-Grab-Id-Userid"
)

// replacePathParam replaces the placeholder in the path with the actual value.
func replacePathParam(path, key, value string) string {
	placeholder := ":" + key
	return strings.Replace(path, placeholder, value, 1)
}

// GetVideoCallOps fetches video call operations based on the given application ID and user ID.
func (c *CustomerExperienceClient) GetVideoCallOps(ctx context.Context, applicationID string, userID string) (*GetVideoCallOpsResponse, error) {
	slog.FromContext(ctx).Info(logTag, "calling videoCall GetVideoCallOps")

	var tickets []VideoCallTicket
	ctx = klient.MakeContext(ctx, getVideoCallOpsDescriptor)

	headers := http.Header{
		UserIDHeaderKey: []string{userID},
	}

	getVideoCallOpsDescriptor.Path = replacePathParam(getVideoCallOpsDescriptor.Path, "applicationID", applicationID)

	req := &klientRequest{
		ctx:            ctx,
		descriptor:     getVideoCallOpsDescriptor,
		requestHeaders: headers,
	}

	err := c.machinery.RoundTrip(ctx, req, &klientResponse{responseBody: &tickets})

	kResp := &GetVideoCallOpsResponse{
		Items: tickets,
	}

	if err != nil {
		slog.FromContext(ctx).Info(logTag, "failed to call videoCall GetVideoCallOps", slog.Error(err))
		return kResp, err
	}

	return kResp, nil
}
