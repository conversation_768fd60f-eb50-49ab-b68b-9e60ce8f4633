package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"

	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	loanAppServiceAPI "gitlab.myteksi.net/dakota/lending/loan-app/api"
	loanExpServiceAPI "gitlab.myteksi.net/dakota/lending/loan-exp/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p process) mappingLendingRelatedData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	var err error

	switch req.Key {
	case constants.KeyLendingPAS:
		result, err = p.GetAccountListCustomerSearch(ctx, req)
	case constants.KeyOnboardingLoan:
		result, err = p.GetLoanOnboardingApplication(ctx, req)
	case constants.KeyAddressLoan:
		result, err = p.GetLoanAddress(ctx, req)
	case constants.KeyEmergencyContact:
		result, err = p.GetEmergencyContact(ctx, req)
	case constants.KeyLocInformation:
		result, err = p.GetLOCInformation(ctx, req)
	case constants.KeyActiveLoanList:
		result, err = p.GetActiveLoanList(ctx, req)
	case constants.KeyActiveLoanListDetails:
		result, err = p.GetLoanDetailInformation(ctx, req)
	case constants.KeyPortfolioReviewLog:
		return nil, nil
	// TODO: case for portfolio management
	case constants.KeyLocBlockTable:
		result, err = p.GetLOCBlockData(ctx, req)
	case constants.KeyTermActiveLoanList, constants.KeyInactiveLoanList:
		result = p.getLoanListData(ctx, req)
	case constants.KeyGroups.LendingTermLoanTab[req.Key]:
		result, err = p.mappingDetailLoanAccountData(ctx, req)
	}
	if err != nil {
		return nil, err
	}
	if result == nil {
		return result, nil
	}

	switch req.IdentifierType {
	case api.IdentifierType_SAFE_ID:
		result[constants.DataTypeSafeID] = req.Identifier
	case api.IdentifierType_CIF:
		result[constants.DataTypeCif] = req.Identifier
	}

	return result, nil
}

// nolint:funlen
func (p process) GetLoanOnboardingApplication(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	cif := req.Payload["cif"]
	if cif == "" {
		return nil, nil
	}

	ktp := req.Payload["KTP"]
	var phoneNumber string
	if ktp == "" {
		// Get customer by identifier using ktp
		customerData, err := p.GetCustomerByIdentifier(ctx, cif, string(api.IdentifierType_CIF), 1)
		if err != nil {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "invalid customer")
		}

		if customerData == nil || len(customerData.Items) == 0 {
			return nil, nil
		}

		ktp = customerData.Items[0].Customer.NIK

		if len(customerData.Items[0].Customer.Contacts) > 0 {
			phoneNumber = customerData.Items[0].Customer.Contacts[0].PhoneNumber
		}
	}

	var (
		loanAppResp    *loanAppServiceAPI.GetFlexiTermLoanApplicationByIdentifierResponse
		loanAppErr     error
		coolPeriodResp *loanAppServiceAPI.GetApplicantCoolingPeriodDetailsResponse
		coolPeriodErr  error
		whitelistResp  *loanAppServiceAPI.WhitelistApplicantData
		whitelistErr   error
		wg             sync.WaitGroup
	)

	wg.Add(3)

	// Get loan application data
	go func() {
		defer wg.Done()
		loanAppResp, loanAppErr = helper.GetLoanAppByIdentifier(ctx, ktp, "KTP", p.LoanAppClient, constants.CustomerSearchLogTag)
	}()

	// Get cooling period data
	go func() {
		defer wg.Done()
		coolPeriodResp, coolPeriodErr = helper.GetApplicantCoolingPeriod(ctx, cif, constants.ProductVariantCodeLOC, p.LoanAppClient, constants.CustomerSearchLogTag)
	}()

	// Get whitelist data
	go func() {
		defer wg.Done()
		whitelistResp, whitelistErr = helper.GetWhitelistApplicant(ctx, cif, phoneNumber, ktp, p.LoanAppClient, constants.CustomerSearchLogTag)
	}()

	wg.Wait()

	// Check for errors
	if loanAppErr != nil {
		return nil, loanAppErr
	}
	if loanAppResp == nil || len(loanAppResp.LoanApplications) == 0 {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "no loan application data", utils.GetTraceID(ctx))
	}

	if coolPeriodErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get cooling period: "+coolPeriodErr.Error(), utils.GetTraceID(ctx))
	}

	if whitelistErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get whitelist: "+whitelistErr.Error(), utils.GetTraceID(ctx))
	}

	return mappingLoanOnboardingApplication(loanAppResp, coolPeriodResp, whitelistResp)
}

// nolint:funlen,gocognit
func mappingLoanOnboardingApplication(data *loanAppServiceAPI.GetFlexiTermLoanApplicationByIdentifierResponse, coolPeriod *loanAppServiceAPI.GetApplicantCoolingPeriodDetailsResponse, whitelistResp *loanAppServiceAPI.WhitelistApplicantData) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// Handle whitelist data even if loan application data is nil
	var whitelistStatus, whitelistChannel, whitelistTimestamp string
	if whitelistResp != nil {
		if whitelistResp.Whitelisted != nil {
			if *whitelistResp.Whitelisted {
				whitelistStatus = "Whitelisted"
			} else {
				whitelistStatus = "Not Whitelisted"
			}
		}
		whitelistChannel = whitelistResp.Channel
		whitelistTimestamp = whitelistResp.UpdatedAt.String()
	}

	// Handle cooling period data even if loan application data is nil
	var coolOffPeriod int
	var coolOffEndDate string
	if coolPeriod != nil {
		coolOffEndDate = coolPeriod.UpdatedAt.String()
	}

	// Add whitelist and cooling period data to result
	loanOnboardingInfo := map[string]interface{}{
		constants.KeyOnboardingLoanCooloffPeriod:        coolOffPeriod,
		constants.KeyOnboardingLoanCooloffPeriodEndDate: coolOffEndDate,
		constants.KeyOnboardingLoanWhitelistedStatus:    whitelistStatus,
		constants.KeyOnboardingLoanWhitelistedChannel:   whitelistChannel,
		constants.KeyOnboardingLoanWhitelistedTimestamp: whitelistTimestamp,
	}

	// If loan application data exists, append to result
	if data != nil && len(data.LoanApplications) > 0 {
		sort.Slice(data.LoanApplications, func(i, j int) bool {
			return data.LoanApplications[i].CreatedAt.After(data.LoanApplications[j].CreatedAt)
		})
		applicationData := data.LoanApplications[0]
		applicationDetails := applicationData.ApplicationDetails
		isExistingCustomer := "NTB"
		loanOnboardingInfo[constants.KeyOnboardingLoanCustomerType] = isExistingCustomer

		if len(applicationDetails.Applicants) > 0 {
			applicant := applicationDetails.Applicants[0]

			if applicant.ExistingCustomer {
				isExistingCustomer = "ETB"
			}

			var consentID string
			if len(applicant.Consent.Id) > 0 {
				if id, ok := constants.ConsentMap[applicant.Consent.Id[len(applicant.Consent.Id)-1:]]; ok {
					consentID = id
				}
			}

			education, ok := constants.EducationMap[int(applicant.Education)]
			if !ok {
				education = ""
			}

			loanOnboardingInfo[constants.KeyOnboardingLoanApplicationID] = applicationDetails.ApplicationID
			loanOnboardingInfo[constants.KeyOnboardingLoanApplicationSubmitted] = applicationDetails.AppCreationDate
			loanOnboardingInfo[constants.KeyOnboardingLoanLastModifiedDate] = applicationData.UpdatedAt
			loanOnboardingInfo[constants.KeyOnboardingLoanCustomerType] = isExistingCustomer
			loanOnboardingInfo[constants.KeyOnboardingLoanCustomerEducation] = education
			loanOnboardingInfo[constants.KeyOnboardingLoanApplicationStatus] = applicationData.Status
			loanOnboardingInfo[constants.KeyOnboardingLoanStatusReason] = applicationData.StatusReason
			loanOnboardingInfo[constants.KeyOnboardingLoanChannel] = applicationDetails.Channel
			loanOnboardingInfo[constants.KeyOnboardingLoanConsentID] = consentID
			loanOnboardingInfo[constants.KeyOnboardingLoanConsentTimestamp] = applicant.Consent.Timestamp
		}
	}

	// Add non-empty values to result
	for k, v := range loanOnboardingInfo {
		if v != "" && v != nil {
			result[k] = v
		}
	}

	return result, nil
}

func (p process) GetLoanAddress(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// get address
	resp, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), 1)
	if err != nil {
		return nil, err
	}

	if len(resp.Items) == 0 {
		return nil, nil
	}

	return mappingLoanAddress(resp.Items[0].Customer.Addresses)
}

func mappingLoanAddress(addresses []customerExperienceAPI.Addresses) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	if len(addresses) == 0 {
		return result, nil
	}

	findAddressByType := func(addressType string) *customerExperienceAPI.Addresses {
		for _, addr := range addresses {
			if strings.EqualFold(string(addr.AddressType), addressType) {
				return &addr
			}
		}
		return nil
	}

	// First will try to find domicile address, if empty then will use mailing address
	var selectedAddress *customerExperienceAPI.Addresses
	if domicileAddr := findAddressByType("DOMICILE"); domicileAddr != nil {
		selectedAddress = domicileAddr
	} else if mailingAddr := findAddressByType("MAILING"); mailingAddr != nil {
		selectedAddress = mailingAddr
	}

	// If no address found, return empty result
	if selectedAddress == nil {
		return result, nil
	}

	addressInfo := map[string]interface{}{
		constants.KeyAddressLoanStreet:      selectedAddress.Street,
		constants.KeyAddressLoanRT:          selectedAddress.Rt,
		constants.KeyAddressLoanRW:          selectedAddress.Rw,
		constants.KeyAddressLoanKelurahan:   selectedAddress.Village,
		constants.KeyAddressLoanKecamatan:   selectedAddress.Subdistrict,
		constants.KeyAddressLoanKota:        selectedAddress.City,
		constants.KeyAddressLoanProvinsi:    selectedAddress.Province,
		constants.KeyAddressLoanKodePos:     selectedAddress.PostalCode,
		constants.KeyAddressLoanAddressType: selectedAddress.AddressType,
	}

	for k, v := range addressInfo {
		if v != "" && v != nil {
			result[k] = v
		}
	}

	return result, nil
}

func (p process) GetEmergencyContact(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	cif := req.Payload["cif"]
	if cif == "" {
		return nil, nil
	}

	ktp := req.Payload["KTP"]
	if ktp == "" {
		// Get customer by identifier using ktp
		customerData, err := p.GetCustomerByIdentifier(ctx, cif, string(api.IdentifierType_CIF), 1)
		if err != nil {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "invalid customer")
		}

		if customerData == nil || len(customerData.Items) == 0 {
			return nil, nil
		}

		ktp = customerData.Items[0].Customer.NIK
	}

	resp, err := helper.GetLoanAppByIdentifier(ctx, ktp, "KTP", p.LoanAppClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, nil
	}

	if len(resp.LoanApplications) == 0 {
		return nil, nil
	}

	return mappingEmergencyContact(resp)
}

func mappingEmergencyContact(resp *loanAppServiceAPI.GetFlexiTermLoanApplicationByIdentifierResponse) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	if resp == nil || len(resp.LoanApplications) == 0 {
		return result, nil
	}

	applicationDetails := resp.LoanApplications[0].ApplicationDetails
	if len(applicationDetails.Applicants) == 0 {
		return result, nil
	}

	emergencyContact := applicationDetails.Applicants[0]
	contactInfo := map[string]interface{}{
		constants.KeyEmergencyContactName:         emergencyContact.EmergencyContactName,
		constants.KeyEmergencyContactMobileNumber: emergencyContact.EmergencyContactMobileNumber,
		constants.KeyEmergencyContactRelationship: emergencyContact.EmergencyContactRelationship,
	}

	for k, v := range contactInfo {
		if v != "" && v != nil {
			result[k] = v
		}
	}

	return result, nil
}

// GetLOCInformation ...
// nolint:funlen
func (p process) GetLOCInformation(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// Need to provide LOC Account ID, safe ID, CIF, and active loan account ID
	accountID := req.Payload["locAccountID"]
	if accountID == "" {
		return nil, nil
	}

	safeID := req.Payload["safeID"]
	if safeID == "" {
		return nil, nil
	}

	activeLoanAccountID := req.Payload["activeLoanAccountID"]

	cif := req.Payload["cif"]
	if cif == "" {
		return nil, nil
	}

	var (
		resp            *loanExpServiceAPI.AccountDetailsResponse
		respErr         error
		insight         *loanExpServiceAPI.FetchCustomerInsightsForCRMResponse
		insightErr      error
		holdcode        *loanExpServiceAPI.GetHoldCodesResponse
		holdcodeErr     error
		instructions    []helper.LoanInstruction
		instructionsErr error
		wg              sync.WaitGroup
	)

	wg.Add(4)

	// Fetch account details
	go func() {
		defer wg.Done()
		resp, respErr = p.CachedFetchAccountDetails(ctx, accountID, constants.ProductVariantLineOfCredit, safeID)
	}()

	// Get insight data
	go func() {
		defer wg.Done()
		insight, insightErr = helper.GetCustomerInsights(ctx, accountID, constants.ProductVariantLineOfCredit, safeID, p.LoanExpClient, constants.CustomerSearchLogTag)
	}()

	// Get hold code
	go func() {
		defer wg.Done()
		holdcode, holdcodeErr = helper.GetLOCHoldCode(ctx, accountID, constants.ProductVariantLineOfCredit, safeID, p.LoanExpClient, constants.CustomerSearchLogTag)
	}()

	// Get all loan instructions
	go func() {
		defer wg.Done()
		instructions, instructionsErr = helper.GetLoanInstructions(ctx, constants.ProductVariantLineOfCredit, constants.InstructionTypeBlockUnblock, p.ProductMasterClient, constants.CustomerSearchLogTag)
	}()

	wg.Wait()

	// Check for errors
	if respErr != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, fmt.Sprintf("failed to fetch account details: %s", respErr.Error()), utils.GetTraceID(ctx))
		return nil, respErr
	}
	if resp == nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "no account details available", utils.GetTraceID(ctx))
		return nil, errorwrapper.Error(apiError.ResourceNotFound, "no loan application data")
	}

	if insightErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get customer insights: "+insightErr.Error(), utils.GetTraceID(ctx))
	}
	if holdcodeErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get hold code: "+holdcodeErr.Error(), utils.GetTraceID(ctx))
	}
	if instructionsErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get loan instructions: "+instructionsErr.Error(), utils.GetTraceID(ctx))
	}

	result := make(map[string]interface{})
	result[constants.DataTypeCif] = cif
	result[constants.DataTypeSafeID] = safeID
	p.mappingLOCInformation(result, resp, insight, holdcode, instructions, activeLoanAccountID)

	return result, nil
}

// nolint:funlen
func (p process) mappingLOCInformation(result map[string]interface{}, locData *loanExpServiceAPI.AccountDetailsResponse, insight *loanExpServiceAPI.FetchCustomerInsightsForCRMResponse, holdcode *loanExpServiceAPI.GetHoldCodesResponse, instructions []helper.LoanInstruction, activeLoanAccountID string) {
	result[constants.KeyLocProductVariant] = locData.ProductVariantCode
	result[constants.KeyLocAccountID] = locData.AccountID
	result[constants.KeyLocStatus] = locData.CurrentStatus
	result[constants.KeyLocCreditLimitCreatedDate] = locData.CreatedAt
	result[constants.KeyLocSubStatus] = locData.SubStatus

	countOfActiveLoan := len(utils.SplitAndTrim(activeLoanAccountID, ","))
	result[constants.KeyLocCountOfActiveLoan] = countOfActiveLoan

	if locData.Parameters == nil {
		return
	}

	parameters := locData.Parameters

	if closedAt, exists := parameters["closedAt"]; exists && closedAt != "" {
		result[constants.KeyLocCloseLocTimestamp] = closedAt
	}

	if closedReasons, exists := parameters["closedReasons"].([]string); exists && len(closedReasons) > 0 {
		result[constants.KeyLocOpsReasonForCloseLOC] = closedReasons
	}

	result[constants.KeyLocTotalCreditLimit] = getMonetaryValue(parameters, "offeredLOC")
	result[constants.KeyLocAvailableCreditLimit] = getMonetaryValue(parameters, "availableLOC")
	result[constants.KeyLocTotalRemainingPayable] = getMonetaryValue(parameters, "totalPendingRepaymentAmount")

	currentDue, _ := parameters["currentDueAmountWithDueDate"].(map[string]interface{})
	if currentDue != nil {
		result[constants.KeyLocTotalNextInstallment] = getMonetaryValue(currentDue, "dueAmount")
		if dueDate, ok := currentDue["dueDate"].(string); ok {
			result[constants.KeyLoanDetailNextInstallmentDueDate] = dueDate
		}
	}

	if insight != nil {
		if insight.DueInDays != 0 {
			nextDueDate := int(insight.DueInDays)
			result[constants.KeyLocNextDueDate] = time.Now().AddDate(0, 0, nextDueDate).Format("02 Jan 2006")
		}

		if insight.TotalDueAmount != nil {
			result[constants.KeyLocTotalNextInstallment] = utils.HumanizeBalance(insight.TotalDueAmount.Val, true)
		}

		if insight.TotalPendingRepaymentAmount != nil {
			result[constants.KeyLocTotalRemainingPayable] = utils.HumanizeBalance(insight.TotalPendingRepaymentAmount.Val, true)
		}
	}

	if offeredInterestRate, ok := parameters["offeredInterestRate"].(float64); ok {
		result[constants.KeyLocFlatRate] = utils.FormatInterestValue(fmt.Sprint(offeredInterestRate))
	}

	if offeredMaxTenor, ok := parameters["offeredMaxTenor"].(float64); ok {
		result[constants.KeyLocMaxTenor] = offeredMaxTenor
	}

	if offeredEIR, ok := parameters["offeredEIR"].(float64); ok {
		result[constants.KeyLocEffectiveRate] = utils.FormatInterestValue(fmt.Sprint(offeredEIR))
	}

	if expiryDate, ok := parameters["expiryDate"].(string); ok {
		result[constants.KeyLocExpiryDate] = expiryDate
	}

	if holdcode != nil {
		blockInfo := processHoldCodes(holdcode, instructions)
		result[constants.KeyLocBlockStatus] = blockInfo.BlockStatus
	}
}

// GetLOCBlockData is to get LOC Block table data
func (p process) GetLOCBlockData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["locAccountID"]
	if accountID == "" {
		return nil, nil
	}

	safeID := req.Payload["safeID"]
	if safeID == "" {
		return nil, nil
	}

	cif := req.Payload["cif"]
	if cif == "" {
		return nil, nil
	}

	var (
		holdcode        *loanExpServiceAPI.GetHoldCodesResponse
		holdcodeErr     error
		instructions    []helper.LoanInstruction
		instructionsErr error
		wg              sync.WaitGroup
	)

	wg.Add(2)

	// Get hold code
	go func() {
		defer wg.Done()
		holdcode, holdcodeErr = helper.GetLOCHoldCode(ctx, accountID, constants.ProductVariantLineOfCredit, safeID, p.LoanExpClient, constants.CustomerSearchLogTag)
	}()

	// Get all loan instructions
	go func() {
		defer wg.Done()
		instructions, instructionsErr = helper.GetLoanInstructions(ctx, constants.ProductVariantLineOfCredit, constants.InstructionTypeBlockUnblock, p.ProductMasterClient, constants.CustomerSearchLogTag)
	}()

	wg.Wait()

	if holdcodeErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get hold code: "+holdcodeErr.Error(), utils.GetTraceID(ctx))
		return nil, nil
	}
	if instructionsErr != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "failed to get loan instructions: "+instructionsErr.Error(), utils.GetTraceID(ctx))
	}

	result := make(map[string]interface{})
	result[constants.DataTypeCif] = cif
	result[constants.DataTypeSafeID] = safeID
	p.mappingLOCBlockData(result, holdcode, instructions)

	return result, nil
}

func (p process) mappingLOCBlockData(result map[string]interface{}, holdcode *loanExpServiceAPI.GetHoldCodesResponse, instructions []helper.LoanInstruction) {
	if holdcode != nil {
		blockInfo := processHoldCodes(holdcode, instructions)
		var blockTableData []map[string]interface{}
		blockItem := make(map[string]interface{})

		if len(blockInfo.ReasonNames) > 0 {
			blockItem[constants.KeyBlockReasonCodeForBlockLOC] = strings.Join(blockInfo.ReasonNames, "\n")
		} else if len(blockInfo.ReasonCodes) > 0 {
			blockItem[constants.KeyBlockReasonCodeForBlockLOC] = strings.Join(blockInfo.ReasonCodes, "\n")
		}
		blockItem[constants.KeyBlockLOCMakerName] = holdcode.ModifiedBy

		blockTableData = append(blockTableData, blockItem)

		result[constants.KeyLocBlockTable] = blockTableData
	}
}

func (p process) GetActiveLoanList(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// Need to provide LOC Account ID, active loan account IDs, cif, and safe ID
	locAccountID := req.Payload["locAccountID"]
	if locAccountID == "" {
		return nil, nil
	}

	loanAccountIDs := req.Payload["loanAccountID"]
	if loanAccountIDs == "" {
		return nil, nil
	}

	safeID := req.Payload["safeID"]
	if safeID == "" {
		return nil, nil
	}

	cif := req.Payload["cif"]
	if cif == "" {
		return nil, nil
	}

	// Get loan details
	loanDetails := p.fetchLoanDetails(ctx, loanAccountIDs, locAccountID, safeID)

	result := make(map[string]interface{})
	result[constants.DataTypeCif] = cif
	result[constants.DataTypeSafeID] = safeID

	if len(loanDetails) > 0 {
		result[constants.KeyActiveLoanList] = loanDetails
	}

	return result, nil
}

// fetchLoanDetails retrieves and processes loan details for multiple loan accounts
func (p process) fetchLoanDetails(ctx context.Context, loanAccountIDs, locAccountID, safeID string) []map[string]interface{} {
	arrayLoanIDs := utils.SplitAndTrim(loanAccountIDs, ",")
	if len(arrayLoanIDs) == 0 {
		return nil
	}

	// Create a worker pool with max 5 workers
	maxWorkers := min(5, len(arrayLoanIDs))
	workChan := make(chan string, len(arrayLoanIDs))
	resultChan := make(chan map[string]interface{}, len(arrayLoanIDs))
	errorChan := make(chan error, len(arrayLoanIDs))

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for accountID := range workChan {
				// Process account
				loanDetail, processErr := p.processLoanAccount(ctx, accountID, locAccountID, safeID)
				if processErr != nil {
					errorChan <- fmt.Errorf("account %s: %w", accountID, processErr)
					continue
				}
				if loanDetail != nil {
					resultChan <- loanDetail
				}
			}
		}()
	}

	for _, accountID := range arrayLoanIDs {
		if accountID != "" {
			workChan <- accountID
		}
	}
	close(workChan)

	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	var loanDetails []map[string]interface{}
	for loanDetail := range resultChan {
		loanDetails = append(loanDetails, loanDetail)
	}

	for err := range errorChan {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	return loanDetails
}

func (p process) processLoanAccount(ctx context.Context, accountID, locAccountID, safeID string) (map[string]interface{}, error) {
	// Get loan account
	accountDetail, err := helper.GetAccountDetail(ctx, &accountServiceAPI.GetAccountRequest{AccountID: accountID}, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	if accountDetail.Account.ParentAccountID != locAccountID {
		return nil, nil
	}

	// Fetch account details using the cached function
	resp, err := p.CachedFetchAccountDetails(ctx, strings.TrimSpace(accountID), constants.ProductVariantLoanAccount, safeID)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		return nil, nil
	}

	return p.mappingLoanInformation(resp), nil
}

func (p process) mappingLoanInformation(loanData *loanExpServiceAPI.AccountDetailsResponse) map[string]interface{} {
	result := make(map[string]interface{})

	if loanData.Parameters == nil {
		return result
	}

	parameters := loanData.Parameters
	result[constants.KeyActiveLoanListLoanAccountNumber] = loanData.AccountID
	result[constants.KeyActiveLoanListLoanStatus] = loanData.CurrentStatus
	result[constants.KeyActiveLoanListLoanCreatedLoanAccount] = loanData.CreatedAt
	result[constants.KeyActiveLoanListLoanTotalRemainingOutstanding] = getMonetaryValue(parameters, "totalPendingRepaymentAmount")
	result[constants.KeyActiveLoanListLoanPaidAmount] = getMonetaryValue(parameters, "totalPaidAmount")

	if loanPurpose, ok := parameters["loanName"].(string); ok {
		result[constants.KeyActiveLoanListLoanPurpose] = loanPurpose
	}
	if collectability, ok := parameters["daysPastDueBucket"].(string); ok {
		result[constants.KeyActiveLoanListLoanCollectability] = collectability
	}

	if tenor, ok := parameters["tenorValue"]; ok {
		result[constants.KeyActiveLoanListLoanTenor] = tenor
	}
	if maturityDate, ok := parameters["lastInstallmentDueDate"].(string); ok {
		result[constants.KeyActiveLoanListLoanMaturityDate] = maturityDate
	}

	for k, v := range result {
		if v != "" && v != nil {
			result[k] = v
		}
	}

	return result
}

// nolint:funlen,gocognit
func processHoldCodes(holdcodes *loanExpServiceAPI.GetHoldCodesResponse, loanInstructions []helper.LoanInstruction) constants.BlockInfo {
	result := constants.BlockInfo{
		ReasonCodes: make([]string, 0),
		ReasonNames: make([]string, 0),
	}

	// Return if holdcodes is nil
	if holdcodes == nil {
		return result
	}

	// Get all reason codes from holdcodes
	var allReasonCodes []string
	if holdcodes.ReasonCodes != nil {
		allReasonCodes = holdcodes.ReasonCodes
	} else {
		allReasonCodes = []string{}
	}

	// Filter out specific reason codes
	filteredReasonCodes := []string{}
	if len(allReasonCodes) > 0 {
		for _, code := range allReasonCodes {
			if code != constants.LocBlockHoldCodeTemporaryBlocked &&
				code != constants.LocBlockHoldCodePermanentBlocked {
				filteredReasonCodes = append(filteredReasonCodes, code)
			}
		}
	}

	// Filter out temporary and permanent block codes
	var locReasonCodes []helper.LoanInstruction
	for _, instruction := range loanInstructions {
		if instruction.Code != constants.LocBlockHoldCodeTemporaryBlocked &&
			instruction.Code != constants.LocBlockHoldCodePermanentBlocked {
			locReasonCodes = append(locReasonCodes, instruction)
		}
	}

	// Deciding hold status
	if holdcodes.AccountID == "" {
		result.BlockStatus = ""
		result.BlockStatusCode = ""
		return result
	}

	// Check if contains permanent block code
	hasPermanentBlock := false
	hasTemporaryBlock := false

	for _, code := range allReasonCodes {
		if code == constants.LocBlockHoldCodePermanentBlocked {
			hasPermanentBlock = true
			break
		} else if code == constants.LocBlockHoldCodeTemporaryBlocked {
			hasTemporaryBlock = true
		}
	}

	if hasPermanentBlock {
		result.BlockStatus = constants.LocBlockHoldCodeNamePermanentBlocked
		result.BlockStatusCode = constants.LocBlockHoldCodePermanentBlocked
	} else if hasTemporaryBlock {
		result.BlockStatus = constants.LocBlockHoldCodeNameTemporaryBlocked
		result.BlockStatusCode = constants.LocBlockHoldCodeTemporaryBlocked
	} else {
		result.BlockStatus = constants.LocBlockStatusNotBlocked
		result.BlockStatusCode = ""
	}

	// Get isDrawdownBlocked
	result.IsDrawdownBlocked = holdcodes.IsDrawdownBlocked

	// Get reason names
	result.ReasonCodes = filteredReasonCodes
	for _, reasonCode := range filteredReasonCodes {
		for _, locReason := range locReasonCodes {
			if locReason.Code == reasonCode {
				result.ReasonNames = append(result.ReasonNames, locReason.Name)
				break
			}
		}
	}

	return result
}

// GetLoanDetailInformation ...
func (p process) GetLoanDetailInformation(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["loanAccountID"]
	if accountID == "" {
		return nil, nil
	}

	safeID := req.Payload["safeID"]
	if safeID == "" {
		return nil, nil
	}

	// Get loan account detail using the cached function
	resp, err := p.CachedFetchAccountDetails(ctx, strings.TrimSpace(accountID), constants.ProductVariantLoanAccount, safeID)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, fmt.Sprintf("failed to fetch account details: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	if resp == nil {
		return nil, nil
	}

	result := make(map[string]interface{})
	p.mappingLoanDetailInformation(result, resp)

	result[constants.DataTypeSafeID] = safeID

	return result, nil
}

// getMonetaryValue is a function to safely get and format monetary values
func getMonetaryValue(dataMap map[string]interface{}, key string) string {
	if val, ok := dataMap[key].(map[string]interface{}); ok {
		if amount, exists := val["val"].(float64); exists {
			return utils.HumanizeBalance(amount, true)
		}
	} else if val, ok := dataMap[key].(float64); ok {
		return utils.HumanizeBalance(val, true)
	}
	return "0"
}

// nolint:funlen,gocognit
func (p process) mappingLoanDetailInformation(result map[string]interface{}, loanData *loanExpServiceAPI.AccountDetailsResponse) {
	if loanData == nil {
		return
	}

	result[constants.KeyLoanDetailProductVariant] = loanData.ProductVariantCode
	result[constants.KeyLoanDetailAccountSubStatus] = loanData.SubStatus

	if loanData.Parameters == nil {
		return
	}

	parameters := loanData.Parameters

	var nextInstallmentAmount string
	var nextInstallmentDueDate string
	if currentDue, ok := parameters["currentDueAmountWithDueDate"].([]interface{}); ok && len(currentDue) > 0 {
		if dueInfo, ok := currentDue[0].(map[string]interface{}); ok {
			// Get due amount
			if dueAmount, ok := dueInfo["dueAmount"].(map[string]interface{}); ok {
				if val, ok := dueAmount["val"].(float64); ok {
					nextInstallmentAmount = utils.HumanizeBalance(val, true)
				}
			}
			// Get due date
			if dueDate, ok := dueInfo["dueDate"].(string); ok {
				nextInstallmentDueDate = dueDate
			}
		}
	}

	mappings := map[string]interface{}{
		constants.KeyLoanDetailDisbursementDate:       loanData.CreatedAt,
		constants.KeyLoanDetailDisbursementAmount:     getMonetaryValue(parameters, "principalAmount"),
		constants.KeyLoanDetailRemainingPayable:       getMonetaryValue(parameters, "totalPendingRepaymentAmount"),
		constants.KeyLoanDetailInterestSaved:          getMonetaryValue(parameters, "totalInterestSavingsAmount"),
		constants.KeyLoanDetailPreferredPaymentDate:   parameters["preferredRepaymentDayOfMonth"],
		constants.KeyLoanDetailNextInstallment:        nextInstallmentAmount,
		constants.KeyLoanDetailNextInstallmentDueDate: nextInstallmentDueDate,
		constants.KeyLoanDetailLastUpdatedDate:        loanData.UpdatedAt,
		constants.KeyLoanDetailRecoveryAmount:         getMonetaryValue(parameters, "recoveryAmount"),
		constants.KeyLoanDetailExpiryDate:             parameters["expiryDate"],
		constants.KeyLoanDetailDPD:                    parameters["daysPastDue"],
		// TODO: map write off when ticket already available
		// constants.KeyLoanDetailWriteOffDate:           parameters["writeOffDate"],
		// constants.KeyLoanDetailWriteOffAmount:         getMonetaryValue("writeOffAmount"),
		// constants.KeyLoanDetailWriteOffReasonCode:     parameters["writeOffReasonCode"],
	}

	// Handle installment details if present
	if installments, ok := parameters["installmentDetails"].([]interface{}); ok {
		var installmentDetails []map[string]interface{}
		for _, inst := range installments {
			if instMap, ok := inst.(map[string]interface{}); ok {
				status := instMap["status"]
				if statusStr, ok := status.(string); ok && strings.ToUpper(statusStr) == "PENDING" {
					status = constants.InstallmentStatusUnpaid
				}

				installmentDetail := map[string]interface{}{
					constants.KeyInstallmentDetailInstallmentNumber:    instMap["installmentSequenceNumber"],
					constants.KeyInstallmentDetailDueDate:              instMap["installmentDueDate"],
					constants.KeyInstallmentDetailTotalOutstanding:     getMonetaryValue(instMap, "installmentPendingBalance"),
					constants.KeyInstallmentDetailPrincipalOutstanding: getMonetaryValue(instMap, "principalPendingBalance"),
					constants.KeyInstallmentDetailInterestOutstanding:  getMonetaryValue(instMap, "normalInterestPendingBalance"),
					constants.KeyInstallmentDetailLateFeeOutstanding:   getMonetaryValue(instMap, "penalInterestPendingBalance"),
					constants.KeyInstallmentDetailInstallmentDPD:       instMap["daysPastDue"],
					constants.KeyInstallmentDetailUpdatedAt:            instMap["updatedAt"],
					constants.KeyInstallmentDetailStatus:               status,
					constants.KeyInstallmentDetailPaidAmount:           getMonetaryValue(instMap, "installmentBalancePaid"),
					constants.KeyInstallmentDetailPaidPrinciple:        getMonetaryValue(instMap, "principalPaid"),
					constants.KeyInstallmentDetailPaidInterest:         getMonetaryValue(instMap, "normalInterestPaid"),
					constants.KeyInstallmentDetailPaidLateFee:          getMonetaryValue(instMap, "penalInterestPaid"),
				}
				installmentDetails = append(installmentDetails, installmentDetail)
			}
		}
		if len(installmentDetails) > 0 {
			result[constants.KeyLoanDetailInstallmentDetails] = installmentDetails
		}
	}

	for key, value := range mappings {
		if value != nil && value != "" {
			result[key] = value
		}
	}
}

// CachedFetchAccountDetails wraps the FetchAccountDetails function with caching
func (p process) CachedFetchAccountDetails(ctx context.Context, accountID, productVariant, safeID string) (*loanExpServiceAPI.AccountDetailsResponse, error) {
	cacheKey := fmt.Sprintf(constants.FetchAccountDetailsRedisKey, accountID, productVariant, safeID)

	// Get from redis cache
	cachedData, err := redis.GetRedisValue(ctx, cacheKey, constants.CustomerSearchLogTag)
	if err == nil && cachedData != "" {
		var accountDetails loanExpServiceAPI.AccountDetailsResponse
		if unmarshalErr := json.Unmarshal([]byte(cachedData), &accountDetails); unmarshalErr == nil {
			slog.FromContext(ctx).Info(constants.CustomerSearchLogTag, fmt.Sprintf("Cache hit for account details: %s", cacheKey), utils.GetTraceID(ctx))
			return &accountDetails, nil
		}
	}

	// If no cache available, fetch account details
	resp, err := helper.FetchAccountDetails(ctx, accountID, productVariant, safeID, p.LoanExpClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	// Cache the result
	if resp != nil {
		jsonData, err := json.Marshal(resp)
		if err == nil {
			// Set redis value with 1 minutes expiration
			err = redis.SetRedisValue(ctx, cacheKey, string(jsonData), 60, constants.CustomerSearchLogTag)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, fmt.Sprintf("failed to cache account details: %s", err.Error()), utils.GetTraceID(ctx))
			}
		}
	}

	return resp, nil
}

func (p process) getLoanListData(_ context.Context, req *api.CustomerSearchRequest) map[string]interface{} {
	// TODO: Remove this mock data and use integration
	var datas []interface{}

	status := "INACTIVE"
	if req.Key == constants.KeyTermActiveLoanList {
		status = "ACTIVE"
	}

	limit := 10

	for i := 0; i < limit; i++ {
		loanAccountNo := fmt.Sprintf("%d", time.Now().UnixMilli())
		loanAccountNo = loanAccountNo[len(loanAccountNo)-5:]
		data := map[string]interface{}{
			"loanAccountNumber":    fmt.Sprintf("%d%s", i, loanAccountNo),
			"akulakuLoanID":        fmt.Sprintf("%d%s", i, loanAccountNo),
			"partner":              "Akulaku",
			"productType":          "Dana Cicil",
			"loanPeriod":           "3 Months",
			"interestRate":         "2.50%",
			"disbursementDate":     time.Now().UTC(),
			"maturityDate":         time.Now().UTC(),
			"principal":            3000000,
			"totalInterestAmount":  576000,
			"totalInstallmentFee":  81000,
			"outstandingPrincipal": 20000,
			"outstandingInterest":  288000,
			"outstandingFee":       90000,
			"installmentAmount":    253711,
			"nextInstallmentDate":  time.Now().UTC(),
			"overdueAmount":        12000,
			"collectibility":       1,
			"dpd":                  0,
			"loanStatus":           status,
		}
		datas = append(datas, data)
	}

	return composeLoanListData(datas, req.Key)
}

type FieldPair struct {
	activeKey   string
	inactiveKey string
	fieldName   string
}

func composeLoanListData(resp []interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}
	var fieldsMap = []FieldPair{
		{constants.KeyActiveLoanAccountNumber, constants.KeyInactiveLoanAccountNumber, "loanAccountNumber"},
		{constants.KeyActiveAkulakuLoanId, constants.KeyInactiveAkulakuLoanId, "akulakuLoanID"},
		{constants.KeyActivePartner, constants.KeyInactivePartner, "partner"},
		{constants.KeyActiveProductType, constants.KeyInactiveProductType, "productType"},
		{constants.KeyActiveLoanPeriod, constants.KeyInactiveLoanPeriod, "loanPeriod"},
		{constants.KeyActiveInterestRate, constants.KeyInactiveInterestRate, "interestRate"},
		{constants.KeyActiveDisbursementDate, constants.KeyInactiveDisbursementDate, "disbursementDate"},
		{constants.KeyActiveMaturityDate, constants.KeyInactiveMaturityDate, "maturityDate"},
		{constants.KeyActivePrincipal, constants.KeyInactivePrincipal, "principal"},
		{constants.KeyActiveTotalInterestAmount, constants.KeyInactiveTotalInterestAmount, "totalInterestAmount"},
		{constants.KeyActiveTotalInstallmentFee, constants.KeyInactiveTotalInstallmentFee, "totalInstallmentFee"},
		{constants.KeyActiveOutstandingPrincipal, constants.KeyInactiveOutstandingPrincipal, "outstandingPrincipal"},
		{constants.KeyActiveOutstandingInterest, constants.KeyInactiveOutstandingInterest, "outstandingInterest"},
		{constants.KeyActiveOutstandingFee, constants.KeyInactiveOutstandingFee, "outstandingFee"},
		{constants.KeyActiveInstallmentAmount, constants.KeyInactiveInstallmentAmount, "installmentAmount"},
		{constants.KeyActiveNextInstallmentDate, constants.KeyInactiveNextInstallmentDate, "nextInstallmentDate"},
		{constants.KeyActiveOverdueAmount, constants.KeyInactiveOverdueAmount, "overdueAmount"},
		{constants.KeyActiveCollectibility, constants.KeyInactiveCollectibility, "collectibility"},
		{constants.KeyActiveDpd, constants.KeyInactiveDpd, "dpd"},
		{constants.KeyActiveLoanStatus, constants.KeyInactiveLoanStatus, "loanStatus"},
	}

	fields = make([][]interface{}, 0, len(fieldsMap))
	isActive := resultKey == constants.KeyTermActiveLoanList
	for _, item := range fieldsMap {
		if isActive {
			fields = append(fields, []interface{}{item.activeKey, item.fieldName})
		} else {
			fields = append(fields, []interface{}{item.inactiveKey, item.fieldName})
		}
	}

	result := make(map[string]interface{})
	var finalData []map[string]interface{}
	for _, item := range resp {
		data, _ := item.(map[string]interface{})
		currentData := make(map[string]interface{})

		for _, field := range fields {
			targetKey := field[0].(string)
			sourceKey := field[1].(string)
			currentData[targetKey] = data[sourceKey]
		}

		finalData = append(finalData, currentData)
	}

	result[resultKey] = finalData
	return result
}

func (p process) mappingDetailLoanAccountData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	if req.Payload == nil && req.Payload["loanAccountNumber"] == "" {
		return nil, api.ResourceNotFoundError
	}

	switch req.Key {
	case constants.KeyActiveLoanDetail, constants.KeyInactiveLoanDetail:
		return p.getDetailLoanAccountData(ctx, req)
	case constants.KeyActiveCustomerInformation, constants.KeyInactiveCustomerInformation:
		return p.getDetailLoanCustomerInformationData(ctx, req)
	case constants.KeyActiveAddressDetail, constants.KeyInactiveAddressDetail:
		return p.getDetailLoanAddressDetailData(ctx, req)
	case constants.KeyActiveEmergencyContact, constants.KeyInactiveEmergencyContact:
		return p.getDetailLoanEmergencyContactData(ctx, req)
	case constants.KeyActiveWorkInformation, constants.KeyInactiveWorkInformation:
		return p.getDetailLoanWorkInformationData(ctx, req)
	case constants.KeyActiveContractList, constants.KeyInactiveContractList:
		return p.getDetailLoanContractListData(ctx, req)
	case constants.KeyActiveDrawdownAccountInfo, constants.KeyInactiveDrawdownAccountInfo:
		return p.getDetailLoanDrawdownAccountInfoData(ctx, req)
	case constants.KeyActiveDeviceInformation, constants.KeyInactiveDeviceInformation:
		return p.getDetailLoanDeviceInformationData(ctx, req)
	case constants.KeyActiveLoanSummary, constants.KeyInactiveLoanSummary:
		return p.getDetailLoanSummaryData(ctx, req)
	case constants.KeyActiveInstallmentDetail, constants.KeyInactiveInstallmentDetail:
		return p.getDetailInstallmentDetailData(ctx, req)
	case constants.KeyActiveRepaymentDetail, constants.KeyInactiveRepaymentDetail:
		return p.getDetailRepaymentDetailData(ctx, req)
	case constants.KeyActiveVaGenerationHistory, constants.KeyInactiveVaGenerationHistory:
		return p.getDetailVaGenerationHistoryData(ctx, req)
	case constants.KeyActiveCreditInformation, constants.KeyInactiveCreditInformation:
		return p.getDetailCreditInformationData(ctx, req)
	}

	return result, nil
}

func (p process) getDetailLoanAccountData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	switch req.Key {
	case constants.KeyActiveLoanDetail:
		data := map[string]interface{}{
			constants.KeyActivePartner:           "Akulaku",
			constants.KeyActiveProductType:       "Dana Cicil",
			constants.KeyActiveLoanAccountNumber: req.Payload["loanAccountNumber"],
		}

		result[constants.KeyActiveLoanDetail] = data
	case constants.KeyInactiveLoanDetail:
		data := map[string]interface{}{
			constants.KeyInactivePartner:           "Akulaku",
			constants.KeyInactiveProductType:       "Dana Cicil",
			constants.KeyInactiveLoanAccountNumber: req.Payload["loanAccountNumber"],
		}

		result[constants.KeyInactiveLoanDetail] = data
	}

	return result, nil
}

func (p process) getDetailLoanCustomerInformationData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"akulakuCustomerID":       "********",
		"superbankCustomerID":     "ID********987",
		"onboardingApplicationID": "d41979ba-dddc-4bc6-8977-be9fa770a958",
		"customerName":            "Gains B",
		"ktpNumber":               "187********90000",
		"phoneNumber":             "08********90",
		"email":                   "<EMAIL>",
		"nationality":             "Indonesia",
		"dateOfBirth":             time.Now().UTC(),
		"placeOfBirth":            "Surabaya",
		"maritalStatus":           "Marry",
		"gender":                  "Female",
		"education":               "S1",
		"npwpAvailability":        "Yes",
		"motherMaidenName":        "Umma",
		"riskLevel":               "90",
		"userLevel":               "A",
		"userType":                "[104,103]",
		"partnerSelfieImage":      "/uploads/selfieZNIkxtOFQn-2025-06-24.webp",
		"ktpFile":                 "/uploads/identitycard-fHdvfOaSSe-2025-06-24.webp",
		"selfieImage":             "/uploads/selfieZNIkxtOFQn-2025-06-24.webp",
		"onboardingImage":         "/uploads/selfieZNIkxtOFQn-2025-06-24.webp",
	}

	return composeLoanCustomerInformationData(data, req.Key), nil
}

func composeLoanCustomerInformationData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	var customerFieldsMap = []FieldPair{
		{constants.KeyActiveAkulakuCustomerID, constants.KeyInactiveAkulakuCustomerID, "akulakuCustomerID"},
		{constants.KeyActiveSuperbankCustomerID, constants.KeyInactiveSuperbankCustomerID, "superbankCustomerID"},
		{constants.KeyActiveOnboardingApplicationID, constants.KeyInactiveOnboardingApplicationID, "onboardingApplicationID"},
		{constants.KeyActiveCustomerName, constants.KeyInactiveCustomerName, "customerName"},
		{constants.KeyActiveKtpNumber, constants.KeyInactiveKtpNumber, "ktpNumber"},
		{constants.KeyActivePhoneNumber, constants.KeyInactivePhoneNumber, "phoneNumber"},
		{constants.KeyActivePartnerSelfieImage, constants.KeyInactivePartnerSelfieImage, "partnerSelfieImage"},
		{constants.KeyActiveKtpFile, constants.KeyInactiveKtpFile, "ktpFile"},
		{constants.KeyActiveEmail, constants.KeyInactiveEmail, "email"},
		{constants.KeyActiveNationality, constants.KeyInactiveNationality, "nationality"},
		{constants.KeyActiveDateOfBirth, constants.KeyInactiveDateOfBirth, "dateOfBirth"},
		{constants.KeyActivePlaceOfBirth, constants.KeyInactivePlaceOfBirth, "placeOfBirth"},
		{constants.KeyActiveMaritalStatus, constants.KeyInactiveMaritalStatus, "maritalStatus"},
		{constants.KeyActiveGender, constants.KeyInactiveGender, "gender"},
		{constants.KeyActiveSelfieImage, constants.KeyInactiveSelfieImage, "selfieImage"},
		{constants.KeyActiveEducation, constants.KeyInactiveEducation, "education"},
		{constants.KeyActiveMotherMaidenName, constants.KeyInactiveMotherMaidenName, "motherMaidenName"},
		{constants.KeyActiveNpwpAvailability, constants.KeyInactiveNpwpAvailability, "npwpAvailability"},
		{constants.KeyActiveRiskLevel, constants.KeyInactiveRiskLevel, "riskLevel"},
		{constants.KeyActiveUserLevel, constants.KeyInactiveUserLevel, "userLevel"},
		{constants.KeyActiveUserType, constants.KeyInactiveUserType, "userType"},
		{constants.KeyActiveOnboardingSelfie, constants.KeyInactiveOnboardingSelfie, "onboardingImage"},
	}

	fields = make([][]interface{}, 0, len(customerFieldsMap))

	isActive := resultKey == constants.KeyActiveCustomerInformation
	for _, item := range customerFieldsMap {
		if isActive {
			fields = append(fields, []interface{}{item.activeKey, item.fieldName})
		} else {
			fields = append(fields, []interface{}{item.inactiveKey, item.fieldName})
		}
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanAddressDetailData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"ktpAddress":          "Jalan Mawar Melati",
		"ktpPostalCode":       "13950",
		"ktpProvince":         "DKI Jakarta",
		"ktpCity":             "Jakarta Timur",
		"ktpDistrict":         "Cakung",
		"ktpSubDistrict":      "Cakung Timur",
		"ktpCountry":          "Indonesia",
		"domicileAddress":     "Jalan Mawar Melati",
		"domicilePostalCode":  "13950",
		"domicileProvince":    "DKI Jakarta",
		"domicileCity":        "Jakarta Timur",
		"domicileDistrict":    "Cakung",
		"domicileSubDistrict": "Cakung Timur",
		"domicileCountry":     "Indonesia",
	}

	return composeLoanAddressDetailData(data, req.Key), nil
}

func composeLoanAddressDetailData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	ktpFields := []FieldPair{
		{constants.KeyActiveKtpAddress, constants.KeyInactiveKtpAddress, "ktpAddress"},
		{constants.KeyActiveKtpPostalCode, constants.KeyInactiveKtpPostalCode, "ktpPostalCode"},
		{constants.KeyActiveKtpProvince, constants.KeyInactiveKtpProvince, "ktpProvince"},
		{constants.KeyActiveKtpCity, constants.KeyInactiveKtpCity, "ktpCity"},
		{constants.KeyActiveKtpDistrict, constants.KeyInactiveKtpDistrict, "ktpDistrict"},
		{constants.KeyActiveKtpSubDistrict, constants.KeyInactiveKtpSubDistrict, "ktpSubDistrict"},
		{constants.KeyActiveKtpCountry, constants.KeyInactiveKtpCountry, "ktpCountry"},
	}

	domicileFields := []FieldPair{
		{constants.KeyActiveDomicileAddress, constants.KeyInactiveDomicileAddress, "domicileAddress"},
		{constants.KeyActiveDomicilePostalCode, constants.KeyInactiveDomicilePostalCode, "domicilePostalCode"},
		{constants.KeyActiveDomicileProvince, constants.KeyInactiveDomicileProvince, "domicileProvince"},
		{constants.KeyActiveDomicileCity, constants.KeyInactiveDomicileCity, "domicileCity"},
		{constants.KeyActiveDomicileDistrict, constants.KeyInactiveDomicileDistrict, "domicileDistrict"},
		{constants.KeyActiveDomicileSubDistrict, constants.KeyInactiveDomicileSubDistrict, "domicileSubDistrict"},
		{constants.KeyActiveDomicileCountry, constants.KeyInactiveDomicileCountry, "domicileCountry"},
	}

	isActive := resultKey == constants.KeyActiveAddressDetail

	// Build map from source data
	dataKtp := make(map[string]interface{})
	for _, f := range ktpFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		dataKtp[key] = resp[f.fieldName]
	}

	dataDomicile := make(map[string]interface{})
	for _, f := range domicileFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		dataDomicile[key] = resp[f.fieldName]
	}

	result := map[string]interface{}{
		resultKey: []map[string]interface{}{dataKtp, dataDomicile},
	}
	return result
}

func (p process) getDetailLoanEmergencyContactData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"emergencyContactName":         "Rangga",
		"emergencyContactNumber":       "+62887781053300",
		"emergencyContactRelationship": "Spouse",
	}

	return composeLoanEmergencyContactData(data, req.Key), nil
}

// nolint:funlen,dupl
func composeLoanEmergencyContactData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	emergencyContactFields := []FieldPair{
		{constants.KeyActiveEmergencyContactName, constants.KeyInactiveEmergencyContactName, "emergencyContactName"},
		{constants.KeyActiveEmergencyContactPhoneNumber, constants.KeyInactiveEmergencyContactPhoneNumber, "emergencyContactNumber"},
		{constants.KeyActiveEmergencyContactRelationship, constants.KeyInactiveEmergencyContactRelationship, "emergencyContactRelationship"},
	}

	isActive := resultKey == constants.KeyActiveEmergencyContact

	fields = make([][]interface{}, 0, len(emergencyContactFields))
	for _, f := range emergencyContactFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanWorkInformationData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"workingMonths": "3",
		"stayMonths":    "29",
		"incomeSource":  "Salary",
		"monthlyIncome": "Rp7,0000,000-Rp25,000,000",
		"companyName":   "PT. Mawar Melati Tbk.",
		"businessType":  "Education",
		"workType":      "Accountant",
		"jobPosition":   "Staff",
	}

	return composeLoanWorkInformationData(data, req.Key), nil
}

func composeLoanWorkInformationData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	workInformationFields := []FieldPair{
		{constants.KeyActiveWorkingMonths, constants.KeyInactiveWorkingMonths, "workingMonths"},
		{constants.KeyActiveStayMonths, constants.KeyInactiveStayMonths, "stayMonths"},
		{constants.KeyActiveIncomeSource, constants.KeyInactiveIncomeSource, "incomeSource"},
		{constants.KeyActiveMonthlyIncome, constants.KeyInactiveMonthlyIncome, "monthlyIncome"},
		{constants.KeyActiveCompanyName, constants.KeyInactiveCompanyName, "companyName"},
		{constants.KeyActiveBusinessType, constants.KeyInactiveBusinessType, "businessType"},
		{constants.KeyActiveWorkType, constants.KeyInactiveWorkType, "workType"},
		{constants.KeyActiveJobPosition, constants.KeyInactiveJobPosition, "jobPosition"},
	}

	isActive := resultKey == constants.KeyActiveWorkInformation

	fields = make([][]interface{}, 0, len(workInformationFields))
	for _, f := range workInformationFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanContractListData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"contractName": "Loan Contract",
		"contractUrl":  "/uploads/loan.contract.vxbUOAgJwP-2025-06-24.pdf",
		"timestamp":    time.Now().UTC(),
	}

	return composeLoanContractListData(data, req.Key), nil
}

// nolint:funlen,dupl
func composeLoanContractListData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	contractFields := []FieldPair{
		{constants.KeyActiveContractName, constants.KeyInactiveContractName, "contractName"},
		{constants.KeyActiveContractUrl, constants.KeyInactiveContractUrl, "contractUrl"},
		{constants.KeyActiveContractTimestamp, constants.KeyInactiveContractTimestamp, "timestamp"},
	}

	isActive := resultKey == constants.KeyActiveContractList

	fields = make([][]interface{}, 0, len(contractFields))
	for _, f := range contractFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanDrawdownAccountInfoData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"drawdownAmount":      "Rp123,456,765.43",
		"drawdownBankName":    "Superbank",
		"drawdownAccountName": "Superbank",
		"drawdownBankCode":    "562",
	}

	return composeLoanDrawdownAccountInfoData(data, req.Key), nil
}

func composeLoanDrawdownAccountInfoData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	drawdownFields := []FieldPair{
		{constants.KeyActiveDrawdownAmount, constants.KeyInactiveDrawdownAmount, "drawdownAmount"},
		{constants.KeyActiveDrawdownBankName, constants.KeyInactiveDrawdownBankName, "drawdownBankName"},
		{constants.KeyActiveDrawdownAccountName, constants.KeyInactiveDrawdownAccountName, "drawdownAccountName"},
		{constants.KeyActiveDrawdownBankCode, constants.KeyInactiveDrawdownBankCode, "drawdownBankCode"},
	}

	isActive := resultKey == constants.KeyActiveDrawdownAccountInfo

	fields = make([][]interface{}, 0, len(drawdownFields))
	for _, f := range drawdownFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanDeviceInformationData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"deviceID":       "a1b2c3d4e5f67890",
		"adjustID":       "12abc345-de67-89fg-0123-hijk456lmnop",
		"imei":           "***************",
		"ipAddress":      "*************",
		"latitude":       "-6.2088",
		"longitude":      "106.8456",
		"appPackageName": "com.idbank.mobile",
		"appVersion":     "1.23.12",
		"deviceBrand":    "Samsung",
		"deviceModel":    "Samsung Galaxy S23 Ultra",
		"osSystem":       "Android",
		"osVersion":      "12.23",
	}

	return composeLoanDeviceInformationData(data, req.Key), nil
}

func composeLoanDeviceInformationData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	deviceFields := []FieldPair{
		{constants.KeyActiveDeviceID, constants.KeyInactiveDeviceID, "deviceID"},
		{constants.KeyActiveAdjustID, constants.KeyInactiveAdjustID, "adjustID"},
		{constants.KeyActiveImei, constants.KeyInactiveImei, "imei"},
		{constants.KeyActiveIpAddress, constants.KeyInactiveIpAddress, "ipAddress"},
		{constants.KeyActiveLatitude, constants.KeyInactiveLatitude, "latitude"},
		{constants.KeyActiveLongitude, constants.KeyInactiveLongitude, "longitude"},
		{constants.KeyActiveAppPackageName, constants.KeyInactiveAppPackageName, "appPackageName"},
		{constants.KeyActiveAppVersion, constants.KeyInactiveAppVersion, "appVersion"},
		{constants.KeyActiveDeviceBrand, constants.KeyInactiveDeviceBrand, "deviceBrand"},
		{constants.KeyActiveDeviceModel, constants.KeyInactiveDeviceModel, "deviceModel"},
		{constants.KeyActiveOsSystem, constants.KeyInactiveOsSystem, "osSystem"},
		{constants.KeyActiveOsVersion, constants.KeyInactiveOsVersion, "osVersion"},
	}

	isActive := resultKey == constants.KeyActiveDeviceInformation

	fields = make([][]interface{}, 0, len(deviceFields))
	for _, f := range deviceFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailLoanSummaryData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"partnerName":                "Akulaku",
		"superbankLoanAccountNumber": "",
		"akulakuLoanID":              "",
		"productID":                  "Dana Cicil",
		"superbankLoanID":            "",
		"loanAccountStatus":          "",
		"loanAccountSubStatus":       "",
		"loanApplicationStatus":      "REJECTED",
		"loanCode":                   "CODE_XXX_XXX",
		"loanReason":                 "rejected from loan",
		"loanAmount":                 "Rp25,000,000",
		"commision":                  "Rp5,000",
		"tenor":                      "12 M",
		"interestRate":               "2.00%",
		"interestAmount":             "Rp50,000",
		"loanApplicationDate":        "",
		"disbursementDate":           time.Now().UTC(),
		"firstDueDate":               time.Now().UTC(),
		"nextDueDate":                "",
		"purposeOfCredit":            "Konsumsi",
		"maturityDate":               "",
		"collectability":             "2",
		"dpd":                        "61 Days",
		"remainingOutstanding":       "",
		"nextInstallment":            "",
	}

	return composeLoanDetailLoanSummaryData(data, req.Key), nil
}

func composeLoanDetailLoanSummaryData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	var fields [][]interface{}

	loanSummaryFields := []FieldPair{
		{constants.KeyActiveLoanPartnerName, constants.KeyInactiveLoanPartnerName, "partnerName"},
		{constants.KeyActiveLoanSuperbankLoanAccountNumber, constants.KeyInactiveLoanSuperbankLoanAccountNumber, "superbankLoanAccountNumber"},
		{constants.KeyActiveLoanAkulakuLoanID, constants.KeyInactiveLoanAkulakuLoanID, "akulakuLoanID"},
		{constants.KeyActiveLoanProductID, constants.KeyInactiveLoanProductID, "productID"},
		{constants.KeyActiveLoanSuperbankLoanID, constants.KeyInactiveLoanSuperbankLoanID, "superbankLoanID"},
		{constants.KeyActiveLoanAccountStatus, constants.KeyInactiveLoanAccountStatus, "loanAccountStatus"},
		{constants.KeyActiveLoanAccountSubStatus, constants.KeyInactiveLoanAccountSubStatus, "loanAccountSubStatus"},
		{constants.KeyActiveLoanApplicationStatus, constants.KeyInactiveLoanApplicationStatus, "loanApplicationStatus"},
		{constants.KeyActiveLoanCode, constants.KeyInactiveLoanCode, "loanCode"},
		{constants.KeyActiveLoanReason, constants.KeyInactiveLoanReason, "loanReason"},
		{constants.KeyActiveLoanAmount, constants.KeyInactiveLoanAmount, "loanAmount"},
		{constants.KeyActiveloanCommission, constants.KeyInactiveloanCommission, "commision"},
		{constants.KeyActiveLoanTenor, constants.KeyInactiveLoanTenor, "tenor"},
		{constants.KeyActiveLoanInterestRate, constants.KeyInactiveLoanInterestRate, "interestRate"},
		{constants.KeyActiveLoanInterestAmount, constants.KeyInactiveLoanInterestAmount, "interestAmount"},
		{constants.KeyActiveLoanApplicationDate, constants.KeyInactiveLoanApplicationDate, "loanApplicationDate"},
		{constants.KeyActiveLoanDisbursementDate, constants.KeyInactiveLoanDisbursementDate, "disbursementDate"},
		{constants.KeyActiveLoanFistDueDate, constants.KeyInactiveLoanFistDueDate, "firstDueDate"},
		{constants.KeyActiveLoanNextDueDate, constants.KeyInactiveLoanNextDueDate, "nextDueDate"},
		{constants.KeyActiveLoanPurposeOfCredit, constants.KeyInactiveLoanPurposeOfCredit, "purposeOfCredit"},
		{constants.KeyActiveLoanMaturityDate, constants.KeyInactiveLoanMaturityDate, "maturityDate"},
		{constants.KeyActiveLoanCollectability, constants.KeyInactiveLoanCollectability, "collectability"},
		{constants.KeyActiveLoanDpd, constants.KeyInactiveLoanDpd, "dpd"},
		{constants.KeyActiveLoanRemainingOutstanding, constants.KeyInactiveLoanRemainingOutstanding, "remainingOutstanding"},
		{constants.KeyActiveLoanNextInstallment, constants.KeyInactiveLoanNextInstallment, "nextInstallment"},
	}

	isActive := resultKey == constants.KeyActiveLoanSummary

	fields = make([][]interface{}, 0, len(loanSummaryFields))
	for _, f := range loanSummaryFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields = append(fields, []interface{}{key, f.fieldName})
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}

func (p process) getDetailInstallmentDetailData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data and use integration
	var datas []interface{}

	status := "INACTIVE"
	if req.Key == constants.KeyActiveInstallmentDetail {
		status = "ACTIVE"
	}

	limit := 10

	for i := 0; i < limit; i++ {
		data := map[string]interface{}{
			"installmentID":          i + 1,
			"installmentNumber":      fmt.Sprintf("%s%d", "Installment", i+1),
			"installmentDueDate":     time.Now().AddDate(0, 0, 3).UTC(),
			"totalOutstanding":       2000000.00,
			"outstandingPrinciple":   2000000.00,
			"outstandingInterest":    8.59,
			"lateFee":                0,
			"dpd":                    0,
			"updatedAt":              time.Now().UTC(),
			"status":                 status,
			"adminFee":               0,
			"insuranceAmount":        0, // missing data point
			"principalInstallment":   0, // missing data point
			"totalInstallmentAmount": 0, // missing data point
			"installmentInterest":    0, // missing data point
			"outstandingInsurance":   0, // missing data point
		}
		datas = append(datas, data)
	}

	return composeDetailInstallmentDetailData(datas, req.Key), nil
}

func composeDetailInstallmentDetailData(resp []interface{}, resultKey string) map[string]interface{} {
	installFields := []FieldPair{
		{constants.KeyActiveLoanInstallmentID, constants.KeyInactiveLoanInstallmentID, "installmentID"},
		{constants.KeyActiveLoanInstallmentNumber, constants.KeyInactiveLoanInstallmentNumber, "installmentNumber"},
		{constants.KeyActiveLoanDueDate, constants.KeyInactiveLoanDueDate, "installmentDueDate"},
		{constants.KeyActiveLoanTotalOutstanding, constants.KeyInactiveLoanTotalOutstanding, "totalOutstanding"},
		{constants.KeyActiveLoanPrincipalOutstanding, constants.KeyInactiveLoanPrincipalOutstanding, "outstandingPrinciple"},
		{constants.KeyActiveLoanInterestOutstanding, constants.KeyInactiveLoanInterestOutstanding, "outstandingInterest"},
		{constants.KeyActiveLoanLateFeeOutstanding, constants.KeyInactiveLoanLateFeeOutstanding, "lateFee"},
		{constants.KeyActiveLoanInstallmentDpd, constants.KeyInactiveLoanInstallmentDpd, "dpd"},
		{constants.KeyActiveLoanUpdatedAt, constants.KeyInactiveLoanUpdatedAt, "updatedAt"},
		{constants.KeyActiveLoanInstallmentStatus, constants.KeyInactiveLoanInstallmentStatus, "status"},
	}

	detailFields := []FieldPair{
		{constants.KeyActiveLoanDetailInstallmentID, constants.KeyInactiveLoanDetailInstallmentID, "installmentID"},
		{constants.KeyActiveLoanDetailUpdatedAt, constants.KeyInactiveLoanDetailUpdatedAt, "updatedAt"},
		{constants.KeyActiveLoanDetailInsuranceAmount, constants.KeyInactiveLoanDetailInsuranceAmount, "insuranceAmount"},
		{constants.KeyActiveLoanDetailDueDate, constants.KeyInactiveLoanDetailDueDate, "installmentDueDate"},
		{constants.KeyActiveLoanDetailPrincipalInstallment, constants.KeyInactiveLoanDetailPrincipalInstallment, "principalInstallment"},
		{constants.KeyActiveLoanDetailTotalInstallmentAmount, constants.KeyInactiveLoanDetailTotalInstallmentAmount, "totalInstallmentAmount"},
		{constants.KeyActiveLoanDetailInstallmentDpd, constants.KeyInactiveLoanDetailInstallmentDpd, "dpd"},
		{constants.KeyActiveLoanDetailInstallmentInterest, constants.KeyInactiveLoanDetailInstallmentInterest, "installmentInterest"},
		{constants.KeyActiveLoanDetailInstallmentStatus, constants.KeyInactiveLoanDetailInstallmentStatus, "status"},
		{constants.KeyActiveLoanDetailTotalOutstanding, constants.KeyInactiveLoanDetailTotalOutstanding, "totalOutstanding"},
		{constants.KeyActiveLoanDetailOutstandingInsurance, constants.KeyInactiveLoanDetailOutstandingInsurance, "outstandingInsurance"},
		{constants.KeyActiveLoanDetailPrincipalOutstanding, constants.KeyInactiveLoanDetailPrincipalOutstanding, "outstandingPrinciple"},
		{constants.KeyActiveLoanDetailLateFeeOutstanding, constants.KeyInactiveLoanDetailLateFeeOutstanding, "lateFee"},
		{constants.KeyActiveLoanDetailOutstandingInterest, constants.KeyInactiveLoanDetailOutstandingInterest, "outstandingInterest"},
		{constants.KeyActiveLoanDetailAdminFeeOutstanding, constants.KeyInactiveLoanDetailAdminFeeOutstanding, "adminFee"},
	}

	isActive := resultKey == constants.KeyActiveInstallmentDetail

	makeFields := func(pairs []FieldPair) [][]interface{} {
		f := make([][]interface{}, len(pairs))
		for i, p := range pairs {
			k := p.inactiveKey
			if isActive {
				k = p.activeKey
			}
			f[i] = []interface{}{k, p.fieldName}
		}
		return f
	}

	fields := makeFields(installFields)
	details := makeFields(detailFields)

	return map[string]interface{}{
		resultKey: mapResponseDetailInstallmentDetailData(resp, fields, details, isActive),
	}
}

func mapResponseDetailInstallmentDetailData(resp []interface{}, fields [][]interface{}, details [][]interface{}, isActive bool) []map[string]interface{} {
	var final []map[string]interface{}

	for _, item := range resp {
		data, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		parent := make(map[string]interface{})
		for _, f := range fields {
			parent[f[0].(string)] = data[f[1].(string)]
		}

		detailMap := make(map[string]interface{})
		for _, f := range details {
			detailMap[f[0].(string)] = data[f[1].(string)]
		}

		if isActive {
			parent[constants.KeyActiveLoanInstallmentDetail] = detailMap
		} else {
			parent[constants.KeyInactiveLoanInstallmentDetail] = detailMap
		}

		final = append(final, parent)
	}

	return final
}

func (p process) getDetailRepaymentDetailData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data and use integration
	var datas []interface{}

	status := "PAID"
	if req.Key == constants.KeyActiveRepaymentDetail {
		status = "UNPAID"
	}

	limit := 10

	for i := 0; i < limit; i++ {
		var installments []map[string]interface{}
		for x := 0; x < limit; x++ {
			installmentData := map[string]interface{}{
				"installmentNumber": fmt.Sprintf("%d%d", i+1, i+x),
				"amount":            200000, // missing data point
				"principlePaid":     0,      // missing data point
				"interestPaid":      0,      // missing data point
				"adminFeePaid":      0,      // missing data point
				"lateFeePaid":       0,      // missing data point
				"insurancePaid":     0,      // missing data point
			}
			installments = append(installments, installmentData)
		}

		data := map[string]interface{}{
			"loanRepaymentID": i + 1,
			"repaymentDate":   time.Now().UTC(),
			"vaNumber":        "************",
			"vaIssuerName":    "Superbank",
			"amount": map[string]interface{}{
				"principalPaid": 0,
				"interestPaid":  0,
				"lateFeePaid":   0,
				"adminFeePaid":  0,
			},
			"status":                  status,
			"externalVaTransactionID": "********",
			"internalVaTransactionID": "********",
			"installments":            installments,
		}
		datas = append(datas, data)
	}

	return composeDetailRepaymentDetailData(datas, req.Key), nil
}

func getRepaymentFieldGroups(resultKey string) (fields, detailFields, installmentFields [][]interface{}) {
	isActive := resultKey == constants.KeyActiveRepaymentDetail

	key := func(active, inactive string) string {
		if isActive {
			return active
		}
		return inactive
	}

	fields = [][]interface{}{
		{key(constants.KeyActiveLoanRepaymentID, constants.KeyInactiveLoanRepaymentID), "loanRepaymentID"},
		{key(constants.KeyActiveLoanRepaymentDate, constants.KeyInactiveLoanRepaymentDate), "repaymentDate"},
		{key(constants.KeyActiveLoanVaNumber, constants.KeyInactiveLoanVaNumber), "vaNumber"},
		{key(constants.KeyActiveLoanVaIssuerName, constants.KeyInactiveLoanVaIssuerName), "vaIssuerName"},
		{key(constants.KeyActiveLoanTotalAmount, constants.KeyInactiveLoanTotalAmount), "amount"},
		{key(constants.KeyActiveLoanRepaymentStatus, constants.KeyInactiveLoanRepaymentStatus), "status"},
		{key(constants.KeyActiveLoanPgVaTransactionID, constants.KeyInactiveLoanPgVaTransactionID), "externalVaTransactionID"},
		{key(constants.KeyActiveLoanSuperbankVaTransactionID, constants.KeyInactiveLoanSuperbankVaTransactionID), "internalVaTransactionID"},
	}

	detailFields = [][]interface{}{
		{key(constants.KeyActiveLoanRepaymentID, constants.KeyInactiveLoanRepaymentID), "loanRepaymentID"},
		{key(constants.KeyActiveRepaymentDate, constants.KeyInactiveRepaymentDate), "repaymentDate"},
		{key(constants.KeyActiveRepaymentVaNumber, constants.KeyInactiveRepaymentVaNumber), "vaNumber"},
		{key(constants.KeyActiveRepaymentVaIssuerName, constants.KeyInactiveRepaymentVaIssuerName), "vaIssuerName"},
		{key(constants.KeyActiveRepaymentTotalAmount, constants.KeyInactiveRepaymentTotalAmount), "amount"},
		{key(constants.KeyActiveRepaymentStatus, constants.KeyInactiveRepaymentStatus), "status"},
		{key(constants.KeyActiveRepaymentPgVaTransactionID, constants.KeyInactiveRepaymentPgVaTransactionID), "externalVaTransactionID"},
		{key(constants.KeyActiveRepaymentSuperbankVaTransactionID, constants.KeyInactiveRepaymentSuperbankVaTransactionID), "internalVaTransactionID"},
	}

	installmentFields = [][]interface{}{
		{key(constants.KeyActiveRepaymentInstallmentNumber, constants.KeyInactiveRepaymentInstallmentNumber), "installmentNumber"},
		{key(constants.KeyActiveRepaymentInstallmentAmount, constants.KeyInactiveRepaymentInstallmentAmount), "amount"},
		{key(constants.KeyActiveRepaymentInstallmentPrincipalPaid, constants.KeyInactiveRepaymentInstallmentPrincipalPaid), "principlePaid"},
		{key(constants.KeyActiveRepaymentInstallmentInterestPaid, constants.KeyInactiveRepaymentInstallmentInterestPaid), "interestPaid"},
		{key(constants.KeyActiveRepaymentInstallmentAdminFeePaid, constants.KeyInactiveRepaymentInstallmentAdminFeePaid), "adminFeePaid"},
		{key(constants.KeyActiveRepaymentInstallmentLateFeePaid, constants.KeyInactiveRepaymentInstallmentLateFeePaid), "lateFeePaid"},
		{key(constants.KeyActiveRepaymentInstallmentInsurancePaid, constants.KeyInactiveRepaymentInstallmentInsurancePaid), "insurancePaid"},
	}

	return fields, detailFields, installmentFields
}

func getTotalAmount(amountMap map[string]interface{}) float64 {
	var total float64
	for _, v := range amountMap {
		switch value := v.(type) {
		case float64:
			total += value
		case int:
			total += float64(value)
		case int64:
			total += float64(value)
		case string:
			if f, err := strconv.ParseFloat(value, 64); err == nil {
				total += f
			}
		}
	}

	return total
}

// nolint:funlen
func composeDetailRepaymentDetailData(resp []interface{}, resultKey string) map[string]interface{} {
	fields, detailFields, installmentFields := getRepaymentFieldGroups(resultKey)
	result := make(map[string]interface{})
	var finalData []map[string]interface{}

	for _, item := range resp {
		data, _ := item.(map[string]interface{})

		currentData := mapRepaymentFields(data, fields)
		detailData := mapRepaymentFields(data, detailFields)

		if raw, ok := data["installments"]; ok {
			if installments, ok := raw.([]map[string]interface{}); ok {
				composed := composeInstallmentRepayment(installments, installmentFields)
				if resultKey == constants.KeyActiveRepaymentDetail {
					detailData[constants.KeyActiveInstallmentList] = composed
				} else {
					detailData[constants.KeyInactiveInstallmentList] = composed
				}
			}
		}

		nestKey := constants.KeyInactiveLoanRepaymentDetail
		if resultKey == constants.KeyActiveRepaymentDetail {
			nestKey = constants.KeyActiveLoanRepaymentDetail
		}
		currentData[nestKey] = detailData

		finalData = append(finalData, currentData)
	}

	result[resultKey] = finalData
	return result
}

func mapRepaymentFields(data map[string]interface{}, fields [][]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for _, f := range fields {
		targetKey := f[0].(string)
		sourceKey := f[1].(string)

		val := data[sourceKey]
		if sourceKey == "amount" {
			if m, ok := val.(map[string]interface{}); ok {
				result[targetKey] = getTotalAmount(m)
			}
		} else {
			result[targetKey] = val
		}
	}
	return result
}

func composeInstallmentRepayment(rawInstallments []map[string]interface{}, installmentField [][]interface{}) []map[string]interface{} {
	// Add nested installment breakdown inside "detail"
	var composedInstallments []map[string]interface{}

	for _, inst := range rawInstallments {
		instData := make(map[string]interface{})
		for _, field := range installmentField {
			targetKey := field[0].(string)
			sourceKey := field[1].(string)
			instData[targetKey] = inst[sourceKey]
		}
		composedInstallments = append(composedInstallments, instData)
	}

	return composedInstallments
}

func (p process) getDetailVaGenerationHistoryData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data and use integration
	var datas []interface{}
	limit := 10

	for i := 0; i < limit; i++ {
		data := map[string]interface{}{
			"vaNumber":              "************",
			"vaIssuerName":          "Superbank",
			"vaAmount":              "12.000",
			"vaGenerationTimestamp": time.Now().UTC(),
			"vaRequestedTimestamp":  time.Now().AddDate(0, 0, -3).UTC(), // missing data point
			"vaExpiryDate":          time.Now().AddDate(0, 0, 3).UTC(),
			"sendNotifTo":           "xxx",
		}
		datas = append(datas, data)
	}

	var resp = &helper.LoanListResponse{
		Pagination: &helper.Pagination{
			PrevCursorID: fmt.Sprint(limit),
			NextCursorID: fmt.Sprint(limit),
		},
		Data: datas,
	}

	return composeDetailVaGenerationHistoryData(resp, req.Key), nil
}

func composeDetailVaGenerationHistoryData(resp *helper.LoanListResponse, resultKey string) map[string]interface{} {
	vaGenerationHistoryFields := []FieldPair{
		{constants.KeyActiveVaNumber, constants.KeyInactiveVaNumber, "vaNumber"},
		{constants.KeyActiveVaIssuerName, constants.KeyInactiveVaIssuerName, "vaIssuerName"},
		{constants.KeyActiveVaAmount, constants.KeyInactiveVaAmount, "vaAmount"},
		{constants.KeyActiveVaGenerationTimestamp, constants.KeyInactiveVaGenerationTimestamp, "vaGenerationTimestamp"},
		{constants.KeyActiveVaRequestTimestamp, constants.KeyInactiveVaRequestTimestamp, "vaRequestedTimestamp"},
		{constants.KeyActiveVaExpiryDate, constants.KeyInactiveVaExpiryDate, "vaExpiryDate"},
		{constants.KeyActiveVaSendNotifTo, constants.KeyInactiveVaSendNotifTo, "sendNotifTo"},
	}

	isActive := resultKey == constants.KeyActiveVaGenerationHistory

	fields := make([][]interface{}, len(vaGenerationHistoryFields))
	for i, f := range vaGenerationHistoryFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields[i] = []interface{}{key, f.fieldName}
	}

	result := make(map[string]interface{})
	var finalData []map[string]interface{}

	if listData, ok := resp.Data.([]interface{}); ok {
		for _, item := range listData {
			data, _ := item.(map[string]interface{})
			currentData := make(map[string]interface{})

			for _, field := range fields {
				targetKey := field[0].(string)
				sourceKey := field[1].(string)
				currentData[targetKey] = data[sourceKey]
			}

			finalData = append(finalData, currentData)
		}
	}

	result[resultKey] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func (p process) getDetailCreditInformationData(_ context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// TODO: Remove this mock data after integration
	data := map[string]interface{}{
		"creditUserLevel": "A",
		"creditUserType":  "103",
	}

	return composeLoanDetailCreditInformationData(data, req.Key), nil
}

func composeLoanDetailCreditInformationData(resp map[string]interface{}, resultKey string) map[string]interface{} {
	creditFields := []FieldPair{
		{constants.KeyActiveCreditUserLevel, constants.KeyInactiveCreditUserLevel, "creditUserLevel"},
		{constants.KeyActiveCreditUserType, constants.KeyInactiveCreditUserType, "creditUserType"},
	}

	isActive := resultKey == constants.KeyActiveCreditInformation

	fields := make([][]interface{}, len(creditFields))
	for i, f := range creditFields {
		key := f.inactiveKey
		if isActive {
			key = f.activeKey
		}
		fields[i] = []interface{}{key, f.fieldName}
	}

	result := make(map[string]interface{})
	for _, field := range fields {
		targetKey := field[0].(string)
		sourceKey := field[1].(string)
		result[targetKey] = resp[sourceKey]
	}

	return result
}
