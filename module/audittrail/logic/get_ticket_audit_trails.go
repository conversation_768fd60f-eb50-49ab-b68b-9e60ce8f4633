package logic

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sort"
	"strconv"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/pagination"
	"gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/utils/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetAuditTrails ...
//
//nolint:funlen // this function is logically complex and intentionally long
func (p *AuditTrailImpl) GetAuditTrails(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error) {
	slog.FromContext(ctx).Info(constants.GetAuditTrailsTag, "Received request to log audit trails", utils.GetTraceID(ctx))

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// Validation request
	err = validations.ValidateGetAuditTrailsRequest(req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetAuditTrailLogsTag, fmt.Sprintf("error validate request %v", err), slog.Error(err))
		return nil, err
	}

	// Return non pagination data
	if req.WithPagination == nil || *req.WithPagination == false {
		// Get the audit trails
		var auditTrails []*storage.AuditTrailsDTO
		auditTrails, err = storage.GetAuditTrails(ctx, db, makeAuditTrailsDBCondition(req))
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get audit trails")
		}

		// Return the audit trails
		return &api.GetAuditTrailsResponse{
			AuditTrails: makeAuditTrailsAPIStruct(auditTrails),
			Links:       map[string]string{"prevCursorID": "", "nextCursorID": ""},
		}, nil
	}

	// Do rest if withPagination true
	activityType := derefString(req.ActivityType)
	startingBefore := derefString(req.StartingBefore)
	endingAfter := derefString(req.EndingAfter)

	limit := 10
	if req.Limit != nil && *req.Limit > 0 {
		limit = int(*req.Limit)
	}

	sortBy := api.Sort{
		Sort:   api.SortOrder_ASC,
		Column: "created_at",
	}
	if req.SortBy != nil {
		sortBy = *req.SortBy
	}

	reqDto := storage.GetAuditTrailsRequestWithPaginationDto{
		ActivityType:   activityType,
		Limit:          int64(limit),
		StartingBefore: startingBefore,
		EndingAfter:    endingAfter,
		SortBy:         sortBy,
	}

	var result = make([]api.AuditTrails, 0)
	links := make(map[string]string)

	cursorData, err := pagination.ParsePaginationCursorData(reqDto.StartingBefore, reqDto.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetAuditTrailsTag, "error parsing cursor", slog.Error(err))
		return nil, apiError.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetAuditTrailsTag, fmt.Sprintf("Parsed cursor data: %v", cursorData))

	return constructAuditTrailsPage(ctx, db, reqDto, cursorData, result, links)
}

// Below is for non Pagination function
// makeAuditTrailsDBCondition ...
func makeAuditTrailsDBCondition(req *api.GetAuditTrailsRequest) []commonStorage.QueryCondition {
	var conditions []commonStorage.QueryCondition
	if req.Identifier != nil && *req.Identifier != "" {
		conditions = append(conditions, commonStorage.EqualTo(
			"identifier",
			req.Identifier,
		))
	}
	if req.IdentifierType != nil && *req.IdentifierType != "" {
		conditions = append(conditions, commonStorage.EqualTo(
			"identifier_type",
			req.IdentifierType,
		))
	}

	return conditions
}

// makeAuditTrailsAPIStruct ...
func makeAuditTrailsAPIStruct(auditTrails []*storage.AuditTrailsDTO) []api.AuditTrails {
	var res []api.AuditTrails
	for _, at := range auditTrails {
		// Create a map for ExtraParams if it's nil
		extraParams, ok := at.ExtraParams.(map[string]interface{})
		if !ok {
			extraParams = make(map[string]interface{})
		}

		auditTrail := api.AuditTrails{
			Id:             at.ID,
			CreatedAt:      utils.ConvertToWIBFormatted(at.CreatedAt.Time),
			CreatedBy:      at.CreatedBy.Int64,
			Identifier:     at.Identifier,
			IdentifierType: at.IdentifierType,
			Title:          at.Title,
			Description:    at.Description,
			ActivityType:   at.ActivityType,
			ReferenceID:    at.ReferenceID,
			ExtraParams:    extraParams,
			UserName:       at.UserName,
		}

		res = append(res, auditTrail)
	}
	return res
}

// Below is Pagination function
// derefString ...
func derefString(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}

// constructAuditTrailsPage ...
func constructAuditTrailsPage(ctx context.Context, db *sql.DB, req storage.GetAuditTrailsRequestWithPaginationDto, cursorData commonStorage.PaginationCursor, result []api.AuditTrails, links map[string]string) (*api.GetAuditTrailsResponse, error) {
	var tempFirstID *storage.AuditTrailsDTO
	var err error

	for len(result) < int(req.Limit) {
		if nextCursorID := links["nextCursorID"]; nextCursorID != "" {
			cursorData, err = pagination.ParsePaginationCursorData(nextCursorID, "")
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetAuditTrailsTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()), utils.GetTraceID(ctx))
				return emptyGetAuditTrailsResponse(), apiError.DefaultInternalServerError
			}
			req.StartingBefore = nextCursorID
		}
		// creating DB filters
		filters := resultDBFilters(req, cursorData)
		// get transaction data from db
		dbResponse, err := getListFromDB(ctx, db, filters)
		if err != nil {
			return emptyGetAuditTrailsResponse(), apiError.DefaultInternalServerError
		}

		if len(dbResponse) == 0 {
			if len(result) > 0 {
				return &api.GetAuditTrailsResponse{Links: links, AuditTrails: result}, nil
			}
			return emptyGetAuditTrailsResponse(), nil
		}

		resList, finalDBResponse := getResponseGenerator(dbResponse, req)
		paginationParams := pagination.MapPaginationParameters("", req.StartingBefore, req.EndingAfter, "", "", req.Limit)

		tempFirstID = updateTempFirstID(tempFirstID, finalDBResponse)

		links = paginationLinks(ctx, finalDBResponse, paginationParams, cursorData)
		result = append(result, resList...)

		if len(finalDBResponse) == 0 || links["nextCursorID"] == "" {
			return &api.GetAuditTrailsResponse{Links: links, AuditTrails: result}, nil
		}
		updateCursorData(cursorData, dbResponse, int(req.Limit))
	}

	return &api.GetAuditTrailsResponse{
		AuditTrails: result,
		Links:       links,
	}, nil
}

// resultDBFilters ...
func resultDBFilters(req storage.GetAuditTrailsRequestWithPaginationDto, cursorData commonStorage.PaginationCursor) []commonStorage.QueryCondition {
	// add default filters
	likePattern := fmt.Sprintf("%%%s%%", req.ActivityType)
	filters := []commonStorage.QueryCondition{commonStorage.Like("at.activity_type", likePattern, commonStorage.QueryConditionTypeWHERE)}

	finalLimit := int(computeNumberOfFilterRow(req.StartingBefore, req.EndingAfter, req.Limit))
	filters = append(filters, commonStorage.Limit(finalLimit))

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters,
			commonStorage.DescendingOrder("at.created_at"),
			commonStorage.DescendingOrder("at.id"))
	case req.EndingAfter != "":
		filters = append(filters,
			commonStorage.GreaterThan("at.id", cursorData.ID),
			commonStorage.AscendingOrder("at.created_at"),
			commonStorage.AscendingOrder("at.id"),
			commonStorage.NotEqualTo("at.id", cursorData.ID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			commonStorage.LessThan("at.id", cursorData.ID),
			commonStorage.DescendingOrder("at.created_at"),
			commonStorage.DescendingOrder("at.id"),
			commonStorage.NotEqualTo("at.id", cursorData.ID),
		)
	}

	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, "", "", cursorData)

	if startingDate != "" {
		filters = append(filters, commonStorage.GreaterThanOrEqualTo("at.created_at", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, commonStorage.LessThanOrEqualTo("at.created_at", endingDate))
	}

	return filters
}

// computeDateRange will compute the time range based on input params and cursor.
func computeDateRange(startingBefore string, endingAfter string, startDate string, endDate string, cursorData commonStorage.PaginationCursor) (string, string) {
	var startingDate, endingDate string
	switch {
	case endingAfter == "" && startingBefore == "":
		startingDate = startDate
		endingDate = endDate
	case endingAfter != "":
		startingDate = cursorData.CreatedAt
		endingDate = endDate
	default:
		startingDate = startDate
		endingDate = cursorData.CreatedAt
	}
	return startingDate, endingDate
}

// getListFromDB : get logs from db
func getListFromDB(ctx context.Context, db *sql.DB, condition []commonStorage.QueryCondition) ([]*storage.AuditTrailsDTO, error) {
	logs, err := storage.GetAuditTrails(ctx, db, condition)
	if errors.Is(err, data.ErrNoData) {
		return []*storage.AuditTrailsDTO{}, nil
	} else if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAuditTrailsTag, fmt.Sprintf("Error getting Log history, err: %s", err.Error()), utils.GetTraceID(ctx))
		return []*storage.AuditTrailsDTO{}, err
	}

	return logs, nil
}

// paginationLinks create links required for subsequent pagination calls
func paginationLinks(ctx context.Context, dbResponse []*storage.AuditTrailsDTO, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) map[string]string {
	// if first page request, FirstTransactionID is equal to ID of the first fetched transaction
	if paginationParameters.EndingAfter == "" && paginationParameters.StartingBefore == "" {
		if len(dbResponse) > 0 {
			cursorData.FirstID = uint64(dbResponse[0].ID)
		}
	}

	nextCursorID := nextPageCursor(ctx, dbResponse, paginationParameters, cursorData)
	prevCursorID := prevPageCursor(ctx, dbResponse, paginationParameters, cursorData)
	return map[string]string{"prevCursorID": prevCursorID, "nextCursorID": nextCursorID}
}

// nextPageCursor method holds logic of next page link
func nextPageCursor(ctx context.Context, dbResponse []*storage.AuditTrailsDTO, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) string {
	var nextCursorID string
	var nextPageCursorData commonStorage.PaginationCursor
	if len(dbResponse) == 0 {
		return ""
	}
	if int64(len(dbResponse)) == paginationParameters.PageSize+1 || paginationParameters.EndingAfter != "" {
		logsPerPage := utils.MinInt(int(paginationParameters.PageSize), len(dbResponse))
		nextPageCursorData = commonStorage.PaginationCursor{
			ID:        int(dbResponse[logsPerPage-1].ID),
			CreatedAt: dbResponse[logsPerPage-1].CreatedAt.Time.Format("2006-01-02T15:04:05Z07:00"),
			UserID:    strconv.FormatInt(dbResponse[logsPerPage-1].CreatedBy.Int64, 10),
			FirstID:   cursorData.FirstID,
		}
		nextCursor, err := pagination.EncodeCursor(nextPageCursorData)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetAuditTrailLogsTag, "error parsing cursor", slog.Error(err))
			return ""
		}

		nextCursorID = nextCursor
	} else if int64(len(dbResponse)) <= paginationParameters.PageSize {
		nextCursorID = ""
	}

	return nextCursorID
}

// prevPageCursor method holds logic of previous page link
func prevPageCursor(ctx context.Context, dbResponse []*storage.AuditTrailsDTO, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) string {
	var prevCursorID string
	var prevPageCursorData commonStorage.PaginationCursor
	if len(dbResponse) == 0 {
		return ""
	}

	if paginationParameters.StartingBefore == "" && paginationParameters.EndingAfter == "" {
		prevCursorID = ""
	} else if uint64(dbResponse[0].ID) == cursorData.FirstID {
		prevCursorID = ""
	} else {
		prevPageCursorData = commonStorage.PaginationCursor{
			ID:        int(dbResponse[0].ID),
			CreatedAt: dbResponse[0].CreatedAt.Time.Format("2006-01-02T15:04:05Z07:00"),
			UserID:    strconv.FormatInt(dbResponse[0].CreatedBy.Int64, 10), // Check CreatedBy.Valid below
			FirstID:   cursorData.FirstID,
		}

		prevCursor, err := pagination.EncodeCursor(prevPageCursorData)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetAuditTrailLogsTag, "error parsing cursor", slog.Error(err))
			return ""
		}

		prevCursorID = prevCursor
		slog.FromContext(ctx).Info(constants.GetAuditTrailLogsTag, fmt.Sprintf("Cursor First ID is %v and Current to be make cursor ID is %v", cursorData.ID, prevPageCursorData.ID))
	}

	return prevCursorID
}

// updateCursorData ...
func updateCursorData(cursorData commonStorage.PaginationCursor, dbResponse []*storage.AuditTrailsDTO, pageSize int) {
	if len(dbResponse) == 0 {
		return
	}
	cursorData.ID = int(dbResponse[len(dbResponse)-1].ID)
	if len(dbResponse) > pageSize {
		cursorData.ID = int(dbResponse[pageSize-1].ID)
	}
}

// getResponseGenerator contains logic to generate response from DB data after filtering.
// nolint:funlen
func getResponseGenerator(dbResponse []*storage.AuditTrailsDTO, req storage.GetAuditTrailsRequestWithPaginationDto) ([]api.AuditTrails, []*storage.AuditTrailsDTO) {
	// method to filter out committed and pending.
	finalDBResponse := filterList(dbResponse, req)
	if len(finalDBResponse) == 0 {
		return []api.AuditTrails{}, []*storage.AuditTrailsDTO{}
	}

	var result []api.AuditTrails
	resultPerPage := utils.MinInt(int(req.Limit), len(finalDBResponse))

	if req.EndingAfter != "" {
		finalDBResponse = invertDBResponseForBackwardScrolling(finalDBResponse[:resultPerPage])
	}
	for _, dbResult := range finalDBResponse[:resultPerPage] {
		result = append(result, api.AuditTrails{
			Id:             dbResult.ID,
			Description:    dbResult.Description,
			IdentifierType: dbResult.IdentifierType,
			Title:          dbResult.Title,
			Identifier:     dbResult.Identifier,
			CreatedAt:      utils.ConvertToWIBFormatted(dbResult.CreatedAt.Time),
			UserName:       dbResult.UserName,
			CreatedBy:      dbResult.CreatedBy.Int64,
			ActivityType:   dbResult.ActivityType,
			ReferenceID:    dbResult.ReferenceID,
		})
	}
	return result, finalDBResponse
}

// filterList ...
func filterList(response []*storage.AuditTrailsDTO, req storage.GetAuditTrailsRequestWithPaginationDto) []*storage.AuditTrailsDTO {
	// computing number of rows to be added
	filteredRowsCount := computeNumberOfFilterRow(req.StartingBefore, req.EndingAfter, req.Limit)

	// Select transactions as per required pageSize
	finalDBResponse := createFinalDBList(response, req.StartingBefore, req.EndingAfter, filteredRowsCount)
	return finalDBResponse
}

// computeNumberOfFilterRow...
func computeNumberOfFilterRow(startingBefore string, endingAfter string, pageSize int64) int64 {
	var filteredRowsCount int64
	switch {
	case endingAfter == "" && startingBefore == "":
		filteredRowsCount = pageSize + 1
	case endingAfter != "":
		filteredRowsCount = pageSize
	default:
		filteredRowsCount = pageSize + 1
	}
	return filteredRowsCount
}

// invertDBResponseForBackwardScrolling will return inverted array.
// In case of backward scrolling, results are fetched in ascending order therefore rotating them
func invertDBResponseForBackwardScrolling(dbResponse []*storage.AuditTrailsDTO) []*storage.AuditTrailsDTO {
	var invertedDBResponse []*storage.AuditTrailsDTO
	for index := len(dbResponse) - 1; index >= 0; index-- {
		invertedDBResponse = append(invertedDBResponse, dbResponse[index])
	}
	return invertedDBResponse
}

// updateTempFirstID ...
func updateTempFirstID(tempFirstID *storage.AuditTrailsDTO, finalDBResponse []*storage.AuditTrailsDTO) *storage.AuditTrailsDTO {
	if len(finalDBResponse) > 0 {
		if tempFirstID != nil {
			finalDBResponse[0] = tempFirstID
		} else {
			tempFirstID = finalDBResponse[0]
		}
	}
	return tempFirstID
}

// createFinalDBList creates final list of logs to be shown
func createFinalDBList(response []*storage.AuditTrailsDTO, startingBefore string, endingAfter string, filteredRowsCount int64) []*storage.AuditTrailsDTO {
	var finalDBResponse []*storage.AuditTrailsDTO
	finalDBResponse = append(finalDBResponse, response...)
	minData := utils.MinInt(len(finalDBResponse), int(filteredRowsCount))

	finalResponse := finalDBResponse[:minData]

	sort.Slice(finalResponse, func(i, j int) bool {
		switch {
		case endingAfter == "" && startingBefore == "":
			return finalResponse[i].CreatedAt.Time.After(finalResponse[j].CreatedAt.Time)
		case endingAfter != "":
			if finalResponse[i].CreatedAt.Time.Before(finalResponse[j].CreatedAt.Time) {
				return true
			} else if finalResponse[i].CreatedAt.Time.After(finalResponse[j].CreatedAt.Time) {
				return false
			} else {
				return finalResponse[i].ID < finalResponse[j].ID
			}
		case startingBefore != "":
			return finalResponse[i].CreatedAt.Time.After(finalResponse[j].CreatedAt.Time)
		}
		return finalResponse[i].CreatedAt.Time.After(finalResponse[j].CreatedAt.Time)
	})
	return finalResponse
}

// emptyGetLogAuditTrailsResponse will generate empty response structure in case no matching logs is found
func emptyGetAuditTrailsResponse() *api.GetAuditTrailsResponse {
	response := &api.GetAuditTrailsResponse{}
	response.Links = map[string]string{"prevCursorID": "", "nextCursorID": ""}
	response.AuditTrails = []api.AuditTrails{}
	return response
}
