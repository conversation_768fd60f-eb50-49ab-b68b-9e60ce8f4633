package handlers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// CreateTicket creates a ticket
func (o *OnedashService) CreateTicket(ctx context.Context, req *api.CreateTicketRequest) (*api.CreateTicketResponse, error) {
	res, err := logic.Process.CreateTicket(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error("handler.CreateTicket", fmt.Sprintf("error processing create ticket: %v", err.Error()))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create ticket")
	}

	return res, nil
}
