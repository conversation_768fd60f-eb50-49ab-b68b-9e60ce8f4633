package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Create User", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("create user without assign role", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUser)).WithArgs(user.Name, user.Email, sqlmock.AnyArg(), user.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(
					sqlmock.NewResult(2, 1))
				auditTrail := resources.SampleDataCreateUserAuditTrail(1)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(resources.SuccessResponse))
			})
		})
		When("create user with assign role", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				creatorID := int64(1)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,2]
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUser)).WithArgs(user.Name, user.Email, sqlmock.AnyArg(), user.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(
					sqlmock.NewResult(2, 1))
				roleIDs := []int64{1, 2}
				for idx, role := range roleIDs {
					mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUserRole)).WithArgs(role, sqlmock.AnyArg(), creatorID, user.ID).WillReturnResult(sqlmock.NewResult(int64(idx), 1))
				}
				auditTrail := resources.SampleDataCreateUserAuditTrail(creatorID)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(resources.SuccessResponse))
			})
		})
	})
	Context("invalid request", func() {
		When("unauthorized request with invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
		When("unauthorized request with invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				permDetail.Permissions["USER_MANAGEMENT"] = 1
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("invalid request with empty name", func() {
			It("return 401", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedErr := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'CreateUserRequest.Name' Error:Field validation for 'Name' failed on the 'required' tag"
						}
					]
				}`
				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("invalid request with empty email", func() {
			It("return 401", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "",
					"status": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedErr := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'CreateUserRequest.Email' Error:Field validation for 'Email' failed on the 'required' tag"
						}
					]
				}`
				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("invalid request because already exists", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				user := resources.SampleDataForCreateUpdateUser()
				// mock the db query
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnRows(resources.SampleRowsGetUserByEmail(mocker, user))

				expectedRes := `{
					"code": "fieldInvalid",
					"message": "failed to create user",
					"errors": [
						{
							"errorCode": "fieldInvalid",
							"message": "email has been used for another user"
						}
					]
				}`
				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("error request", func() {
		When("error when create user", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUser)).WithArgs(user.Name, user.Email, sqlmock.AnyArg(), user.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnError(sql.ErrConnDone)

				expectedRes := `{
					"code": "internalServerError",
					"message": "failed to create user",
					"errors": [
					  {
						"errorCode": "internalServerError",
						"message": "sql: connection is already closed"
					  }
					]
				}`

				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error when create user role", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				creatorID := int64(1)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"name": "New User",
					"password": "password",
					"email": "<EMAIL>",
					"status": 1,
					"roleIDs": [1,2]
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// mock the db query
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)
				user := resources.SampleDataForCreateUpdateUser()
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUser)).WithArgs(user.Name, user.Email, sqlmock.AnyArg(), user.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(
					sqlmock.NewResult(2, 1))
				roleIDs := []int64{1, 2}
				for _, role := range roleIDs {
					mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUserRole)).WithArgs(role, sqlmock.AnyArg(), creatorID, user.ID).WillReturnError(sql.ErrConnDone)
				}

				expectedRes := `{
					"code": "internalServerError",
					"message": "failed to create role",
					"errors": [
					  {
						"errorCode": "internalServerError",
						"message": "sql: connection is already closed"
					  }
					]
				}`
				res, err := permissionManagemeneClient.Post(CreateUser, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})
