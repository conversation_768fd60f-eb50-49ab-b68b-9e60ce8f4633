// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: ops.proto
package handlers

import (
	context "context"
	servicename "gitlab.myteksi.net/dakota/common/servicename"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

// RegisterRoutes registers handlers with the Servus library.
func (o *OpsService) RegisterRoutes(app *v2.Application) {
	app.POST(
		"/api/v1/ops-service/validate-file",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.ValidateFile(ctx, req.(*api.ValidateFileRequest))
			return res, err
		},
		v2.WithRequest(&api.ValidateFileRequest{}),
		v2.WithResponse(&api.ValidateFileResponse{}),
		v2.WithDescription("ValidateBulkFile is API to validate file."),
		v2.WithClientIdentities(servicename.SentryPartnerT6),
	)
	app.POST(
		"/api/v1/ops-service/execute-file",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.ExecuteFile(ctx, req.(*api.ExecuteFileRequest))
			return res, err
		},
		v2.WithRequest(&api.ExecuteFileRequest{}),
		v2.WithResponse(&api.ExecuteFileResponse{}),
		v2.WithDescription("ExecuteFile is API to process file."),
		v2.WithClientIdentities(servicename.SentryPartnerT6),
	)
}
