package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

const (
	testResultPassed = "PASSED"
	testResultFailed = "FAILED"
	successResponse  = `{
		"id": 4,
		"status": "Success"
	}`
)

var _ = Describe("Create Permission", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := testResultPassed
		if desc.Failed {
			result = testResultFailed
		}
		fmt.Printf("Create Permission: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 success", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID, data.BitwiseValue))

		// mock db
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountPermissionForDuplicateChecking)).
			WithArgs(data.Name, data.BitwiseValue, data.ModuleID).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(0))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreatePermission)).
			WithArgs(data.Name, data.Description, sqlmock.AnyArg(), sqlmock.AnyArg(), data.CreatedBy.Int64, sqlmock.AnyArg(), data.BitwiseValue, data.ModuleID, data.Status).WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Create Permission", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(200))
		Expect(res.Body.String).Should(MatchJSON(successResponse))
	})

	It("should return 403: failed to create permission due to invalid access, when user lacks MODULE_CONFIG permission", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID, data.BitwiseValue))

		// mock db
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountPermissionForDuplicateChecking)).
			WithArgs(data.Name, data.BitwiseValue, data.ModuleID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(0))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreatePermission)).
			WithArgs(data.Name, data.Description, sqlmock.AnyArg(), sqlmock.AnyArg(), data.CreatedBy.Int64, sqlmock.AnyArg(), data.BitwiseValue, data.ModuleID, data.Status).
			WillReturnResult(sqlmock.NewResult(4, 1))

		auditTrail := resources.SampleDataAuditTrailPermission("Create Permission", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// in case permission MODULE_CONFIG deleted on next test case, we need to restore it
		permissionDetail := resources.SampleDataUserPermissionsDetail()

		delete(permissionDetail.Permissions, string(constants.ModuleConfig)) // Simulate missing permission

		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
	})

	It("should return 401: failed to create permission due to unauthorized user", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID, data.BitwiseValue))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// return empty from redis
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

		// no user id on db
		mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
	})

	It("should return 400: failed to create permission due to duplicate name or bitwise value in the same module", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID, data.BitwiseValue))

		// mock db
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountPermissionForDuplicateChecking)).
			WithArgs(data.Name, data.BitwiseValue, data.ModuleID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreatePermission)).
			WithArgs(data.Name, data.Description, sqlmock.AnyArg(), sqlmock.AnyArg(), data.CreatedBy.Int64, sqlmock.AnyArg(), data.BitwiseValue, data.ModuleID, data.Status).
			WillReturnResult(sqlmock.NewResult(4, 1))

		auditTrail := resources.SampleDataAuditTrailPermission("Create Permission", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		expectedResponse := `{
			"code":"badRequest",
			"message":"failed to create permission due to duplicate name or bitwise value in the same module"
		}`

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 400: failed to create permission due to missing fields", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountPermissionForDuplicateChecking)).
			WithArgs(data.Name, data.BitwiseValue, data.ModuleID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreatePermission)).
			WithArgs(data.Name, data.Description, sqlmock.AnyArg(), sqlmock.AnyArg(), data.CreatedBy.Int64, sqlmock.AnyArg(), data.BitwiseValue, data.ModuleID, data.Status).
			WillReturnResult(sqlmock.NewResult(4, 1))

		auditTrail := resources.SampleDataAuditTrailPermission("Create Permission", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		expectedResponse := `{
			"code": "badRequest",
			"message": "failed to validate request",
			"errors": [
				{
				"errorCode": "badRequest",
				"message": "failed to validate request Key: 'CreatePermissionRequest.Bitwise' Error:Field validation for 'Bitwise' failed on the 'required' tag"
				}
			]
		}`

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 400: failed to create permission due to incorrect bitwise", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		data := resources.SampleDataForCreatePermission()

		// change wrong bitwise here
		data.BitwiseValue = 5

		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, data.Name, data.Description, data.Status, data.ModuleID, data.BitwiseValue))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountPermissionForDuplicateChecking)).
			WithArgs(data.Name, data.BitwiseValue, data.ModuleID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreatePermission)).
			WithArgs(data.Name, data.Description, sqlmock.AnyArg(), sqlmock.AnyArg(), data.CreatedBy.Int64, sqlmock.AnyArg(), data.BitwiseValue, data.ModuleID, data.Status).
			WillReturnResult(sqlmock.NewResult(4, 1))

		auditTrail := resources.SampleDataAuditTrailPermission("Create Permission", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		expectedResponse := `{
			"code": "badRequest",
			"message": "failed to validate request",
			"errors": [
				{
					"errorCode": "badRequest",
					"message": "failed to validate request Key: 'CreatePermissionRequest.Bitwise' Error:Field validation for 'Bitwise' failed on the 'permission_bitwise' tag"
				}
			]
		}`

		res, err := permissionManagemeneClient.Post(CreatePermission, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})
})
