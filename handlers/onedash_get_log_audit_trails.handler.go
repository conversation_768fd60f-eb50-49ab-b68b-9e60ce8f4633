package handlers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sort"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/pagination"
	"gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// GetLogAuditTrails is API to get Log Audit Trails
func (o *OnedashService) GetLogAuditTrails(ctx context.Context, req *api.GetLogAuditTrailsRequest) (*api.GetLogAuditTrailsResponse, error) {
	if err := validations.ValidateGetLogAuditTrailsRequest(req); err != nil {
		slog.FromContext(ctx).Error(constants.GetLogAuditTrailLogsTag, fmt.Sprintf("error validate request %v", err), slog.Error(err))
		return nil, err
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, o.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, err
	}
	response, err := GetLogAuditTrails(ctx, req, db)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetLogAuditTrailLogsTag, "error get log audit trails", slog.Error(err))
		return nil, err
	}

	return response, nil
}

// GetLogAuditTrails Core logic of getting the Log Audit Trails
func GetLogAuditTrails(ctx context.Context, req *api.GetLogAuditTrailsRequest, db *sql.DB) (*api.GetLogAuditTrailsResponse, error) {
	var finalLogList = make([]api.LogAuditTrail, 0)
	links := make(map[string]string)

	if req.Limit <= 0 {
		req.Limit = 10
	}

	cursorData, err := pagination.ParsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetLogAuditTrailLogsTag, "error parsing cursor", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetLogAuditTrailLogsTag, fmt.Sprintf("Parsed cursor data: %v", cursorData))

	return constructLogsPage(ctx, db, req, cursorData, finalLogList, links)
}

func constructLogsPage(ctx context.Context, db *sql.DB, req *api.GetLogAuditTrailsRequest, cursorData commonStorage.PaginationCursor, finalLogList []api.LogAuditTrail, links map[string]string) (*api.GetLogAuditTrailsResponse, error) {
	var tempFirstID *storage.LogAuditTrail
	var err error

	for len(finalLogList) < int(req.Limit) {
		if nextCursorID := links["nextCursorID"]; nextCursorID != "" {
			cursorData, err = pagination.ParsePaginationCursorData(nextCursorID, "")
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetLogAuditTrailLogsTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()), utils.GetTraceID(ctx))
				return emptyGetLogAuditTrailsResponse(), api.DefaultInternalServerError
			}
			req.StartingBefore = nextCursorID
		}
		// creating DB filters
		filters := logListDBFilters(req, cursorData)
		// get transaction data from db
		dbResponse, err := getAllLogsFromDB(ctx, db, filters)
		if err != nil {
			return emptyGetLogAuditTrailsResponse(), api.DefaultInternalServerError
		}

		if len(dbResponse) == 0 {
			if len(finalLogList) > 0 {
				return &api.GetLogAuditTrailsResponse{Links: links, Logs: finalLogList}, nil
			}
			return emptyGetLogAuditTrailsResponse(), nil
		}

		logList, finalDBResponse := getLogsResponseGenerator(dbResponse, req)
		paginationParams := pagination.MapPaginationParameters(req.UserID, req.StartingBefore, req.EndingAfter, req.StartTime, req.EndTime, req.Limit)

		tempFirstID = updateTempFirstID(tempFirstID, finalDBResponse)

		links = paginationLinks(ctx, finalDBResponse, paginationParams, cursorData)
		finalLogList = append(finalLogList, logList...)

		if len(finalDBResponse) == 0 || links["nextCursorID"] == "" {
			return &api.GetLogAuditTrailsResponse{Links: links, Logs: finalLogList}, nil
		}
		updateCursorData(cursorData, dbResponse, int(req.Limit))
	}

	return &api.GetLogAuditTrailsResponse{
		Logs:  finalLogList,
		Links: links,
	}, nil
}

func logListDBFilters(req *api.GetLogAuditTrailsRequest, cursorData commonStorage.PaginationCursor) []commonStorage.QueryCondition {
	// add default filters
	filters := []commonStorage.QueryCondition{commonStorage.EqualTo("service", req.ServiceName)}
	if req.UserID != "" {
		filters = append(filters, commonStorage.EqualTo("user_id", req.UserID))
	}

	if req.RelatedID != "" {
		filters = append(filters, commonStorage.EqualTo("related_id", req.RelatedID))
	}

	if req.Event != "" {
		filters = append(filters, commonStorage.EqualTo("event", req.Event))
	}

	if req.Action != "" {
		filters = append(filters, commonStorage.EqualTo("action", req.Action))
	}

	if req.SafeID != "" {
		filters = append(filters, commonStorage.EqualTo("safe_id", req.SafeID))
	}

	finalLimit := int(computeNumberOfFilterRow(req.StartingBefore, req.EndingAfter, req.Limit))
	filters = append(filters, commonStorage.Limit(finalLimit))

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters,
			commonStorage.DescendingOrder("created_at"),
			commonStorage.DescendingOrder("ID"))
	case req.EndingAfter != "":
		filters = append(filters,
			commonStorage.GreaterThan("ID", cursorData.ID),
			commonStorage.AscendingOrder("created_at"),
			commonStorage.AscendingOrder("ID"),
			commonStorage.NotEqualTo("ID", cursorData.ID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			commonStorage.LessThan("ID", cursorData.ID),
			commonStorage.DescendingOrder("created_at"),
			commonStorage.DescendingOrder("ID"),
			commonStorage.NotEqualTo("ID", cursorData.ID),
		)
	}

	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, req.StartTime, req.EndTime, cursorData)
	if startingDate != "" {
		filters = append(filters, commonStorage.GreaterThanOrEqualTo("created_at", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, commonStorage.LessThanOrEqualTo("created_at", endingDate))
	}

	return filters
}

// computeDateRange will compute the time range based on input params and cursor.
func computeDateRange(startingBefore string, endingAfter string, startDate string, endDate string, cursorData commonStorage.PaginationCursor) (string, string) {
	var startingDate, endingDate string
	switch {
	case endingAfter == "" && startingBefore == "":
		startingDate = startDate
		endingDate = endDate
	case endingAfter != "":
		startingDate = cursorData.CreatedAt
		endingDate = endDate
	default:
		startingDate = startDate
		endingDate = cursorData.CreatedAt
	}
	return startingDate, endingDate
}

// getAllLogsFromDB : get logs from db
func getAllLogsFromDB(ctx context.Context, db *sql.DB, condition []commonStorage.QueryCondition) ([]*storage.LogAuditTrail, error) {
	logs, err := storage.GetAuditTrailLogs(ctx, db, condition)
	if errors.Is(err, data.ErrNoData) {
		return []*storage.LogAuditTrail{}, nil
	} else if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLogAuditTrailLogsTag, fmt.Sprintf("Error getting Log history, err: %s", err.Error()), utils.GetTraceID(ctx))
		return []*storage.LogAuditTrail{}, err
	}

	return logs, nil
}

// paginationLinks create links required for subsequent pagination calls
func paginationLinks(ctx context.Context, dbResponse []*storage.LogAuditTrail, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) map[string]string {
	// if first page request, FirstTransactionID is equal to ID of the first fetched transaction
	if paginationParameters.EndingAfter == "" && paginationParameters.StartingBefore == "" {
		cursorData.FirstID = dbResponse[0].ID
	}
	nextCursorID := nextPageCursor(ctx, dbResponse, paginationParameters, cursorData)
	prevCursorID := prevPageCursor(ctx, dbResponse, paginationParameters, cursorData)
	return map[string]string{"prevCursorID": prevCursorID, "nextCursorID": nextCursorID}
}

// nextPageCursor method holds logic of next page link
func nextPageCursor(ctx context.Context, dbResponse []*storage.LogAuditTrail, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) string {
	var nextCursorID string
	var nextPageCursorData commonStorage.PaginationCursor
	if len(dbResponse) == 0 {
		return ""
	}
	if int64(len(dbResponse)) == paginationParameters.PageSize+1 || paginationParameters.EndingAfter != "" {
		logsPerPage := utils.MinInt(int(paginationParameters.PageSize), len(dbResponse))
		nextPageCursorData = commonStorage.PaginationCursor{
			ID:        int(dbResponse[logsPerPage-1].ID),
			CreatedAt: dbResponse[logsPerPage-1].CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UserID:    dbResponse[logsPerPage-1].UserID,
			FirstID:   cursorData.FirstID,
		}
		nextCursor, err := pagination.EncodeCursor(nextPageCursorData)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetLogAuditTrailLogsTag, "error parsing cursor", slog.Error(err))
			return ""
		}

		nextCursorID = nextCursor
	} else if int64(len(dbResponse)) <= paginationParameters.PageSize {
		nextCursorID = ""
	}

	return nextCursorID
}

// prevPageCursor method holds logic of previous page link
func prevPageCursor(ctx context.Context, dbResponse []*storage.LogAuditTrail, paginationParameters commonStorage.PaginationParams, cursorData commonStorage.PaginationCursor) string {
	var prevCursorID string
	var prevPageCursorData commonStorage.PaginationCursor
	if len(dbResponse) == 0 {
		return ""
	}
	if paginationParameters.StartingBefore == "" && paginationParameters.EndingAfter == "" {
		prevCursorID = ""
	} else if dbResponse[0].ID == cursorData.FirstID {
		// If the first ID of page equal to FirstTransactionID, this implies not previousPage exist
		prevCursorID = ""
	} else {
		prevPageCursorData = commonStorage.PaginationCursor{
			ID:        int(dbResponse[0].ID),
			CreatedAt: dbResponse[0].CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UserID:    dbResponse[0].UserID,
			FirstID:   cursorData.FirstID,
		}

		prevCursor, err := pagination.EncodeCursor(prevPageCursorData)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetLogAuditTrailLogsTag, "error parsing cursor", slog.Error(err))
			return ""
		}

		prevCursorID = prevCursor
		slog.FromContext(ctx).Info(constants.GetLogAuditTrailLogsTag, fmt.Sprintf("Cursor First ID is %v and Current to be make cursor ID is %v", cursorData.ID, prevPageCursorData.ID))
	}
	return prevCursorID
}

func updateCursorData(cursorData commonStorage.PaginationCursor, dbResponse []*storage.LogAuditTrail, pageSize int) {
	if len(dbResponse) == 0 {
		return
	}
	cursorData.ID = int(dbResponse[len(dbResponse)-1].ID)
	if len(dbResponse) > pageSize {
		cursorData.ID = int(dbResponse[pageSize-1].ID)
	}
}

// getLogsResponseGenerator contains logic to generate response from DB data after filtering.
// nolint:funlen
func getLogsResponseGenerator(dbResponse []*storage.LogAuditTrail, req *api.GetLogAuditTrailsRequest) ([]api.LogAuditTrail, []*storage.LogAuditTrail) {
	// method to filter out committed and pending.
	finalDBResponse := filterLogs(dbResponse, req)
	if len(finalDBResponse) == 0 {
		return []api.LogAuditTrail{}, []*storage.LogAuditTrail{}
	}

	var logsList []api.LogAuditTrail
	logsPerPage := utils.MinInt(int(req.Limit), len(finalDBResponse))
	// invert final DB entries to show them in descending order
	if req.EndingAfter != "" {
		finalDBResponse = invertDBResponseForBackwardScrolling(finalDBResponse[:logsPerPage])
	}
	for _, log := range finalDBResponse[:logsPerPage] {
		logsList = append(logsList, api.LogAuditTrail{
			ID:         int64(log.ID),
			Name:       log.Name,
			UserID:     log.UserID,
			Email:      log.Email,
			Event:      log.Event,
			Action:     log.Action,
			Metadata:   log.Metadata,
			RelatedID:  log.RelatedID,
			Service:    log.Service,
			ActionTime: log.CreatedAt,
			SafeID:     log.SafeID,
		})
	}
	return logsList, finalDBResponse
}

func filterLogs(response []*storage.LogAuditTrail, req *api.GetLogAuditTrailsRequest) []*storage.LogAuditTrail {
	// computing number of rows to be added
	filteredRowsCount := computeNumberOfFilterRow(req.StartingBefore, req.EndingAfter, req.Limit)

	// Select transactions as per required pageSize
	finalDBResponse := createFinalDBLogList(response, req.StartingBefore, req.EndingAfter, filteredRowsCount)
	return finalDBResponse
}

// computeNumberOfFilterRow...
func computeNumberOfFilterRow(startingBefore string, endingAfter string, pageSize int64) int64 {
	var filteredRowsCount int64
	switch {
	case endingAfter == "" && startingBefore == "":
		filteredRowsCount = pageSize + 1
	case endingAfter != "":
		filteredRowsCount = pageSize
	default:
		filteredRowsCount = pageSize + 1
	}
	return filteredRowsCount
}

// invertDBResponseForBackwardScrolling will return inverted array.
// In case of backward scrolling, results are fetched in ascending order therefore rotating them
func invertDBResponseForBackwardScrolling(dbResponse []*storage.LogAuditTrail) []*storage.LogAuditTrail {
	var invertedDBResponse []*storage.LogAuditTrail
	for index := len(dbResponse) - 1; index >= 0; index-- {
		invertedDBResponse = append(invertedDBResponse, dbResponse[index])
	}
	return invertedDBResponse
}

// updateTempFirstID ...
func updateTempFirstID(tempFirstID *storage.LogAuditTrail, finalDBResponse []*storage.LogAuditTrail) *storage.LogAuditTrail {
	if len(finalDBResponse) > 0 {
		if tempFirstID != nil {
			finalDBResponse[0] = tempFirstID
		} else {
			tempFirstID = finalDBResponse[0]
		}
	}
	return tempFirstID
}

// createFinalDBLogList creates final list of logs to be shown
func createFinalDBLogList(response []*storage.LogAuditTrail, startingBefore string, endingAfter string, filteredRowsCount int64) []*storage.LogAuditTrail {
	var finalDBResponse []*storage.LogAuditTrail
	finalDBResponse = append(finalDBResponse, response...)
	minData := utils.MinInt(len(finalDBResponse), int(filteredRowsCount))

	finalResponse := finalDBResponse[:minData]

	sort.Slice(finalResponse, func(i, j int) bool {
		switch {
		case endingAfter == "" && startingBefore == "":
			return finalResponse[i].CreatedAt.After(finalResponse[j].CreatedAt)
		case endingAfter != "":
			if finalResponse[i].CreatedAt.Before(finalResponse[j].CreatedAt) {
				return true
			} else if finalResponse[i].CreatedAt.After(finalResponse[j].CreatedAt) {
				return false
			} else {
				return finalResponse[i].ID < finalResponse[j].ID
			}
		case startingBefore != "":
			return finalResponse[i].CreatedAt.After(finalResponse[j].CreatedAt)
		}
		return finalResponse[i].CreatedAt.After(finalResponse[j].CreatedAt)
	})
	return finalResponse
}

// emptyGetLogAuditTrailsResponse will generate empty response structure in case no matching logs is found
func emptyGetLogAuditTrailsResponse() *api.GetLogAuditTrailsResponse {
	response := &api.GetLogAuditTrailsResponse{}
	response.Links = map[string]string{"prevCursorID": "", "nextCursorID": ""}
	response.Logs = []api.LogAuditTrail{}
	return response
}
