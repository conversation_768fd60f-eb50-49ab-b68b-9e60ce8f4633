package api

import (
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
	jcMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

var _ = Describe("Login", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
		mockJumpcloud = &jcMock.JumpCloud{}
		jumpcloud.JCClient = mockJumpcloud
	})

	Context("Success request", func() {
		When("success all field", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrail()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrail()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success without extraParams", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutExtraParams()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutExtraParams()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success without metadata", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutMetadata()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutMetadata()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success without title", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutTitle()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutTitle()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success without description", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutDescription()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutDescription()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success without reference_id", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutReferenceID()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutReferenceID()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("success with null", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithNull()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithNull()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("with identifier is null", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutIdentifier()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutIdentifier()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(400))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("with identifier_type is null", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutIdentifierType()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutIdentifierType()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(400))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("with activity_type is null", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutActivityType()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutActivityType()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(400))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})

		When("with created_by is null", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				payloadAuditTrail := resources.SamplePayloadAuditTrailWithoutCreatedBy()
				payload, err := utils.ToJSON[string](payloadAuditTrail)
				Expect(err).ShouldNot(HaveOccurred())
				log.Println(payload)

				body := hcl.JSON(payload)

				auditTrail := resources.SampleDataAuditTrailWithoutCreatedBy()
				queryInsert := auditTrail.QueryInsert()
				log.Println(queryInsert)

				mocker.ExpectExec(regexp.QuoteMeta(queryInsert)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title.String, auditTrail.Description.String, auditTrail.ActivityType, auditTrail.ReferenceID.String, auditTrail.ExtraParams, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				res, err := auditTrailClient.Post(AuditTrail, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(400))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})
	})

})
