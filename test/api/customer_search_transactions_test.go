package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerExperienceMock "gitlab.myteksi.net/bersama/customer-experience/api/mock"
	customerMaster "gitlab.myteksi.net/bersama/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/bersama/customer-master/api/v2/mock"
	txHistory "gitlab.myteksi.net/bersama/transaction-history/api"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search", func() {
	var (
		db     *sql.DB
		mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockAccountService = &accountServiceMock.AccountService{}

		config := &logic.MockProcessConfig{
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AccountService:     mockAccountService,
			AppConfig:          service.AppConfig,
		}
		logic.MockInitLogic(config)

		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch Transactions", func() {
		When("user has valid permissions and getting tx tab", func() {
			It("should successfully return account lists", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
					"identifierType": "SAFE_ID",
					"key": "customerTransactionList"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerTransactionList", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Customer Transactions List", "customerTransactionList", nil, 1, 1, 1, "tab",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Transaction List", "transactionList", "table", 1, 1).
						AddRow(2, "Transaction Detail", "transactionDetails", "modal", 1, 2))

				var expectedResponseCustomerLeanInfo = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"dateOfBirth": "1988-08-18",
							"gender": "MALE",
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"name": "Efren Ruecker Sr",
							"nationality": "WNI",
							"placeOfBirth": "PEMALANG",
							"publicID": "ID179555577230",
							"relatedCounterPartyInd": false,
							"startDate": "2024-06-04T03:06:04Z",
							"status": "ONBOARDED",
							"type": "NAT_PERSON"
						}
					}
				}`
				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseCustomerLeanInfo), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponse, nil).Once()

				var mockAccountDetail *accountService.ListCASAAccountsForCustomerDetailResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseGetAccountList), &mockAccountDetail)

				// mock for get
				mockAccountService.On("ListCASAAccountsForCustomerDetail", mock.Anything, &accountService.ListCASAAccountsForCustomerDetailRequest{
					CifNumber: "ID179555577230",
				}).Return(mockAccountDetail, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "customerTransactionList",
						"type": "tab",
						"label": "Customer Transactions List",
						"children": [
							{
								"key": "transactionList",
								"type": "table",
								"label": "Transaction List"
							},
							{
								"key": "transactionDetails",
								"type": "modal",
								"label": "Transaction Detail"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting tx list key", func() {
			It("should successfully return tx lists", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "************",
					"identifierType": "ACCOUNT_NUMBER",
					"key": "transactionList"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("transactionList", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).
						AddRow(1, "Customer Transactions List", "customerTransactionList", nil, 1, 1, 1, "tab").
						AddRow(2, "Transaction List", "transactionList", 1, 1, 2, 1, "table").
						AddRow(3, "Transaction Detail", "transactionDetails", 2, 1, 2, 2, "modal").
						AddRow(4, "Transaction ID", "transactionID", 3, 1, 3, 1, "oneliner").
						AddRow(5, "Pre Transaction ID", "preTransactionID", 3, 2, 2, 3, "oneliner").
						AddRow(6, "Transaction Description", "transactionDescription", 3, 2, 3, 3, "oneliner").
						AddRow(7, "Transaction Type", "transactionType", 3, 2, 4, 3, "oneliner").
						AddRow(8, "Transaction Sub Type", "transactionSubType", 3, 2, 5, 3, "oneliner").
						AddRow(9, "Creation Date", "creationDate", 3, 2, 6, 3, "oneliner").
						AddRow(10, "Posting Date", "postingDate", 3, 2, 7, 3, "oneliner").
						AddRow(11, "Transaction Amount", "transactionAmount", 3, 2, 8, 3, "oneliner").
						AddRow(12, "Transaction Status", "transactionStatus", 3, 2, 9, 3, "oneliner").
						AddRow(13, "Counterparty Name", "counterpartyName", 3, 2, 10, 3, "oneliner").
						AddRow(14, "Counterparty Account Name", "counterpartyAccountName", 3, 2, 11, 3, "oneliner").
						AddRow(15, "Counterparty Account ID", "counterpartyAccountID", 3, 2, 12, 3, "oneliner").
						AddRow(16, "Ending Balance", "endingBalance", 3, 2, 13, 3, "oneliner").
						AddRow(17, "Purpose of Transaction", "purposeOfTransaction", 3, 3, 1, 4, "oneliner").
						AddRow(18, "Principle Amount", "principleAmount", 3, 3, 2, 4, "oneliner").
						AddRow(19, "Total Discount Amount", "totalDiscountAmount", 3, 3, 3, 4, "oneliner").
						AddRow(20, "Error Description", "errorDescription", 3, 3, 4, 4, "oneliner").
						AddRow(21, "Customer Payment Account Number", "cpan", 3, 3, 5, 4, "oneliner").
						AddRow(22, "Merchant Location", "merchantLocation", 3, 3, 6, 4, "oneliner").
						AddRow(23, "Terminal ID", "terminalID", 3, 3, 7, 4, "oneliner").
						AddRow(24, "QRIS Type", "qrisType", 3, 3, 8, 4, "oneliner").
						AddRow(25, "Remarks", "remarks", 3, 3, 9, 4, "oneliner").
						AddRow(26, "Partner Reference ID", "partnerReferenceID", 3, 3, 10, 4, "oneliner").
						AddRow(27, "Client Transaction ID", "clientTransactionID", 3, 3, 11, 4, "oneliner").
						AddRow(28, "External ID", "externalID", 3, 3, 12, 4, "oneliner").
						AddRow(29, "Original Reference ID", "originalReferenceID", 3, 3, 13, 4, "oneliner").
						AddRow(30, "Original External ID", "originalExternalID", 3, 3, 14, 4, "oneliner").
						AddRow(31, "Activity Type", "activityType", 3, 3, 15, 4, "oneliner"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Transaction ID", "transactionID", "oneliner", 1, 1).
						AddRow(5, "Pre Transaction ID", "preTransactionID", "oneliner", 1, 2).
						AddRow(6, "Transaction Description", "transactionDescription", "oneliner", 1, 3).
						AddRow(7, "Transaction Type", "transactionType", "oneliner", 1, 4).
						AddRow(8, "Transaction Sub Type", "transactionSubType", "oneliner", 1, 5).
						AddRow(9, "Creation Date", "creationDate", "oneliner", 1, 6).
						AddRow(10, "Posting Date", "postingDate", "oneliner", 1, 7).
						AddRow(11, "Transaction Amount", "transactionAmount", "oneliner", 1, 8).
						AddRow(12, "Transaction Status", "transactionStatus", "oneliner", 1, 9).
						AddRow(13, "Counterparty Name", "counterpartyName", "oneliner", 1, 10).
						AddRow(14, "Counterparty Account Name", "counterpartyAccountName", "oneliner", 1, 11).
						AddRow(15, "Counterparty Account ID", "counterpartyAccountID", "oneliner", 1, 12).
						AddRow(16, "Ending Balance", "endingBalance", "oneliner", 1, 13))

				var expectedResponseLastTrxList = `{
					"links": {
						"next": "/v1/accounts/************/transactions?pageSize=1&startingBefore=************************************************************************************************************************************",
						"nextCursorID": "************************************************************************************************************************************",
						"prev": "",
						"prevCursorID": ""
					},
					"data": [
						{
							"transactionID": "pt02_dm01_************_5_se01_************_1743094860000000000",
							"batchID": "************_5_se01_************_1743094860000000000",
							"displayName": "Bunga Didapat",
							"iconURL": "https://idbank-dev-backend-assets.s3.ap-southeast-3.amazonaws.com/interest_earned.png",
							"amount": 32,
							"currency": "IDR",
							"status": "COMPLETED",
							"creationTimestamp": "2025-03-27T17:01:00Z",
							"category": {},
							"endingBalance": 8434,
							"transactionType": "INTEREST_PAYOUT",
							"transactionSubtype": "SAVINGS",
							"transactionDescription": "Bunga Didapat",
							"counterParty": {
								"displayName": "Bunga Didapat",
								"iconURL": "https://idbank-dev-backend-assets.s3.ap-southeast-3.amazonaws.com/interest_earned.png"
							},
							"updateTimestamp": "2025-03-27T17:01:00Z"
						}
					]
				}`

				var mockResponse *txHistory.GetTransactionsHistoryResponse
				_ = json.Unmarshal([]byte(expectedResponseLastTrxList), &mockResponse)

				mockTxHistory.On("ListAccountTransactionsSearch", mock.Anything, &txHistory.GetTransactionsHistoryRequest{
					AccountID: "***************",
					PageSize:  1,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "transactionList",
						"type": "table",
						"label": "Transaction List",
						"children": [
							{
								"key": "transactionID",
								"type": "oneliner",
								"label": "Transaction ID"
							},
							{
								"key": "preTransactionID",
								"type": "oneliner",
								"label": "Pre Transaction ID"
							},
							{
								"key": "transactionDescription",
								"type": "oneliner",
								"label": "Transaction Description"
							},
							{
								"key": "transactionType",
								"type": "oneliner",
								"label": "Transaction Type"
							},
							{
								"key": "transactionSubType",
								"type": "oneliner",
								"label": "Transaction Sub Type"
							},
							{
								"key": "creationDate",
								"type": "oneliner",
								"label": "Creation Date"
							},
							{
								"key": "postingDate",
								"type": "oneliner",
								"label": "Posting Date"
							},
							{
								"key": "transactionAmount",
								"type": "oneliner",
								"label": "Transaction Amount"
							},
							{
								"key": "transactionStatus",
								"type": "oneliner",
								"label": "Transaction Status"
							},
							{
								"key": "counterpartyName",
								"type": "oneliner",
								"label": "Counterparty Name"
							},
							{
								"key": "counterpartyAccountName",
								"type": "oneliner",
								"label": "Counterparty Account Name"
							},
							{
								"key": "counterpartyAccountID",
								"type": "oneliner",
								"label": "Counterparty Account ID"
							},
							{
								"key": "endingBalance",
								"type": "oneliner",
								"label": "Ending Balance"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": {
							"nextCursorID": "************************************************************************************************************************************",
							"prevCursorID": ""
						},
						"safeID": null,
						"transactionList": [
							{
								"counterpartyAccountID": "",
								"counterpartyAccountName": "",
								"counterpartyName": "",
								"creationDate": "2025-03-27T17:01:00Z",
								"endingBalance": 8434,
								"postingDate": "2025-03-27T17:01:00Z",
								"transactionAmount": 32,
								"transactionID": "pt02_dm01_************_5_se01_************_1743094860000000000",
								"transactionStatus": "COMPLETED",
								"transactionSubType": "SAVINGS",
								"transactionType": "INTEREST_PAYOUT"
							},
							{
								"counterpartyAccountID": "",
								"counterpartyAccountName": "",
								"counterpartyName": "",
								"creationDate": "2025-02-27T17:01:00Z",
								"endingBalance": 8402,
								"postingDate": "2025-02-27T17:01:00Z",
								"transactionAmount": 36,
								"transactionID": "pt02_dm01_************_5_se01_************_1740675660000000000",
								"transactionStatus": "COMPLETED",
								"transactionSubType": "SAVINGS",
								"transactionType": "INTEREST_PAYOUT"
							}
						]
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
