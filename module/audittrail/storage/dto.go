// Package storage provide the data access layer
package storage

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"
)

// DataJSON ...
type DataJSON map[string]any

// Value ...
func (a DataJSON) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan ...
func (a *DataJSON) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid (%v)", value)
	}

	return json.Unmarshal(b, &a)
}

// AuditTrailsDTO represents the audit trails log data transfer object on onedash
type AuditTrailsDTO struct {
	ID             int64         `json:"id" db:"id"`
	CreatedAt      sql.NullTime  `json:"created_at" db:"created_at"`
	CreatedBy      sql.NullInt64 `json:"created_by" db:"created_by"`
	Identifier     string        `json:"identifier" db:"identifier"`
	IdentifierType string        `json:"identifier_type" db:"identifier_type"`
	Title          string        `json:"title" db:"title"`
	Description    string        `json:"description" db:"description"`
	ActivityType   string        `json:"activity_type" db:"activity_type"`
	ReferenceID    string        `json:"reference_id" db:"reference_id"`
	ExtraParams    interface{}   `json:"extra_params" db:"extra_params"`
	UserName       string        `json:"user_name" db:"user_name"`
}

// GetAuditTrailsRequestWithPaginationDto dto that needed to make sure req is not null 'withPagination=true'
type GetAuditTrailsRequestWithPaginationDto struct {
	Limit          int64    `json:"limit,omitempty"`
	StartingBefore string   `json:"startingBefore,omitempty"`
	EndingAfter    string   `json:"endingAfter,omitempty"`
	ActivityType   string   `json:"activityType,omitempty"`
	SortBy         api.Sort `json:"sortBy,omitempty" validate:"omitempty,audit_trails_sort"`
}

// AuditTrailDTO represents the audit trails log data transfer object on onedash
type AuditTrailDTO struct {
	ID             int64          `json:"id" db:"id"`
	Identifier     string         `json:"identifier" db:"identifier"`
	IdentifierType string         `json:"identifier_type" db:"identifier_type"`
	Title          sql.NullString `json:"title" db:"title"`
	Description    sql.NullString `json:"description" db:"description"`
	ActivityType   string         `json:"activity_type" db:"activity_type"`
	ReferenceID    sql.NullString `json:"reference_id" db:"reference_id"`
	ExtraParams    *DataJSON      `json:"extra_params" db:"extra_params"`
	CreatedBy      int64          `json:"created_by" db:"created_by"`
	CreatedAt      sql.NullTime   `json:"created_at" db:"created_at"`
}

// GetField ...
func (d AuditTrailDTO) GetField(withoutID bool) (res []string) {
	res = []string{"id", "identifier", "identifier_type", "title", "description", "activity_type", "reference_id", "extra_params", "created_by", "created_at"}
	if withoutID {
		res = res[1:]
	}

	return res
}

// QueryInsert ...
func (d AuditTrailDTO) QueryInsert() (res string) {
	fields := d.GetField(true)
	colomn, values := "", ""
	for _, v := range fields {
		colomn = fmt.Sprintf("%s,%s", colomn, v)
		values = fmt.Sprintf("%s,%s", values, "?")
	}

	return fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)", d.TableName(), colomn[1:], values[1:])
}

// TableName ...
func (d AuditTrailDTO) TableName() (res string) {
	return "audit_trails"
}
