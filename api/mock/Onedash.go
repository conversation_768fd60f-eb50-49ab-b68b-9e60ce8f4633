// Code generated by mockery v2.44.1. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"

	mock "github.com/stretchr/testify/mock"
)

// Onedash is an autogenerated mock type for the Onedash type
type Onedash struct {
	mock.Mock
}

// BlockAccount provides a mock function with given fields: ctx, req
func (_m *Onedash) BlockAccount(ctx context.Context, req *api.BlockAccountRequest) (*api.BlockAccountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for BlockAccount")
	}

	var r0 *api.BlockAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.BlockAccountRequest) (*api.BlockAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.BlockAccountRequest) *api.BlockAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.BlockAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.BlockAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDocument provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateDocument(ctx context.Context, req *api.CreateDocumentRequest) (*api.CreateDocumentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateDocument")
	}

	var r0 *api.CreateDocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDocumentRequest) (*api.CreateDocumentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDocumentRequest) *api.CreateDocumentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateDocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateElement provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateElement(ctx context.Context, req *api.CreateElementRequest) (*api.CreateElementResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateElement")
	}

	var r0 *api.CreateElementResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateElementRequest) (*api.CreateElementResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateElementRequest) *api.CreateElementResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateElementResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateElementRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateFeatureFlag provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateFeatureFlag(ctx context.Context, req *api.CreateFeatureFlagRequest) (*api.CreateFeatureFlagResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeatureFlag")
	}

	var r0 *api.CreateFeatureFlagResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateFeatureFlagRequest) (*api.CreateFeatureFlagResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateFeatureFlagRequest) *api.CreateFeatureFlagResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateFeatureFlagResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateFeatureFlagRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateModule provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateModule(ctx context.Context, req *api.CreateModuleRequest) (*api.CreateModuleResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateModule")
	}

	var r0 *api.CreateModuleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateModuleRequest) (*api.CreateModuleResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateModuleRequest) *api.CreateModuleResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateModuleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateModuleRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePriority provides a mock function with given fields: ctx, req
func (_m *Onedash) CreatePriority(ctx context.Context, req *api.CreatePriorityRequest) (*api.CreatePriorityResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreatePriority")
	}

	var r0 *api.CreatePriorityResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePriorityRequest) (*api.CreatePriorityResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePriorityRequest) *api.CreatePriorityResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreatePriorityResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreatePriorityRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateStatus provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateStatus(ctx context.Context, req *api.CreateStatusRequest) (*api.CreateStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateStatus")
	}

	var r0 *api.CreateStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateStatusRequest) (*api.CreateStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateStatusRequest) *api.CreateStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateTicket provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateTicket(ctx context.Context, req *api.CreateTicketRequest) (*api.CreateTicketResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateTicket")
	}

	var r0 *api.CreateTicketResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTicketRequest) (*api.CreateTicketResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTicketRequest) *api.CreateTicketResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateTicketResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateTicketRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateTicketComment provides a mock function with given fields: ctx, req
func (_m *Onedash) CreateTicketComment(ctx context.Context, req *api.CreateTicketCommentRequest) (*api.CreateTicketCommentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateTicketComment")
	}

	var r0 *api.CreateTicketCommentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTicketCommentRequest) (*api.CreateTicketCommentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTicketCommentRequest) *api.CreateTicketCommentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateTicketCommentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateTicketCommentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CustomerSearch provides a mock function with given fields: ctx, req
func (_m *Onedash) CustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (*api.CustomerSearchResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CustomerSearch")
	}

	var r0 *api.CustomerSearchResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CustomerSearchRequest) (*api.CustomerSearchResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CustomerSearchRequest) *api.CustomerSearchResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CustomerSearchResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CustomerSearchRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeactivateLOC provides a mock function with given fields: ctx, req
func (_m *Onedash) DeactivateLOC(ctx context.Context, req *api.DeactivateLOCRequest) (*api.DeactivateLOCResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeactivateLOC")
	}

	var r0 *api.DeactivateLOCResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeactivateLOCRequest) (*api.DeactivateLOCResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeactivateLOCRequest) *api.DeactivateLOCResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.DeactivateLOCResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.DeactivateLOCRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteDocument provides a mock function with given fields: ctx, req
func (_m *Onedash) DeleteDocument(ctx context.Context, req *api.DeleteDocumentRequest) (*api.DeleteDocumentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDocument")
	}

	var r0 *api.DeleteDocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteDocumentRequest) (*api.DeleteDocumentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteDocumentRequest) *api.DeleteDocumentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.DeleteDocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.DeleteDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteFeatureFlag provides a mock function with given fields: ctx, req
func (_m *Onedash) DeleteFeatureFlag(ctx context.Context, req *api.DeleteFeatureFlagRequest) (*api.DeleteFeatureFlagResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFeatureFlag")
	}

	var r0 *api.DeleteFeatureFlagResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteFeatureFlagRequest) (*api.DeleteFeatureFlagResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteFeatureFlagRequest) *api.DeleteFeatureFlagResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.DeleteFeatureFlagResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.DeleteFeatureFlagRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccounts provides a mock function with given fields: ctx, req
func (_m *Onedash) GetCustomerAccounts(ctx context.Context, req *api.GetCustomerAccountRequest) (*api.GetCustomerAccountsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccounts")
	}

	var r0 *api.GetCustomerAccountsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCustomerAccountRequest) (*api.GetCustomerAccountsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCustomerAccountRequest) *api.GetCustomerAccountsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCustomerAccountsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCustomerAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerSegements provides a mock function with given fields: ctx
func (_m *Onedash) GetCustomerSegements(ctx context.Context) (*api.GetCustomerSegmentsResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerSegements")
	}

	var r0 *api.GetCustomerSegmentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetCustomerSegmentsResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetCustomerSegmentsResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCustomerSegmentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomers provides a mock function with given fields: ctx, req
func (_m *Onedash) GetCustomers(ctx context.Context, req *api.GetCustomersRequest) (*api.GetCustomersResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomers")
	}

	var r0 *api.GetCustomersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCustomersRequest) (*api.GetCustomersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCustomersRequest) *api.GetCustomersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCustomersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCustomersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomersDataPoint provides a mock function with given fields: ctx, req
func (_m *Onedash) GetCustomersDataPoint(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomersDataPoint")
	}

	var r0 *api.CustomerSearchDataPointResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CustomerSearchDataPointRequest) *api.CustomerSearchDataPointResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CustomerSearchDataPointResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CustomerSearchDataPointRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDataSegregation provides a mock function with given fields: ctx, req
func (_m *Onedash) GetDataSegregation(ctx context.Context, req *api.GetDataSegregationRequest) (*api.GetDataSegregationResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetDataSegregation")
	}

	var r0 *api.GetDataSegregationResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDataSegregationRequest) (*api.GetDataSegregationResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDataSegregationRequest) *api.GetDataSegregationResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDataSegregationResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDataSegregationRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDocument provides a mock function with given fields: ctx, req
func (_m *Onedash) GetDocument(ctx context.Context, req *api.GetDocumentRequest) (*api.GetDocumentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetDocument")
	}

	var r0 *api.GetDocumentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDocumentRequest) (*api.GetDocumentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDocumentRequest) *api.GetDocumentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDocumentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDocumentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetElementByID provides a mock function with given fields: ctx, req
func (_m *Onedash) GetElementByID(ctx context.Context, req *api.GetElementByIDRequest) (*api.GetElementByIDResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetElementByID")
	}

	var r0 *api.GetElementByIDResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementByIDRequest) (*api.GetElementByIDResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementByIDRequest) *api.GetElementByIDResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetElementByIDResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetElementByIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetElementPriorities provides a mock function with given fields: ctx, req
func (_m *Onedash) GetElementPriorities(ctx context.Context, req *api.GetElementPrioritiesRequest) (*api.GetElementPrioritiesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetElementPriorities")
	}

	var r0 *api.GetElementPrioritiesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementPrioritiesRequest) (*api.GetElementPrioritiesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementPrioritiesRequest) *api.GetElementPrioritiesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetElementPrioritiesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetElementPrioritiesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetElements provides a mock function with given fields: ctx, req
func (_m *Onedash) GetElements(ctx context.Context, req *api.GetElementsRequest) (*api.GetElementsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetElements")
	}

	var r0 *api.GetElementsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementsRequest) (*api.GetElementsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetElementsRequest) *api.GetElementsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetElementsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetElementsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureFlag provides a mock function with given fields: ctx, req
func (_m *Onedash) GetFeatureFlag(ctx context.Context, req *api.GetFeatureFlagRequest) (*api.GetFeatureFlagResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureFlag")
	}

	var r0 *api.GetFeatureFlagResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagRequest) (*api.GetFeatureFlagResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagRequest) *api.GetFeatureFlagResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetFeatureFlagResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetFeatureFlagRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureFlagList provides a mock function with given fields: ctx, req
func (_m *Onedash) GetFeatureFlagList(ctx context.Context, req *api.GetFeatureFlagListRequest) (*api.GetFeatureFlagListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureFlagList")
	}

	var r0 *api.GetFeatureFlagListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagListRequest) (*api.GetFeatureFlagListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagListRequest) *api.GetFeatureFlagListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetFeatureFlagListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetFeatureFlagListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLogAuditTrails provides a mock function with given fields: ctx, req
func (_m *Onedash) GetLogAuditTrails(ctx context.Context, req *api.GetLogAuditTrailsRequest) (*api.GetLogAuditTrailsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetLogAuditTrails")
	}

	var r0 *api.GetLogAuditTrailsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLogAuditTrailsRequest) (*api.GetLogAuditTrailsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLogAuditTrailsRequest) *api.GetLogAuditTrailsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLogAuditTrailsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLogAuditTrailsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModules provides a mock function with given fields: ctx, req
func (_m *Onedash) GetModules(ctx context.Context, req *api.GetModulesRequest) (*api.GetModulesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetModules")
	}

	var r0 *api.GetModulesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetModulesRequest) (*api.GetModulesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetModulesRequest) *api.GetModulesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetModulesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetModulesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOptions provides a mock function with given fields: ctx, req
func (_m *Onedash) GetOptions(ctx context.Context, req *api.GetOptionsRequest) (*api.GetOptionsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetOptions")
	}

	var r0 *api.GetOptionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetOptionsRequest) (*api.GetOptionsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetOptionsRequest) *api.GetOptionsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetOptionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetOptionsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPriorities provides a mock function with given fields: ctx, req
func (_m *Onedash) GetPriorities(ctx context.Context, req *api.GetPrioritiesRequest) (*api.GetPrioritiesResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPriorities")
	}

	var r0 *api.GetPrioritiesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPrioritiesRequest) (*api.GetPrioritiesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPrioritiesRequest) *api.GetPrioritiesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPrioritiesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPrioritiesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRolesDataSegregation provides a mock function with given fields: ctx, req
func (_m *Onedash) GetRolesDataSegregation(ctx context.Context, req *api.GetDataSegregationRoleListRequest) (*api.GetDataSegregationRoleListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetRolesDataSegregation")
	}

	var r0 *api.GetDataSegregationRoleListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDataSegregationRoleListRequest) (*api.GetDataSegregationRoleListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDataSegregationRoleListRequest) *api.GetDataSegregationRoleListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDataSegregationRoleListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDataSegregationRoleListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStatuses provides a mock function with given fields: ctx
func (_m *Onedash) GetStatuses(ctx context.Context) (*api.GetStatusesResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetStatuses")
	}

	var r0 *api.GetStatusesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetStatusesResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetStatusesResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetStatusesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketByID provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketByID(ctx context.Context, req *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketByID")
	}

	var r0 *api.GetTicketByIDResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketByIDRequest) *api.GetTicketByIDResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketByIDResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketByIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketChains provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketChains(ctx context.Context, req *api.GetTicketChainsRequest) (*api.GetTicketChainsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketChains")
	}

	var r0 *api.GetTicketChainsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketChainsRequest) (*api.GetTicketChainsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketChainsRequest) *api.GetTicketChainsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketChainsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketChainsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketComments provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketComments(ctx context.Context, req *api.GetTicketCommentsRequest) (*api.GetTicketCommentsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketComments")
	}

	var r0 *api.GetTicketCommentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketCommentsRequest) (*api.GetTicketCommentsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketCommentsRequest) *api.GetTicketCommentsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketCommentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketCommentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketDocuments provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketDocuments(ctx context.Context, req *api.GetTicketDocumentsRequest) (*api.GetTicketDocumentsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketDocuments")
	}

	var r0 *api.GetTicketDocumentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketDocumentsRequest) (*api.GetTicketDocumentsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketDocumentsRequest) *api.GetTicketDocumentsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketDocumentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketDocumentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketFields provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketFields(ctx context.Context, req *api.GetTicketFieldsRequest) (*api.GetTicketFieldsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketFields")
	}

	var r0 *api.GetTicketFieldsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketFieldsRequest) (*api.GetTicketFieldsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketFieldsRequest) *api.GetTicketFieldsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketFieldsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketFieldsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketList provides a mock function with given fields: ctx, req
func (_m *Onedash) GetTicketList(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketList")
	}

	var r0 *api.GetTicketListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketListRequest) (*api.GetTicketListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTicketListRequest) *api.GetTicketListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTicketListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTicketRequestors provides a mock function with given fields: ctx
func (_m *Onedash) GetTicketRequestors(ctx context.Context) (*api.GetTicketRequestorsResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetTicketRequestors")
	}

	var r0 *api.GetTicketRequestorsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetTicketRequestorsResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetTicketRequestorsResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTicketRequestorsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LogAuditTrails provides a mock function with given fields: ctx, req
func (_m *Onedash) LogAuditTrails(ctx context.Context, req *api.LogAuditTrailRequest) (*api.LogAuditTrailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for LogAuditTrails")
	}

	var r0 *api.LogAuditTrailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.LogAuditTrailRequest) (*api.LogAuditTrailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.LogAuditTrailRequest) *api.LogAuditTrailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.LogAuditTrailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.LogAuditTrailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SendNotification provides a mock function with given fields: ctx, req
func (_m *Onedash) SendNotification(ctx context.Context, req *api.SendNotificationRequest) (*api.SendNotificationResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SendNotification")
	}

	var r0 *api.SendNotificationResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.SendNotificationRequest) (*api.SendNotificationResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.SendNotificationRequest) *api.SendNotificationResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.SendNotificationResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.SendNotificationRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransferOnBehalf provides a mock function with given fields: ctx, req
func (_m *Onedash) TransferOnBehalf(ctx context.Context, req *api.PaymentTransferRequest) (*api.PaymentTransferResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for TransferOnBehalf")
	}

	var r0 *api.PaymentTransferResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaymentTransferRequest) (*api.PaymentTransferResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaymentTransferRequest) *api.PaymentTransferResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PaymentTransferResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PaymentTransferRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UnblockAccount provides a mock function with given fields: ctx, req
func (_m *Onedash) UnblockAccount(ctx context.Context, req *api.UnblockAccountRequest) (*api.UnblockAccountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UnblockAccount")
	}

	var r0 *api.UnblockAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UnblockAccountRequest) (*api.UnblockAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UnblockAccountRequest) *api.UnblockAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UnblockAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UnblockAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UnlinkAccount provides a mock function with given fields: ctx, req
func (_m *Onedash) UnlinkAccount(ctx context.Context, req *api.UnlinkAccountRequest) (*api.UnlinkAccountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UnlinkAccount")
	}

	var r0 *api.UnlinkAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UnlinkAccountRequest) (*api.UnlinkAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UnlinkAccountRequest) *api.UnlinkAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UnlinkAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UnlinkAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCASAAccountStatus provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateCASAAccountStatus(ctx context.Context, req *api.UpdateCASAAccountStatusRequest) (*api.UpdateCASAAccountStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCASAAccountStatus")
	}

	var r0 *api.UpdateCASAAccountStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCASAAccountStatusRequest) (*api.UpdateCASAAccountStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCASAAccountStatusRequest) *api.UpdateCASAAccountStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateCASAAccountStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateCASAAccountStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateDataSegregation provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateDataSegregation(ctx context.Context, req *api.UpdateDataSegregationRequest) (*api.UpdateDataSegregationResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDataSegregation")
	}

	var r0 *api.UpdateDataSegregationResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateDataSegregationRequest) (*api.UpdateDataSegregationResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateDataSegregationRequest) *api.UpdateDataSegregationResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateDataSegregationResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateDataSegregationRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateElement provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateElement(ctx context.Context, req *api.UpdateElementRequest) (*api.UpdateElementResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateElement")
	}

	var r0 *api.UpdateElementResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateElementRequest) (*api.UpdateElementResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateElementRequest) *api.UpdateElementResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateElementResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateElementRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateFeatureFlag provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateFeatureFlag(ctx context.Context, req *api.UpdateFeatureFlagRequest) (*api.UpdateFeatureFlagResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeatureFlag")
	}

	var r0 *api.UpdateFeatureFlagResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateFeatureFlagRequest) (*api.UpdateFeatureFlagResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateFeatureFlagRequest) *api.UpdateFeatureFlagResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateFeatureFlagResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateFeatureFlagRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateModule provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateModule(ctx context.Context, req *api.UpdateModuleRequest) (*api.UpdateModuleResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateModule")
	}

	var r0 *api.UpdateModuleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateModuleRequest) (*api.UpdateModuleResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateModuleRequest) *api.UpdateModuleResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateModuleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateModuleRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdatePriority provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdatePriority(ctx context.Context, req *api.UpdatePriorityRequest) (*api.UpdatePriorityResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePriority")
	}

	var r0 *api.UpdatePriorityResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePriorityRequest) (*api.UpdatePriorityResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePriorityRequest) *api.UpdatePriorityResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdatePriorityResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdatePriorityRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateStatus provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateStatus(ctx context.Context, req *api.UpdateStatusRequest) (*api.UpdateStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStatus")
	}

	var r0 *api.UpdateStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateStatusRequest) (*api.UpdateStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateStatusRequest) *api.UpdateStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTicket provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateTicket(ctx context.Context, req *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTicket")
	}

	var r0 *api.UpdateTicketResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTicketRequest) *api.UpdateTicketResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateTicketResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateTicketRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTicketAssignee provides a mock function with given fields: ctx, req
func (_m *Onedash) UpdateTicketAssignee(ctx context.Context, req *api.UpdateTicketAssigneeRequest) (*api.UpdateTicketAssigneeResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTicketAssignee")
	}

	var r0 *api.UpdateTicketAssigneeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTicketAssigneeRequest) (*api.UpdateTicketAssigneeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTicketAssigneeRequest) *api.UpdateTicketAssigneeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateTicketAssigneeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateTicketAssigneeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewOnedash creates a new instance of Onedash. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOnedash(t interface {
	mock.TestingT
	Cleanup(func())
}) *Onedash {
	mock := &Onedash{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
