package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	productMaster "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	productMasterMock "gitlab.myteksi.net/bersama/core-banking/product-master/api/mock"
	txHistory "gitlab.myteksi.net/bersama/transaction-history/api"
	txHistoryMock "gitlab.myteksi.net/bersama/transaction-history/api/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search Data Point", func() {
	var (
		db *sql.DB
		//mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		//db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockTxHistory = &txHistoryMock.TxHistory{}
		mockProductMaster = &productMasterMock.ProductMaster{}

		config := &logic.MockProcessConfig{
			TransactionHistory: mockTxHistory,
			ProductMaster:      mockProductMaster,
			AppConfig:          service.AppConfig,
		}
		logic.MockInitLogic(config)

		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		auditTrailLogic.MockInitLogic(service.AppConfig)
		permissionManagementLogic.MockInitLogic(service.AppConfig)
	})

	//AfterEach(func() {
	//	db.Close()
	//})

	Context("CustomerSearchDataPoint", func() {
		When("user has valid permissions & get last sucessful deduction", func() {
			It("should successfully return customer data point result", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"key": "piggyLastSuccessDeduction",
					"payload": {
						"accountID": "***************",
						"safeID": "3e75913d-e567-4350-82e2-4691a375f1d7"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				var expectedResponseLastTrxList = `{
					"links": {
						"next": "/v1/accounts/***************/transactions?pageSize=1&startingBefore=************************************************************************************************************************************************",
						"nextCursorID": "************************************************************************************************************************************************",
						"prev": "",
						"prevCursorID": ""
					},
					"data": [
						{
							"transactionID": "pt01_dm01_***************_5_se01_***************_1735318860000000000",
							"batchID": "***************_5_se01_***************_1735318860000000000",
							"displayName": "Pajak atas Bunga",
							"iconURL": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/tax_on_interest.png",
							"amount": -832320,
							"currency": "IDR",
							"status": "COMPLETED",
							"creationTimestamp": "2024-12-27T17:01:00Z",
							"category": {},
							"endingBalance": *********,
							"transactionType": "TAX_PAYOUT",
							"transactionSubtype": "SAVINGS",
							"transactionDescription": "Pajak atas Bunga",
							"counterParty": {
								"displayName": "Pajak atas Bunga",
								"iconURL": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/tax_on_interest.png"
							},
							"updateTimestamp": "2024-12-27T17:01:00Z"
						}
					]
				}`

				var mockResponse *txHistory.GetTransactionsHistoryResponse
				_ = json.Unmarshal([]byte(expectedResponseLastTrxList), &mockResponse)

				mockTxHistory.On("ListAccountTransactionsSearch", mock.Anything, &txHistory.GetTransactionsHistoryRequest{
					AccountID: "***************",
					PageSize:  1,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"data": "2024-12-27T17:01:00Z"
				}`

				response, err := client.Post(CustomerSearchDataPoint, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get piggybank goal amount", func() {
			It("should successfully return customer data point result", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"key": "piggyGoalAmount"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyPiggybankGoalAmount, constants.ProductVariantPiggybank)
				// get from redis
				mockRedis.On("GetString", mock.Anything, redisKey).Return("", nil)

				var expectedResponseProductVariantParams = `{
					"productVariantParameters": [
						{
							"id": "f3e65965-bfd1-11ec-b1b7-0aed065bc0fc",
							"productVariantID": "2950f9c6-6268-11ee-bcb8-0e044c1343b4",
							"namespace": "risk",
							"parameterKey": "allowed_hold_codes",
							"parameterValue": "WHOLE_BALANCE_HOLD",
							"dataType": "ENUM",
							"overrideLevel": "NO_OVERRIDE",
							"exceptionLevel": "NO_OVERRIDE",
							"description": "Is the list of allowed hold codes for this product",
							"createdBy": "MANUAL",
							"createdAt": "2022-11-28T03:54:19Z",
							"updatedBy": "MANUAL",
							"updatedAt": "2022-11-28T03:54:19Z"
						},
						{
							"id": "2fcc5572-6268-11ee-bcb8-0e044c1343b4",
							"productVariantID": "2950f9c6-6268-11ee-bcb8-0e044c1343b4",
							"namespace": "business",
							"parameterKey": "hard_deposit_cap_limit",
							"parameterValue": "5000000",
							"dataType": "DOUBLE",
							"overrideLevel": "NO_OVERRIDE",
							"description": "Is the maximum balance limit beyond which any credits are rejected",
							"createdBy": "MANUAL",
							"createdAt": "2023-10-04T03:43:31Z",
							"updatedBy": "MANUAL",
							"updatedAt": "2023-10-04T03:43:31Z"
						},
						{
							"id": "2fcc56ba-6268-11ee-bcb8-0e044c1343b4",
							"productVariantID": "2950f9c6-6268-11ee-bcb8-0e044c1343b4",
							"namespace": "business",
							"parameterKey": "max_accounts_per_customer",
							"parameterValue": "1",
							"dataType": "INT",
							"overrideLevel": "NO_OVERRIDE",
							"description": "Limit to how many accounts of this product variant a customer is allowed to have",
							"createdBy": "MANUAL",
							"createdAt": "2023-10-04T03:43:31Z",
							"updatedBy": "MANUAL",
							"updatedAt": "2023-10-04T03:43:31Z"
						},
						{
							"id": "2fcbd07a-6268-11ee-bcb8-0e044c1343b4",
							"productVariantID": "2950f9c6-6268-11ee-bcb8-0e044c1343b4",
							"namespace": "business",
							"parameterKey": "account_applicable_features",
							"parameterValue": "{\"MICROSAVER\": true}",
							"dataType": "STRING",
							"overrideLevel": "NO_OVERRIDE",
							"description": "The features that are applicable to an account",
							"createdBy": "MANUAL",
							"createdAt": "2023-10-04T03:43:31Z",
							"updatedBy": "MANUAL",
							"updatedAt": "2023-10-04T03:43:31Z"
						},
						{
							"id": "2fcc52bd-6268-11ee-bcb8-0e044c1343b4",
							"productVariantID": "2950f9c6-6268-11ee-bcb8-0e044c1343b4",
							"namespace": "business",
							"parameterKey": "tm_product_id",
							"parameterValue": "deposit_product",
							"dataType": "STRING",
							"overrideLevel": "NO_OVERRIDE",
							"description": "The features that are applicable to an account",
							"createdBy": "MANUAL",
							"createdAt": "2023-10-04T03:43:31Z",
							"updatedBy": "MANUAL",
							"updatedAt": "2023-10-04T03:43:31Z"
						}
					]
				}`

				var mockResponse *productMaster.ListEffectiveProductVariantParametersResponse
				_ = json.Unmarshal([]byte(expectedResponseProductVariantParams), &mockResponse)

				mockProductMaster.On("ListEffectiveProductVariantParameters", mock.Anything, &productMaster.ListEffectiveProductVariantParametersRequest{
					ProductVariantCode: constants.ProductVariantPiggybank,
				}).Return(mockResponse, nil).Once()

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				expectedResponse := `{
					"data": "5,000,000.00"
				}`

				response, err := client.Post(CustomerSearchDataPoint, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
