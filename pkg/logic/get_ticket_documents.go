package logic

import (
	"context"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetTicketDocuments gets ticket documents by ticket id
func (p *process) GetTicketDocuments(ctx context.Context, req *api.GetTicketDocumentsRequest) (*api.GetTicketDocumentsResponse, error) {
	// check permission using common authentication: TODO

	// validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get ticket documents
	docs, err := storage.GetTicketDocuments(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket documents")
	}

	return &api.GetTicketDocumentsResponse{
		Documents: makeDocumentResponse(docs),
	}, nil
}

func makeDocumentResponse(docs []*storage.DocumentListDTO) []api.Document {
	var documents []api.Document
	for _, doc := range docs {
		documents = append(documents, api.Document{
			Id:          doc.ID,
			Name:        doc.Name,
			Url:         doc.URL,
			CreatedAt:   utils.DateAsString(doc.CreatedAt.Time),
			CreatedBy:   doc.CreatedBy.String,
			Type:        doc.Type.String,
			Description: doc.Description.String,
		})
	}
	return documents
}
