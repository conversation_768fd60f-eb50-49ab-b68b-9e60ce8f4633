// Package redis defines the client to communicate with redis instance.
package redis

import (
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

// Config defines the configuration for redis connection.
type Config struct {
	Addr               string `json:"addr"`
	PoolSize           int    `json:"poolSize"`
	ReadTimeoutInSec   int    `json:"readTimeoutInSec"`
	WriteTimeoutInSec  int    `json:"writeTimeoutInSec"`
	IdleTimeoutInSec   int    `json:"idleTimeoutInSec"`
	ReadOnlyFromSlaves bool   `json:"readOnlyFromSlaves"`
	Password           string `json:"password"`
	TLSEnabled         bool   `json:"tlsEnabled"`
}

const (
	defaultDialTimeout = 5 * time.Second
	defaultMaxExpiry   = 168 * time.Hour

	logTag     = "redis_cli"
	elapsedKey = "elapsed"
)

var (
	redisNewCluster = redis.NewClusterClient
	redisNewClient  = redis.NewClient
)

// MakeClusterClient creates a cluster client.
func MakeClusterClient(config *Config, options ...Option) Client {
	if len(config.Addr) == 0 {
		panic("no addr specified, cannot initiate the redis cluster client")
	}
	_, cancelFunc := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelFunc()

	conf := &redisConfig{
		options: redis.UniversalOptions{
			Addrs:          []string{config.Addr},
			ReadOnly:       true,
			RouteByLatency: true,
			Password:       config.Password,
			DialTimeout:    defaultDialTimeout,
			ReadTimeout:    time.Duration(config.ReadTimeoutInSec) * time.Second,
			WriteTimeout:   time.Duration(config.WriteTimeoutInSec) * time.Second,
			PoolSize:       config.PoolSize,
			IdleTimeout:    time.Duration(config.IdleTimeoutInSec) * time.Second,
		},
		stats:  statsd.NewNoop(),
		logger: slog.FallbackLogger(),
	}
	if config.ReadOnlyFromSlaves {
		ReadOnlyFromSlaves(true)(conf)
	}
	if config.TLSEnabled {
		WithTLSConfig(&tls.Config{
			MinVersion: tls.VersionTLS12,
		})(conf)
	}

	for _, opt := range options {
		opt(conf)
	}

	universalCli := redisNewClient(conf.options.Simple())
	return &client{
		redisCli:      universalCli,
		clustered:     true,
		maxExpiryTime: defaultMaxExpiry,
		stats:         conf.stats,
		logger:        conf.logger,
	}
}

type universalClientWrapper interface {
	redis.UniversalClient
}

//go:generate mockery --case=underscore --name=universalClientWrapper --inpackage

type client struct {
	redisCli      universalClientWrapper
	maxExpiryTime time.Duration
	clustered     bool

	stats  statsd.Client
	logger slog.YallLogger
}

func (c *client) setMaxExpiry(expiryTime time.Duration) time.Duration {
	if expiryTime > c.maxExpiryTime {
		return c.maxExpiryTime
	}
	return expiryTime
}

func (c *client) normalizeError(err error) error {
	if err == redis.Nil {
		return ErrNoData
	}
	return err
}

func (c *client) getStatsTags(operations ...redis.Cmder) []string {
	var statsTags []string
	statsTags = append(statsTags, fmt.Sprintf("clustered:%t", c.clustered))
	for _, operation := range operations {
		statsTags = append(statsTags, fmt.Sprintf("operation:%s", strings.ReplaceAll(operation.FullName(), " ", "_")))
		if err := operation.Err(); err != nil {
			statsTags = append(statsTags, fmt.Sprintf("error:%s", strings.ReplaceAll(err.Error(), " ", "_")))
		}
	}
	return statsTags
}
