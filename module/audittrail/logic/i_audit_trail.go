package logic

import (
	"context"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
)

//go:generate mockery --name=IAuditTrail --output=mock_i_audit_trail --inpackage
type IAuditTrail interface {
	GetAuditTrails(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error)
	ConstructAndInsertAuditTrails(ctx context.Context, createdBy int64, identifier string, identifierType string, title string, description string, activityType string) (int64, error)
	InsertAuditTrails(ctx context.Context, data *auditTrailStorage.AuditTrailsDTO) (int64, error)
	CreateAuditTrail(ctx context.Context, req *api.AuditTrailRequest) (*api.AuditTrailResponse, error)
}
