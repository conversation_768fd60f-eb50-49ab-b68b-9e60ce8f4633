package server

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	customerExperienceClient "gitlab.myteksi.net/bersama/customer-experience/api/client"
	customerJournalClient "gitlab.myteksi.net/bersama/customer-journal/api/client"
	customerMasterClient "gitlab.myteksi.net/bersama/customer-master/api/v2/client"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/tracing"
	paymentOpsTrfClient "gitlab.myteksi.net/dakota/payment-ops-trf/api/client"
	payAuthZClient "gitlab.myteksi.net/dakota/payment/pay-authz/api/client"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	transactionLimitClient "gitlab.myteksi.net/dakota/transaction-limit/api/client"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	customerJourneyExperienceClient "gitlab.super-id.net/bersama/corex/customer-journey-experience/api/client"
	amlServiceClient "gitlab.super-id.net/bersama/fintrust/aml-service/api/client"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/consumers"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/chatbot"
	customerExperienceHttpClient "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/customerportal"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
	"gitlab.super-id.net/bersama/opsce/onedash-be/handlers"
	auditTrailHandlers "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/handlers"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementHandler "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/handlers"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/queue"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/scheduler"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/retryablewrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"

	accountServiceClient "gitlab.myteksi.net/bersama/core-banking/account-service/api/client"
	productMasterClient "gitlab.myteksi.net/bersama/core-banking/product-master/api/client"
	transactionHistoryClient "gitlab.myteksi.net/bersama/transaction-history/api/client"
	loanAppClient "gitlab.myteksi.net/dakota/lending/loan-app/api/client"
	loanCoreClient "gitlab.myteksi.net/dakota/lending/loan-core/api/client"
	loanExpClient "gitlab.myteksi.net/dakota/lending/loan-exp/api/client"
	dbmyAccountServiceClient "gitlab.myteksi.net/dbmy/core-banking/account-service/api/client"

	grabdIDClient "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig"
)

// Serve ...
//
// nolint: funlen
func Serve() {
	// Initializing with own configuration
	appCfg := &config.AppConfig{}
	app := servus.Default(
		servus.WithAppConfig(appCfg),
	)

	// initialize db
	commonStorage.Init(app.GetStatsD())
	redisClient := redis.Init(app, appCfg)

	// initialize jumpcloud
	jumpcloud.Init(appCfg.Jumpcloud)

	app.MustRegister("client.accountService", newAccountServiceClient)
	app.MustRegister("client.customerPortal", newCustomerPortalClient)
	app.MustRegister("client.payAuthZ", newPayAuthZClient)
	app.MustRegister("client.paymentOpsTrf", newPaymentOpsTrfClient)
	app.MustRegister("client.Hedwig", newHedwigClient)
	app.MustRegister("client.customerMaster", newCustomerMasterClient)
	app.MustRegister("client.customerExperience", newCustomerExperienceClient)
	app.MustRegister("client.productMaster", newProductMasterClient)
	app.MustRegister("client.transactionHistory", newTransactionHistoryClient)
	app.MustRegister("client.transactionLimit", newTransactionLimitClient)
	app.MustRegister("client.customerJournal", newCustomerJournalClient)
	app.MustRegister("client.customerJourneyExperiencePreference", newCustomerJourneyExperiencePreferenceClient)
	app.MustRegister("client.amlServiceCustomer", newAmlServiceCustomerClient)
	app.MustRegister("queue.crmLogAuditTrail", newCrmLogAuditTrailQueue)
	app.MustRegister("queue.opsUnlinkAccount", newOpsUnlinkAccountQueue)
	app.MustRegister("queue.opsTransferOnBehalf", newOpsTransferOnBehalfQueue)
	app.MustRegister("queue.opsBlockAccount", newOpsBlockAccountQueue)
	app.MustRegister("queue.opsUnblockAccount", newOpsUnblockAccountQueue)
	app.MustRegister("queue.opsDeactivateLOC", newOpsDeactivateLOCQueue)
	app.MustRegister("queue.opsUpdateAccountStatus", newOpsUpdateAccountStatusQueue)
	app.MustRegister("client.redisClient", redisClient)
	app.MustRegister("client.s3", newS3Client)
	app.MustRegister("client.appian", appianClient)
	app.MustRegister("client.chatbot", newChatbotClient)
	app.MustRegister("client.grabid", newGrabIDClient)
	app.MustRegister("client.dbmyAccountService", newDbmyAccountServiceClient)

	auditTrailLogic.InitLogic(app, appCfg)
	auditTrailService := &auditTrailHandlers.AuditTrailService{}
	app.MustRegister("auditTrailService", auditTrailService)
	auditTrailService.RegisterRoutes(app)

	permissionManagementLogic.InitLogic(app, appCfg)
	permissionManagementService := &permissionManagementHandler.PermissionManagementService{}
	app.MustRegister("permissionManagementService", permissionManagementService)
	permissionManagementService.RegisterRoutes(app)
	app.MustRegister("client.loanCore", newLoanCoreClient)
	app.MustRegister("client.loanExp", newLoanExpClient)
	app.MustRegister("client.loanApp", newLoanAppClient)
	app.MustRegister("client.customerExperienceHttp", newCustomerExperienceHttpClient)

	service := &handlers.OnedashService{}
	app.MustRegister("service", service)

	switch appCfg.Locale.HomeCountry {
	case constants.HomeCountryID:
		registerIndonesiaSpecificServices(app, appCfg)

	}

	service.RegisterRoutes(app)

	retryablewrapper.Init(appCfg)
	validations.InitValidator(appCfg.Locale.HomeCountry)

	// Register the required logic after all dependencies are registered
	logic.InitLogic(app)

	//Start Consumers for Kafka
	consumers.Init(appCfg, app.GetStatsD())

	// Start Consumers (SQS)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	startConsumer(ctx, service)

	// shutdown the required consumers gracefully
	shutdownConsumer(app, service, appCfg)

	background.Init(app)
	app.Run()
}

func registerIndonesiaSpecificServices(app *servus.Application, appCfg *config.AppConfig) {
	handlers.RegisterOpsLendingActionKafkaWriter(appCfg, app)

	opsService := &handlers.OpsService{}
	app.MustRegister("opsService", opsService)
	opsService.RegisterRoutes(app)

	//for cron
	scheduler := registerSchedulers(opsService, appCfg, app.GetLogger())
	app.OnShutdown(func(ctx context.Context) {
		scheduler.Stop()
		background.Shutdown()
	})
}

func registerSchedulers(service *handlers.OpsService, appCfg *config.AppConfig, logger slog.YallLogger) *gocron.Scheduler {
	ctx := slog.NewContextWithLogger(context.Background(), logger)
	cron := gocron.NewScheduler(time.UTC)

	scheduler.ScheduleWriteOffExporterJob(ctx, service, appCfg, cron)
	cron.StartAsync()

	return cron
}

// appianClient ...
func appianClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*appian.Client, error) {
	return appian.NewAppianClient(appCfg.Appian.Config, tracer)
}

func shutdownConsumer(app *servus.Application, service *handlers.OnedashService, appCfg *config.AppConfig) {
	app.OnShutdown(func(ctx context.Context) {
		//if service.QueueCrmLogAuditTrail != nil {
		//	service.QueueCrmLogAuditTrail.ShutdownConsumer(ctx)
		//}
		//if service.QueueOpsUnlinkAccount != nil {
		//	service.QueueOpsUnlinkAccount.ShutdownConsumer(ctx)
		//}
		//if service.QueueOpsTransferOnBehalf != nil {
		//	service.QueueOpsTransferOnBehalf.ShutdownConsumer(ctx)
		//}
		//if service.QueueOpsDeactivateLOC != nil {
		//	service.QueueOpsDeactivateLOC.ShutdownConsumer(ctx)
		//}
		//if service.QueueOpsUpdateAccountStatus != nil {
		//	service.QueueOpsUpdateAccountStatus.ShutdownConsumer(ctx)
		//}
		if service.QueueOpsBlockAccount != nil {
			service.QueueOpsBlockAccount.ShutdownConsumer(ctx)
		}
		if service.QueueOpsUnblockAccount != nil {
			service.QueueOpsUnblockAccount.ShutdownConsumer(ctx)
		}
		consumers.Stop(appCfg)
	})
}

// startConsumer starts the required consumers. If your client doesn't require to consume any message, you can skip this function.
func startConsumer(ctx context.Context, service *handlers.OnedashService) {
	if service.QueueCrmLogAuditTrail != nil && service.QueueCrmLogAuditTrail.IsEnabled() {
		service.QueueCrmLogAuditTrail.StartConsumer(ctx)
	}

	if service.QueueOpsUnlinkAccount != nil && service.QueueOpsUnlinkAccount.IsEnabled() {
		service.QueueOpsUnlinkAccount.StartConsumer(ctx)
	}

	if service.QueueOpsTransferOnBehalf != nil && service.QueueOpsTransferOnBehalf.IsEnabled() {
		service.QueueOpsTransferOnBehalf.StartConsumer(ctx)
	}

	if service.QueueOpsBlockAccount != nil && service.QueueOpsBlockAccount.IsEnabled() {
		service.QueueOpsBlockAccount.StartConsumer(ctx)
	}

	if service.QueueOpsUnblockAccount != nil && service.QueueOpsUnblockAccount.IsEnabled() {
		service.QueueOpsUnblockAccount.StartConsumer(ctx)
	}

	if service.QueueOpsDeactivateLOC != nil && service.QueueOpsDeactivateLOC.IsEnabled() {
		service.QueueOpsDeactivateLOC.StartConsumer(context.Background())
	}

	if service.QueueOpsUpdateAccountStatus != nil && service.QueueOpsUpdateAccountStatus.IsEnabled() {
		service.QueueOpsUpdateAccountStatus.StartConsumer(ctx)
	}
}

func newCrmLogAuditTrailQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.AuditTrailQueue, q.QueueLogAuditTrailsMessage, nil)
}

func newOpsUnlinkAccountQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.UnlinkAccountQueue, q.OpsUnlinkAccountQueueHandler, nil)
}

func newOpsTransferOnBehalfQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.TransferOnBehalfQueue, q.OpsTransferOnBehalfQueueHandler, nil)
}

func newOpsBlockAccountQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.BlockAccountQueue, q.OpsBlockAccountQueueHandler, nil)
}

func newOpsUnblockAccountQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.UnblockAccountQueue, q.OpsUnblockAccountQueueHandler, nil)
}

func newOpsDeactivateLOCQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.DeactivateLOCQueue, q.OpsDeactivateLOCQueueHandler, nil)
}

func newOpsUpdateAccountStatusQueue(appCfg *config.AppConfig) (sqs.QueueClient, error) {
	q := queue.NewQueueService(appCfg)
	return sqs.NewQueueClient(context.Background(), appCfg.UpdateAccountStatusQueue, q.OpsUpdateAccountStatusQueueHandler, nil)
}

func newAccountServiceClient(appCfg *config.AppConfig) (*accountServiceClient.AccountServiceClient, error) {
	return accountServiceClient.NewAccountServiceClient(appCfg.AccountServiceConfig.BaseURL)
}

func newCustomerPortalClient(appCfg *config.AppConfig) (customerportal.CustomerPortal, error) {
	return customerportal.NewCustomerPortalClient(appCfg.CustomerPortalConfig, nil)
}

func newPayAuthZClient(appCfg *config.AppConfig) (*payAuthZClient.PayAuthzClient, error) {
	return payAuthZClient.NewPayAuthzClient(appCfg.PayAuthZConfig.BaseURL)
}

func newPaymentOpsTrfClient(appCfg *config.AppConfig) (*paymentOpsTrfClient.PaymentOpsTransferClient, error) {
	return paymentOpsTrfClient.NewPaymentOpsTransferClient(appCfg.PaymentOpsTrf.BaseURL)
}

// HedwigClient ...
func newHedwigClient(appCfg *config.AppConfig) (*hedwig.Client, error) {
	return hedwig.NewHedwigClient(&appCfg.Hedwig.ClientConfig, nil)
}

func newCustomerMasterClient(appCfg *config.AppConfig) (*customerMasterClient.CustomerMasterClient, error) {
	return customerMasterClient.NewCustomerMasterClient(appCfg.CustomerMasterConfig.BaseURL)
}

func newCustomerExperienceClient(appCfg *config.AppConfig) (*customerExperienceClient.CustomerExperienceClient, error) {
	return customerExperienceClient.NewCustomerExperienceClient(appCfg.CustomerExperienceConfig.BaseURL)
}

func newCustomerExperienceHttpClient(appCfg *config.AppConfig) (*customerExperienceHttpClient.CustomerExperienceClient, error) {
	return customerExperienceHttpClient.NewCustomerExperienceHTTPClient(appCfg.CustomerExperienceConfig.BaseURL)
}

func newProductMasterClient(appCfg *config.AppConfig) (*productMasterClient.ProductMasterClient, error) {
	return productMasterClient.NewProductMasterClient(appCfg.ProductMasterConfig.BaseURL)
}

func newTransactionHistoryClient(appCfg *config.AppConfig) (*transactionHistoryClient.TxHistoryClient, error) {
	return transactionHistoryClient.NewTxHistoryClient(appCfg.TransactionHistoryConfig.BaseURL)
}

func newTransactionLimitClient(appCfg *config.AppConfig) (*transactionLimitClient.TransactionLimitClient, error) {
	return transactionLimitClient.NewTransactionLimitClient(appCfg.TransactionLimitConfig.BaseURL)
}

func newCustomerJournalClient(appCfg *config.AppConfig) (*customerJournalClient.CustomerJournalClient, error) {
	return customerJournalClient.NewCustomerJournalClient(appCfg.CustomerJournalConfig.BaseURL)
}

func newCustomerJourneyExperiencePreferenceClient(appCfg *config.AppConfig) (*customerJourneyExperienceClient.PreferenceCenterClient, error) {
	return customerJourneyExperienceClient.NewPreferenceCenterClient(appCfg.CustomerJourneyExperienceConfig.BaseURL)
}

func newAmlServiceCustomerClient(appCfg *config.AppConfig) (*amlServiceClient.CustomerClient, error) {
	return amlServiceClient.NewCustomerClient(appCfg.AmlServiceConfig.BaseURL)
}

// S3Client ...
func newS3Client() (interface{}, error) {
	return s3client.NewS3Client()
}

func newChatbotClient(appCfg *config.AppConfig) (chatbot.Chatbot, error) {
	return chatbot.NewChatbotClient(appCfg.ChatbotServiceConfig, nil)
}

func newGrabIDClient(appCfg *config.AppConfig) (*grabdIDClient.Client, error) {
	return grabdIDClient.NewGrabIDClient(&appCfg.GrabIDConfig.ClientConfig, nil)
}

func newDbmyAccountServiceClient(appCfg *config.AppConfig) (*dbmyAccountServiceClient.AccountServiceClient, error) {
	return dbmyAccountServiceClient.NewAccountServiceClient(appCfg.AccountServiceConfig.BaseURL)
}

func newLoanAppClient(appCfg *config.AppConfig) (*loanAppClient.LoanAppClient, error) {
	return loanAppClient.NewLoanAppClient(appCfg.LoanAppConfig.BaseURL)
}

func newLoanExpClient(appCfg *config.AppConfig) (*loanExpClient.LoanExpClient, error) {
	return loanExpClient.NewLoanExpClient(appCfg.LoanExpConfig.BaseURL)
}

func newLoanCoreClient(appCfg *config.AppConfig) (*loanCoreClient.LoanCoreClient, error) {
	return loanCoreClient.NewLoanCoreClient(appCfg.LoanCoreConfig.BaseURL)
}
