package logic

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

// Logout ...
func (p *PermissionManagementImpl) Logout(ctx context.Context) (*api.LogoutResponse, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, err
	}

	// Get user
	user, db, err := p.GetUser(ctx, claims)
	if err != nil {
		return nil, err
	}

	// delete user expiry token
	err = storage.UpdateUserExpiryToken(ctx, db, "", user.UserID, user.ID)
	if err != nil {
		return nil, err
	}

	// delete redis cache
	err = redis.DeleteRedisKey(ctx, constants.UserIDRedisKey+user.UserID, constants.LogoutLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "Error delete redis key")
	}

	// delete concurrent login
	err = p.deleteConcurrentLogin(ctx, user.UserID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "Error delete concurrent login")
	}

	// write audit trail
	err = helper.WriteLogAuditTrailUser(ctx, user, "Logout", "Logout Successful", constants.Logout, user.ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogoutLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	return &api.LogoutResponse{
		Status: constants.SuccessStatus,
	}, nil
}
