package helper

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	customerJourneyExperience "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func logErrorCustomerJourneyExperience(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.CustomerJourneyExperienceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetConsentListInternal ...
func GetConsentListInternal(ctx context.Context, request *customerJourneyExperience.GetConsentInternalRequest, logTag string, c customerJourneyExperience.PreferenceCenter) (*customerJourneyExperience.GetUserConsentResponse, error) {
	response, err := c.GetConsentListInternal(ctx, request)
	if err != nil {
		logErrorCustomerJourneyExperience(ctx, err, logTag)
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to customer journey experience request")
	}
	return response, nil
}
