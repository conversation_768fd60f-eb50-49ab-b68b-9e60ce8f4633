package api

import (
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerExperience "gitlab.myteksi.net/bersama/customer-experience/api"
	customerMasterMock "gitlab.myteksi.net/bersama/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Customer Accounts", func() {
	BeforeEach(func() {
		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockAccountService = &accountServiceMock.AccountService{}

		config := &logic.MockProcessConfig{
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AccountService:     mockAccountService,
			AppConfig:          service.AppConfig,
		}
		logic.MockInitLogic(config)
	})

	Context("GetCustomerAccounts", func() {
		When("user has valid permissions", func() {
			It("should successfully return customer search results", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "+*************",
    				"identifierType": "PHONE_NUMBER",
   					"page": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				// expectedResponseForCustomerDetailsByIdentifierV2 ...
				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
				    "isLastPage": true,
					"items": [
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
								"NIK": "3009007274171606",
								"addresses": null,
								"alias": "",
								"contacts": [{
									"phoneNumber": "+*************"
								}],
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"fullName": "ade",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"applications": [{
								"ID": "",
								"channel": "",
								"status": "SOFT_REJECTED",
								"statusRemarks": "",
								"ktpFile": null,
								"selfieFile": null,
								"expiresAt": "0001-01-01T00:00:00Z",
								"createdAt": "2024-11-25T04:01:05Z",
								"updatedAt": "2024-11-25T04:02:26Z"
							}]
						},
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107a",
								"NIK": "3009007274171607",
								"addresses": null,
								"alias": "",
								"contacts": [{
									"phoneNumber": "+*************"
								}],
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"fullName": "testing",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"applications": [{
								"ID": "",
								"channel": "",
								"status": "APPROVED",
								"statusRemarks": "",
								"ktpFile": null,
								"selfieFile": null,
								"expiresAt": "0001-01-01T00:00:00Z",
								"createdAt": "2024-11-25T04:01:05Z",
								"updatedAt": "2024-11-25T04:02:26Z"
							}]
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockResponse)

				// mock for GetCustomersOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "+*************",
					IdentifierType: "PHONE_NUMBER",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"isLastPage": true,
					"customers": [
					  {
						"applicationStatus": "SOFT_REJECTED",
						"cif": "ID195849217102",
						"createdAt": "2024-11-25 04:01:05 +0000 UTC",
						"customerStatus": "ONBOARDED",
						"dateOfBirth": "1991-07-16",
						"fullName": "ade",
						"nik": "3009007274171606",
						"phoneNumber": "+*************",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"updatedAt": "2024-11-25 04:02:26 +0000 UTC"
					  },
					  {
						"applicationStatus": "APPROVED",
						"cif": "ID195849217102",
						"createdAt": "2024-11-25 04:01:05 +0000 UTC",
						"customerStatus": "ONBOARDED",
						"dateOfBirth": "1991-07-16",
						"fullName": "testing",
						"nik": "3009007274171607",
						"phoneNumber": "+*************",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107a",
						"updatedAt": "2024-11-25 04:02:26 +0000 UTC"
					  }
					]
				  }`

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"+*************", "PHONE_NUMBER", auditTrail.Title, "VALID", auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user does not have permissions", func() {
			It("should return forbidden error", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(response.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
	})
})
