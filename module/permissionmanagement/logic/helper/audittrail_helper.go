package helper

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

func WriteLogAuditTrailUser(ctx context.Context, user *storage.UserDTO, title string, description string, activityType string, createdBy int64) error {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     user.UserID,
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: createdBy,
			Valid: true,
		},
		Title:        title,
		Description:  description,
		ActivityType: activityType,
	}
	_, err := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return err
}

func WriteLogAuditTrailPermission(ctx context.Context, perm *storage.PermissionDTO, title string, creatorID int64) (*auditTrailStorage.AuditTrailsDTO, error) {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     strconv.FormatInt(perm.ID, 10),
		IdentifierType: constants.PermissionID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: creatorID,
			Valid: true,
		},
		Title:        title,
		Description:  fmt.Sprintf("%v for ID %v : %v executed successfully", title, perm.ID, perm.Name),
		ActivityType: constants.Permission,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return auditTrailsDTO, auditErr
}

func WriteLogAuditTrailRole(ctx context.Context, role *storage.RoleDTO, title string, description string, creatorID int64) (*auditTrailStorage.AuditTrailsDTO, error) {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     strconv.FormatInt(role.ID, 10),
		IdentifierType: constants.RoleID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: creatorID,
			Valid: true,
		},
		Title:        title,
		Description:  description,
		ActivityType: constants.Role,
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return auditTrailsDTO, auditErr
}
