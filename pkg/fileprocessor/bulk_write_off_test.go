// Package fileprocessor ...
//
// nolint: errcheck, gosec
package fileprocessor

import (
	"context"
	"encoding/base64"
	"errors"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/redis"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor/mocks"
)

func TestNewBulkWriteOffFileProcessor(t *testing.T) {
	tests := []struct {
		name string
		want *WriteOffFileProcessor
	}{
		{
			name: "default initialization",
			want: &WriteOffFileProcessor{
				CoreFileProcessor: &core{
					Usecase: WriteOffFileProcessorUsecase,
				},
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			got := NewBulkWriteOffFileProcessor()

			// Check that CoreFileProcessor is not nil
			if got.CoreFileProcessor == nil {
				t.Errorf("NewBulkWriteOffFileProcessor() CoreFileProcessor is nil")
			} else {
				// Check that CoreFileProcessor is of the right type
				coreProcessor, ok := got.CoreFileProcessor.(*core)
				if !ok {
					t.Errorf("NewBulkWriteOffFileProcessor() CoreFileProcessor is not of type *core")
				} else if coreProcessor.Usecase != WriteOffFileProcessorUsecase {
					t.Errorf("NewBulkWriteOffFileProcessor() Usecase = %v, want %v",
						coreProcessor.Usecase, WriteOffFileProcessorUsecase)
				}
			}
		})
	}
}

// nolint: gocognit
func TestWriteOffFileProcessor_WithCache(t *testing.T) {
	mockRedis := redisMock.NewClient(t)

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Cache             redis.Client
	}
	type args struct {
		rc redis.Client
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		verify func(t *testing.T, result *WriteOffFileProcessor)
	}{
		{
			name: "when CoreFileProcessor is nil",
			fields: fields{
				CoreFileProcessor: nil,
				Cache:             nil,
			},
			args: args{
				rc: mockRedis,
			},
			verify: func(t *testing.T, result *WriteOffFileProcessor) {
				if result.Cache != mockRedis {
					t.Errorf("WriteOffFileProcessor.WithCache() didn't set Cache field correctly")
				}

				if result.CoreFileProcessor == nil {
					t.Errorf("WriteOffFileProcessor.WithCache() didn't initialize CoreFileProcessor")
				}

				core, ok := result.CoreFileProcessor.(*core)
				if !ok {
					t.Errorf("WriteOffFileProcessor.WithCache() didn't set CoreFileProcessor to the right type")
				} else {
					if core.Usecase != WriteOffFileProcessorUsecase {
						t.Errorf("CoreFileProcessor has incorrect usecase, got %v, want %v",
							core.Usecase, WriteOffFileProcessorUsecase)
					}
					if core.Cache != mockRedis {
						t.Errorf("CoreFileProcessor.Cache is not set correctly")
					}
				}
			},
		},
		{
			name: "when CoreFileProcessor is already initialized",
			fields: fields{
				CoreFileProcessor: &core{
					Usecase: WriteOffFileProcessorUsecase,
					Cache:   nil,
				},
				Cache: nil,
			},
			args: args{
				rc: mockRedis,
			},
			verify: func(t *testing.T, result *WriteOffFileProcessor) {
				if result.Cache != mockRedis {
					t.Errorf("WriteOffFileProcessor.WithCache() didn't set Cache field correctly")
				}

				core, ok := result.CoreFileProcessor.(*core)
				if !ok {
					t.Errorf("WriteOffFileProcessor.WithCache() changed CoreFileProcessor type")
				} else {
					if core.Usecase != WriteOffFileProcessorUsecase {
						t.Errorf("CoreFileProcessor has incorrect usecase, got %v, want %v",
							core.Usecase, WriteOffFileProcessorUsecase)
					}
					if core.Cache != mockRedis {
						t.Errorf("CoreFileProcessor.Cache is not set correctly")
					}
				}
			},
		},
		{
			name: "method chaining verification",
			fields: fields{
				CoreFileProcessor: &core{
					Usecase: WriteOffFileProcessorUsecase,
				},
				Cache: nil,
			},
			args: args{
				rc: mockRedis,
			},
			verify: func(t *testing.T, result *WriteOffFileProcessor) {},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: tt.fields.CoreFileProcessor,
				Cache:             tt.fields.Cache,
			}
			result := w.WithCache(tt.args.rc)
			if result != w {
				t.Errorf("WriteOffFileProcessor.WithCache() didn't return itself for method chaining")
			}
			tt.verify(t, result)
		})
	}
}

func TestWriteOffFileProcessor_LoadFromCSV(t *testing.T) {
	mockCore := mocks.NewCoreFileProcessor(t)
	ctx := context.Background()

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Records           []interface{}
		Cache             redis.Client
		TimeoutInSec      int
	}
	type args struct {
		ctx      context.Context
		fileName string
		payload  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []WriteOffFileRequestDTO
		wantErr bool
	}{
		{
			name: "success - valid CSV data",
			fields: fields{
				CoreFileProcessor: mockCore,
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				payload:  base64.StdEncoding.EncodeToString([]byte("CIF Number\nCIF123\nCIF456")),
			},
			want: []WriteOffFileRequestDTO{
				{
					CIFNumber: "CIF123",
				},
				{
					CIFNumber: "CIF456",
				},
			},
			wantErr: false,
		},
		{
			name: "error - empty CSV",
			fields: fields{
				CoreFileProcessor: mockCore,
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				payload:  base64.StdEncoding.EncodeToString([]byte("")),
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: tt.fields.CoreFileProcessor,
				Records:           tt.fields.Records,
				Cache:             tt.fields.Cache,
			}
			got, err := w.LoadFromCSV(tt.args.ctx, tt.args.fileName, tt.args.payload)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteOffFileProcessor.LoadFromCSV() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WriteOffFileProcessor.LoadFromCSV() = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint:gocognit
func TestWriteOffFileProcessor_GenerateValidationResultFile(t *testing.T) {
	mockCore := mocks.NewCoreFileProcessor(t)
	ctx := context.Background()

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Records           []interface{}
		Cache             redis.Client
	}
	type args struct {
		ctx      context.Context
		fileName string
		verdict  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    dto.FileResult
		wantErr bool
		setup   func(*WriteOffFileProcessor)
	}{
		{
			name: "success - with success verdict",
			fields: fields{
				CoreFileProcessor: mockCore,
				Records: []interface{}{
					WriteOffDTO{
						CIFNumber:     "CIF123",
						OverallStatus: "SUCCESS",
						FailureReason: "",
					},
					WriteOffDTO{
						CIFNumber:     "CIF456",
						OverallStatus: "FAILED",
						FailureReason: "Invalid CIF",
					},
				},
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				verdict:  "success",
			},
			want: dto.FileResult{
				FileName: "test-validation-success.csv",
				Count:    2,
				// Payload will be checked separately
			},
			wantErr: false,
		},
		{
			name: "success - with failed verdict",
			fields: fields{
				CoreFileProcessor: mockCore,
				Records: []interface{}{
					WriteOffDTO{
						CIFNumber:     "CIF123",
						OverallStatus: "FAILED",
						FailureReason: "Error message",
					},
				},
			},
			args: args{
				ctx:      ctx,
				fileName: "sample-file.csv",
				verdict:  "failed",
			},
			want: dto.FileResult{
				FileName: "sample-file-validation-failed.csv",
				Count:    1,
			},
			wantErr: false,
		},
		{
			name: "success - with empty records",
			fields: fields{
				CoreFileProcessor: mockCore,
				Records:           []interface{}{},
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				verdict:  "success",
			},
			want: dto.FileResult{
				FileName: "test-validation-success.csv",
				Count:    0,
			},
			wantErr: false,
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: tt.fields.CoreFileProcessor,
				Records:           tt.fields.Records,
				Cache:             tt.fields.Cache,
			}

			if tt.setup != nil {
				tt.setup(w)
			}

			got, err := w.GenerateValidationResultFile(tt.args.ctx, tt.args.fileName, tt.args.verdict)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteOffFileProcessor.GenerateValidationResultFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if got.FileName != tt.want.FileName {
					t.Errorf("WriteOffFileProcessor.GenerateValidationResultFile() filename = %v, want %v", got.FileName, tt.want.FileName)
				}
				if got.Count != tt.want.Count {
					t.Errorf("WriteOffFileProcessor.GenerateValidationResultFile() count = %v, want %v", got.Count, tt.want.Count)
				}

				// Verify the payload is valid base64 and contains expected data
				decoded, err := base64.StdEncoding.DecodeString(got.Payload)
				if err != nil {
					t.Errorf("WriteOffFileProcessor.GenerateValidationResultFile() returned invalid base64 payload: %v", err)
				}

				// Basic validation that CSV contains expected data
				if len(tt.fields.Records) > 0 {
					for _, record := range tt.fields.Records {
						dto := record.(WriteOffDTO)
						if !strings.Contains(string(decoded), dto.CIFNumber) {
							t.Errorf("WriteOffFileProcessor.GenerateValidationResultFile() payload does not contain expected CIF number: %v", dto.CIFNumber)
						}
					}
				}
			}
		})
	}
}

func TestWriteOffFileProcessor_renameFile(t *testing.T) {
	mockCore := mocks.NewCoreFileProcessor(t)

	type fields struct {
		CoreFileProcessor CoreFileProcessor
	}
	type args struct {
		fileName string
		suffix   string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "success - simple case",
			fields: fields{
				CoreFileProcessor: mockCore,
			},
			args: args{
				fileName: "test.csv",
				suffix:   "result",
			},
			want: "test-result.csv",
		},
		{
			name: "success - without extension",
			fields: fields{
				CoreFileProcessor: mockCore,
			},
			args: args{
				fileName: "test",
				suffix:   "validation",
			},
			want: "test-validation",
		},
		{
			name: "success - with complex filename",
			fields: fields{
				CoreFileProcessor: mockCore,
			},
			args: args{
				fileName: "bulk-writeoff-20230101.csv",
				suffix:   "validation-success",
			},
			want: "bulk-writeoff-20230101-validation-success.csv",
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: tt.fields.CoreFileProcessor,
			}
			got := w.renameFile(tt.args.fileName, tt.args.suffix)
			if got != tt.want {
				t.Errorf("WriteOffFileProcessor.renameFile() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWriteOffFileProcessor_BeginExecution(t *testing.T) {
	ctx := context.Background()

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Cache             redis.Client
	}
	type args struct {
		ctx      context.Context
		fileName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success - all operations successful",
			fields: fields{
				CoreFileProcessor: func() CoreFileProcessor {
					m := mocks.NewCoreFileProcessor(t)
					m.On("Lock", ctx).Return(nil)
					m.On("SetStagedFileName", ctx, "test.csv").Return(nil)
					m.On("MakeCacheKey", mock.Anything).Return("ck")
					m.On("GetTimeoutInSec").Return(100)
					m.On("GetRecordTTL").Return(200 * time.Second)
					return m
				}(),
				Cache: func() redis.Client {
					m := redisMock.NewClient(t)
					m.On("Set", ctx, "ck", mock.Anything, 200*time.Second).Return(true, nil)
					return m
				}(),
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: false,
		},
		{
			name: "failure - lock fails",
			fields: fields{
				CoreFileProcessor: func() CoreFileProcessor {
					m := mocks.NewCoreFileProcessor(t)
					m.On("Lock", ctx).Return(errors.New("failed to lock"))
					return m
				}(),
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: true,
		},
		{
			name: "failure - SetStagedFileName fails",
			fields: fields{
				CoreFileProcessor: func() CoreFileProcessor {
					m := mocks.NewCoreFileProcessor(t)
					m.On("Lock", ctx).Return(nil)
					m.On("SetStagedFileName", ctx, "test.csv").Return(errors.New("failed to set staged file name"))
					return m
				}(),
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: true,
		},
		{
			name: "failure - cache set fails with error",
			fields: fields{
				CoreFileProcessor: func() CoreFileProcessor {
					m := mocks.NewCoreFileProcessor(t)
					m.On("Lock", ctx).Return(nil)
					m.On("SetStagedFileName", ctx, "test.csv").Return(nil)
					m.On("MakeCacheKey", mock.Anything).Return("ck")
					m.On("GetTimeoutInSec").Return(100)
					m.On("GetRecordTTL").Return(200 * time.Second)

					return m
				}(),
				Cache: func() redis.Client {
					m := redisMock.NewClient(t)
					m.On("Set", ctx, "ck", mock.Anything, 200*time.Second).Return(false, errors.New("failed to set cache"))
					return m
				}(),
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: true,
		},
		{
			name: "failure - cache set returns false",
			fields: fields{
				CoreFileProcessor: func() CoreFileProcessor {
					m := mocks.NewCoreFileProcessor(t)
					m.On("Lock", ctx).Return(nil)
					m.On("SetStagedFileName", ctx, "test.csv").Return(nil)
					m.On("MakeCacheKey", mock.Anything).Return("ck")
					m.On("GetTimeoutInSec").Return(100)
					m.On("GetRecordTTL").Return(200 * time.Second)
					return m
				}(),
				Cache: func() redis.Client {
					m := redisMock.NewClient(t)
					m.On("Set", ctx, "ck", mock.Anything, 200*time.Second).Return(false, nil)
					return m
				}(),
			},
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: true,
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: tt.fields.CoreFileProcessor,
				Cache:             tt.fields.Cache,
			}

			err := w.BeginExecution(tt.args.ctx, tt.args.fileName)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteOffFileProcessor.BeginExecution() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestWriteOffFileProcessor_calculateTimeout(t *testing.T) {
	type fields struct {
		TimeoutInSec int
	}
	tests := []struct {
		name   string
		fields fields
		verify func(time.Time) bool
	}{
		{
			name: "default timeout - 300 seconds",
			fields: fields{
				TimeoutInSec: 300,
			},
			verify: func(timeout time.Time) bool {
				// The timeout should be approximately 300 seconds from now
				diff := time.Until(timeout)
				return diff > 299*time.Second && diff < 301*time.Second
			},
		},
		{
			name: "custom timeout - 600 seconds",
			fields: fields{
				TimeoutInSec: 600,
			},
			verify: func(timeout time.Time) bool {
				// The timeout should be approximately 600 seconds from now
				diff := time.Until(timeout)
				return diff > 599*time.Second && diff < 601*time.Second
			},
		},
		{
			name: "short timeout - 5 seconds",
			fields: fields{
				TimeoutInSec: 5,
			},
			verify: func(timeout time.Time) bool {
				// The timeout should be approximately 5 seconds from now
				diff := time.Until(timeout)
				return diff > 4*time.Second && diff < 6*time.Second
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			w := &WriteOffFileProcessor{
				CoreFileProcessor: &core{
					Usecase:      WriteOffFileProcessorUsecase,
					TimeoutInSec: tt.fields.TimeoutInSec,
				},
			}

			timeout := w.calculateTimeout()
			if !tt.verify(timeout) {
				t.Errorf("WriteOffFileProcessor.calculateTimeout() returned incorrect timeout: %v", timeout)
			}
		})
	}
}

func TestWriteOffFileProcessor_UpdateRowStatus(t *testing.T) {
	ctx := context.Background()

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Cache             redis.Client
	}
	type args struct {
		ctx context.Context
		cif string
		dto WriteOffDTO
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		setup   func(t *testing.T) (CoreFileProcessor, redis.Client)
	}{
		{
			name: "error - failed to get filename",
			args: args{
				ctx: ctx,
				cif: "CIF123",
				dto: WriteOffDTO{
					CIFNumber:     "CIF123",
					OverallStatus: "SUCCESS",
				},
			},
			wantErr: true,
			setup: func(t *testing.T) (CoreFileProcessor, redis.Client) {
				mockCore := mocks.NewCoreFileProcessor(t)
				mockCore.On("IsLocked", ctx).Return(true)
				mockCore.On("GetStagedFileName", ctx).Return("")

				mockRedis := redisMock.NewClient(t)
				return mockCore, mockRedis
			},
		},
		{
			name: "error - pipeline execution fails",
			args: args{
				ctx: ctx,
				cif: "CIF123",
				dto: WriteOffDTO{
					CIFNumber:     "CIF123",
					OverallStatus: "SUCCESS",
				},
			},
			wantErr: true,
			setup: func(t *testing.T) (CoreFileProcessor, redis.Client) {
				mockCore := mocks.NewCoreFileProcessor(t)
				mockCore.On("IsLocked", ctx).Return(true)
				mockCore.On("GetStagedFileName", ctx).Return("test.csv")
				mockCore.On("MakeCacheKey", mock.Anything, mock.Anything).Return("")

				mockRedis := redisMock.NewClient(t)

				// simulate newly created
				mockRedis.On("SAdd", ctx, mock.Anything, mock.Anything).
					Return(true, nil)
				// Mock pipeline execution with error
				mockRedis.On("Pipelined", ctx, mock.Anything).
					Return(errors.New("pipeline error"))

				return mockCore, mockRedis
			},
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			mockCore, mockRedis := tt.setup(t)

			w := &WriteOffFileProcessor{
				CoreFileProcessor: mockCore,
				Cache:             mockRedis,
			}

			err := w.UpdateRowStatus(tt.args.ctx, tt.args.cif, tt.args.dto, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteOffFileProcessor.UpdateRowStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestWriteOffFileProcessor_Stage(t *testing.T) {
	ctx := context.Background()

	type fields struct {
		CoreFileProcessor CoreFileProcessor
		Cache             redis.Client
	}
	type args struct {
		ctx      context.Context
		fileName string
		m        map[string]interface{}
		l        []string
		rowCount int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		setup   func(t *testing.T) (CoreFileProcessor, redis.Client)
	}{
		{
			name: "success - stage new records",
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				m: map[string]interface{}{
					"CIF123": `{"CIFNumber":"CIF123"}`,
					"CIF456": `{"CIFNumber":"CIF456"}`,
				},
				l:        []string{"CIF123", "CIF456"},
				rowCount: 2,
			},
			wantErr: false,
			setup: func(t *testing.T) (CoreFileProcessor, redis.Client) {
				mockCore := mocks.NewCoreFileProcessor(t)
				mockCore.On("MakeCacheKey", "test.csv", suffixStaged).Return("test.csv:staged")
				mockCore.On("MakeCacheKey", "test.csv", suffixRecord).Return("test.csv:record")

				mockRedis := redisMock.NewClient(t)
				mockRedis.On("GetBool", ctx, "test.csv:staged").Return(false, nil)
				mockRedis.On("HSet", ctx, "test.csv:record", mock.Anything).Return(true, nil)

				// Mock successful pipeline execution
				mockRedis.On("Pipelined", ctx, mock.Anything).Return(nil)

				return mockCore, mockRedis
			},
		},
		{
			name: "success - overwrite existing records",
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				m: map[string]interface{}{
					"CIF123": `{"CIFNumber":"CIF123"}`,
				},
				l:        []string{"CIF123"},
				rowCount: 1,
			},
			wantErr: false,
			setup: func(t *testing.T) (CoreFileProcessor, redis.Client) {
				mockCore := mocks.NewCoreFileProcessor(t)
				mockCore.On("MakeCacheKey", "test.csv", suffixStaged).Return("test.csv:staged")
				mockCore.On("MakeCacheKey", "test.csv", suffixRecord).Return("test.csv:record")

				mockRedis := redisMock.NewClient(t)
				mockRedis.On("GetBool", ctx, "test.csv:staged").Return(true, nil)
				mockRedis.On("Delete", ctx, "test.csv:record").Return(true, nil)
				mockRedis.On("HSet", ctx, "test.csv:record", mock.Anything).Return(true, nil)

				// Mock successful pipeline execution
				mockRedis.On("Pipelined", ctx, mock.Anything).Return(nil)

				return mockCore, mockRedis
			},
		},
		{
			name: "error - pipeline execution fails",
			args: args{
				ctx:      ctx,
				fileName: "test.csv",
				m: map[string]interface{}{
					"CIF123": `{"CIFNumber":"CIF123"}`,
				},
				l:        []string{"CIF123"},
				rowCount: 1,
			},
			wantErr: true,
			setup: func(t *testing.T) (CoreFileProcessor, redis.Client) {
				mockCore := mocks.NewCoreFileProcessor(t)
				mockCore.On("MakeCacheKey", "test.csv", suffixStaged).Return("test.csv:staged")
				mockCore.On("MakeCacheKey", "test.csv", suffixRecord).Return("test.csv:record")

				mockRedis := redisMock.NewClient(t)
				mockRedis.On("GetBool", ctx, "test.csv:staged").Return(false, nil)
				mockRedis.On("HSet", ctx, "test.csv:record", mock.Anything).Return(true, nil)

				// Mock pipeline execution with error
				mockRedis.On("Pipelined", ctx, mock.Anything).Return(errors.New("pipeline error"))

				return mockCore, mockRedis
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			mockCore, mockRedis := tt.setup(t)

			w := &WriteOffFileProcessor{
				CoreFileProcessor: mockCore,
				Cache:             mockRedis,
			}

			err := w.Stage(tt.args.ctx, tt.args.fileName, tt.args.m, tt.args.l, tt.args.rowCount)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteOffFileProcessor.Stage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
