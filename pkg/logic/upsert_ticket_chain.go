package logic

import (
	"context"
	"database/sql"
	"time"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

func sumPermissionBitwiseValue(permissionIDs []int64, permsMap map[int64]*permissionManagementStorage.PermissionDTO) int64 {
	var sum int64
	for _, id := range permissionIDs {
		if permsMap[id] == nil {
			continue
		}
		sum += permsMap[id].BitwiseValue
	}
	return sum
}

// nolint:funlen,gocognit
func upsertTicketChain(ctx context.Context, db *sql.DB, tc []api.TicketChain, moduleID int64, elementID int64, userID int64) error {
	chains, err := storage.GetTicketChainByElementID(ctx, db, elementID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket chain")
	}
	chainMap := make(map[int64]*storage.TicketChainDTO)
	for _, chain := range chains {
		chainMap[chain.ID] = chain
	}

	perms, err := permissionManagementStorage.GetPermissionByModuleID(ctx, db, moduleID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get permission")
	}
	permsMap := make(map[int64]*permissionManagementStorage.PermissionDTO)
	for _, perm := range perms {
		permsMap[perm.ID] = perm
	}

	var update, create, remove []storage.TicketChainDTO
	for _, chain := range tc {
		existingChain := chainMap[chain.Id]
		if existingChain != nil {
			update = append(update, storage.TicketChainDTO{
				ID:              chain.Id,
				UpdatedAt:       sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedBy:       sql.NullInt64{Int64: userID, Valid: true},
				ElementID:       elementID,
				CurrentStatusID: chain.PrevStatusID,
				NextStatusID:    chain.NextStatusID,
				ActionName:      chain.ActionName,
				BitwiseRequired: sumPermissionBitwiseValue(chain.PermissionsIDs, permsMap),
				Conditions:      sql.NullString{String: "", Valid: false},
			})
		} else {
			create = append(create, storage.TicketChainDTO{
				CreatedAt:       sql.NullTime{Time: time.Now(), Valid: true},
				CreatedBy:       sql.NullInt64{Int64: userID, Valid: true},
				ElementID:       elementID,
				CurrentStatusID: chain.PrevStatusID,
				NextStatusID:    chain.NextStatusID,
				ActionName:      chain.ActionName,
				BitwiseRequired: sumPermissionBitwiseValue(chain.PermissionsIDs, permsMap),
				Conditions:      sql.NullString{String: "", Valid: false},
			})
		}
		delete(chainMap, chain.Id)
	}
	for _, chain := range chainMap {
		remove = append(remove, *chain)
	}

	if len(create) > 0 {
		for _, dto := range create {
			_, err := storage.CreateTicketChain(ctx, db, &dto)
			if err != nil {
				return err
			}
		}
	}

	if len(update) > 0 {
		for _, dto := range update {
			err := storage.UpdateTicketChain(ctx, db, &dto)
			if err != nil {
				return errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket chain")
			}
		}
	}

	if len(remove) > 0 {
		for _, dto := range remove {
			err := storage.DeleteTicketChain(ctx, db, dto.ID)
			if err != nil {
				return errorwrapper.WrapError(err, apiError.Idem, "failed to remove ticket chain")
			}
		}
	}
	return nil
}
