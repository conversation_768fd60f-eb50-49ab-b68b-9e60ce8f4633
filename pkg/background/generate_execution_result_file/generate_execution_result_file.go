// Package generateexecutionresultfile provides the background process to generate execution result file.
package generateexecutionresultfile

import (
	"context"
	"fmt"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// New is the constructor of handleGenerateExecutionResultFile
func New(appConfig *config.AppConfig, appianClient appian.Appian, cache redis.Client) background.Handler {
	return &handleGenerateExecutionResultFile{
		AppConfig:    appConfig,
		AppianClient: appianClient,
		Cache:        cache,
	}
}

type handleGenerateExecutionResultFile struct {
	AppConfig    *config.AppConfig
	AppianClient appian.Appian
	Cache        redis.Client
}

// Handle is the main function to handle the background process
//
// nolint: funlen
func (h *handleGenerateExecutionResultFile) Handle() {
	tag := "handleGenerateExecutionResultFile.Handle"
	ctx, cancel := background.Context()
	defer cancel()
	defer func() {
		if r := recover(); r != nil {
			slog.FromContext(ctx).Error(tag, "panic on execution: "+fmt.Sprintf("%v", r))
		}
	}()
	slog.FromContext(ctx).Info(tag, "generate execution result file process started")

	result, done := h.getFileResponse(ctx)
	if !done {
		return
	}

	// get Appian access token
	tokenRes, err := h.AppianClient.GetAccessToken(ctx, &dto.GetAccessTokenRequest{
		ClientID:     h.AppConfig.Appian.RegisteredClientID,
		ClientSecret: h.AppConfig.Appian.RegisteredClientSecret,
		GrantType:    h.AppConfig.Appian.GrantType,
	})
	if err != nil {
		slog.FromContext(ctx).Error(fileprocessor.TagBulkFileProcessor, "failed to get access token:"+err.Error())
		return
	}

	accessToken := tokenRes.AccessToken
	authHeader := "Bearer " + accessToken
	ctx = commonCtx.WithHTTPHeader(ctx, "Authorization", authHeader)

	_, err = h.AppianClient.PostBulkFileExecutionStatus(ctx, &dto.BulkFileStatusRequest{
		SourceFileName: result.SourceFileName,
		Result: map[string]dto.FileResult{
			"execution": result.Result["execution"],
		},
		Type:    result.Type,
		Status:  result.Status,
		Message: result.Message,
	})
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to send bulk file status to appian:"+err.Error())
		return
	}
	slog.FromContext(ctx).Info(tag, "bulk file status sent to appian")

	// delete cache
	h.cleanup(ctx)
}

func (h *handleGenerateExecutionResultFile) getFileResponse(ctx context.Context) (dto.FileResponse, bool) {
	// TODO: switch case for different file processor usecases
	interactor := fileprocessor.NewBulkWriteOffFileProcessor().WithCache(h.Cache)
	if !interactor.IsLocked(ctx) {
		slog.FromContext(ctx).Info("tagBulkWriteOff", "file processor is not locked. Skipping...")
		return dto.FileResponse{}, false
	}
	if !interactor.IsExecutionDone(ctx) {
		slog.FromContext(ctx).Info("tagBulkWriteOff", "execution is not done")
		return dto.FileResponse{}, false
	}

	return interactor.CompileExecutionResult(ctx), true
}
func (h *handleGenerateExecutionResultFile) cleanup(ctx context.Context) {
	// TODO: switch case for different file processor usecases
	interactor := fileprocessor.NewBulkWriteOffFileProcessor().WithCache(h.Cache)
	interactor.PostExecutionCleanup(ctx)
}
