package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Update Permission", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := "PASSED"
		if desc.Failed {
			result = "FAILED"
		}
		fmt.Printf("Update Permission: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 when success", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		updatedData := resources.SampleDataForUpdatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, updatedData.ID, updatedData.Name, updatedData.Description, updatedData.Status, updatedData.ModuleID, updatedData.BitwiseValue))

		data := resources.SampleDataForCreatePermission()

		// mock db query
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}))

		// mock check duplicate count permission
		duplicateQuery := TestQueryGetCountPermissionForDuplicateChecking + ` AND id != ?`
		mocker.ExpectQuery(regexp.QuoteMeta(duplicateQuery)).
			WithArgs(updatedData.Name, updatedData.BitwiseValue, updatedData.ModuleID, updatedData.ID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(0))

		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdatePermission)).
			WithArgs(
				updatedData.Name,
				updatedData.Description,
				updatedData.BitwiseValue,
				updatedData.Status,
				updatedData.ModuleID,
				sqlmock.AnyArg(),
				updatedData.UpdatedBy.Int64,
				updatedData.ID,
			).
			WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Update Permission", updatedData.ID, updatedData.CreatedBy.Int64, updatedData.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"id": 4,
			"status": "Success"
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(200))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 200 when success and deactivate", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		updatedData := resources.SampleDataForUpdatePermission()
		updatedData.Status = 0
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, updatedData.ID, updatedData.Name, updatedData.Description, updatedData.Status, updatedData.ModuleID, updatedData.BitwiseValue))

		data := resources.SampleDataForCreatePermission()

		// mock db query
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}))

		// mock check duplicate count permission
		duplicateQuery := TestQueryGetCountPermissionForDuplicateChecking + ` AND id != ?`
		mocker.ExpectQuery(regexp.QuoteMeta(duplicateQuery)).
			WithArgs(updatedData.Name, updatedData.BitwiseValue, updatedData.ModuleID, updatedData.ID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(0))

		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdatePermission)).
			WithArgs(
				updatedData.Name,
				updatedData.Description,
				updatedData.BitwiseValue,
				updatedData.Status,
				updatedData.ModuleID,
				sqlmock.AnyArg(),
				updatedData.UpdatedBy.Int64,
				updatedData.ID,
			).
			WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Update Permission", updatedData.ID, updatedData.CreatedBy.Int64, updatedData.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"id": 4,
			"status": "Success"
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(200))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 401 when unauthorized", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		body := hcl.JSON(`{}`)

		// return empty from redis
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
		// no user id on db
		mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
	})

	It("should return 403: failed to create permission due to invalid access", func() {
		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		body := hcl.JSON(`{
			"id": 4,
			"name": "Read - Updated",
			"description": "Read for Admin Configuration - Updated",
			"status": 0,
			"moduleID": 2,
			"bitwise": 2
		}`)

		permDetail := resources.SampleDataUserPermissionsDetail()
		delete(permDetail.Permissions, string(constants.ModuleConfig))
		binaryData, _ := json.Marshal(permDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		expectedResponse := `{
			"code": "forbidden",
			"message": "User is not authorized to perform this element action"
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 400: failed to update permission due to duplicate name or bitwise value in the same module", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		updatedData := resources.SampleDataForUpdatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, updatedData.ID, updatedData.Name, updatedData.Description, updatedData.Status, updatedData.ModuleID, updatedData.BitwiseValue))

		data := resources.SampleDataForCreatePermission()

		// mock db query
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}))

		// mock check duplicate count permission
		duplicateQuery := TestQueryGetCountPermissionForDuplicateChecking + ` AND id != ?`
		mocker.ExpectQuery(regexp.QuoteMeta(duplicateQuery)).
			WithArgs(updatedData.Name, updatedData.BitwiseValue, updatedData.ModuleID, updatedData.ID).
			WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"code":"badRequest",
			"message":"failed to update permission due to duplicate name or bitwise value in the same module"
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 400: failed to update permission due to still used in roles: IAM.", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		updatedData := resources.SampleDataForUpdatePermission()
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, updatedData.ID, updatedData.Name, updatedData.Description, updatedData.Status, updatedData.ModuleID, updatedData.BitwiseValue))

		data := resources.SampleDataForCreatePermission()

		// mock db query
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}).AddRow("IAM"))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"code":"badRequest",
			"message":"failed to update permission due to still used in roles: IAM."
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 400: failed to validate request Key: 'UpdatePermissionRequest.Bitwise' Error:Field validation for 'Bitwise' failed on the 'permission_bitwise' tag", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		updatedData := resources.SampleDataForUpdatePermission()
		updatedData.BitwiseValue = 5
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"description": "%s",
			"status": %d,
			"moduleID": %d,
			"bitwise": %d
		}`, updatedData.ID, updatedData.Name, updatedData.Description, updatedData.Status, updatedData.ModuleID, updatedData.BitwiseValue))

		data := resources.SampleDataForCreatePermission()

		// mock db query
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"code": "badRequest",
			"message": "failed to validate request",
			"errors": [
				{
					"errorCode": "badRequest",
					"message": "failed to validate request Key: 'UpdatePermissionRequest.Bitwise' Error:Field validation for 'Bitwise' failed on the 'permission_bitwise' tag"
				}
			]
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})
})
