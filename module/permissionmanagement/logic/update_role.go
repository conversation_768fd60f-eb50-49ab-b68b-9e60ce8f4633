package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// UpdateRole ...
//
// nolint: funlen
func (p *PermissionManagementImpl) UpdateRole(ctx context.Context, req *api.UpdateRoleRequest) (*api.UpdateRoleResponse, error) {
	// check permission
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.RoleManagement, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// get role
	role, err := storage.GetRoleByID(ctx, db, req.Id)
	if err != nil {
		return nil, err
	}

	role = updateStructRoleDTO(role, req, user.ID)
	err = storage.UpdateRole(ctx, db, role)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to update role")
	}

	// update matrix
	err = updatePermissionMatrix(ctx, db, req, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to update role permission")
	}

	// expiring the user if status change
	if req.Status == 0 {
		errExp := expiringExistingUserByRole(ctx, db, role.ID)
		if errExp != nil {
			return nil, errExp
		}
		errRemove := storage.RemoveUserRoleByRoleID(ctx, db, role.ID)
		if errRemove != nil {
			return nil, errRemove
		}
	}

	// write audit trail
	title := "Update Role"
	description := fmt.Sprintf("%v for ID %v : %v executed successfully", title, role.ID, role.Name)
	auditTrailReq, auditErr := helper.WriteLogAuditTrailRole(ctx, role, title, description, user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.UpdateRoleResponse{
		Id:        role.ID,
		Name:      role.Name,
		Status:    role.Status,
		UpdatedAt: role.UpdatedAt.Time.Local().String(),
		UpdatedBy: user.Name,
	}, nil
}

func updateStructRoleDTO(dto *storage.RoleDTO, req *api.UpdateRoleRequest, userID int64) *storage.RoleDTO {
	dto.Name = req.Name
	dto.Status = req.Status
	dto.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	dto.UpdatedBy = sql.NullInt64{Int64: userID, Valid: true}

	return dto
}

func getExistingRolePermission(ctx context.Context, db *sql.DB, roleID int64) (map[int64]*storage.RoleElementPermissionDTO, error) {
	mapRole := make(map[int64]*storage.RoleElementPermissionDTO)

	permissions, err := storage.GetPermissionByRoleID(ctx, db, roleID)
	if err != nil {
		return nil, err
	}

	if len(permissions) > 0 {
		for _, perm := range permissions {
			mapRole[perm.ElementID] = perm
		}
	}
	return mapRole, nil
}

func getElementPermissionBitwise(ctx context.Context, db *sql.DB, permissionIDs []int64) (int64, error) {
	if len(permissionIDs) == 0 {
		return 0, nil
	}

	curPermIds := utils.ConvertArrayIntToAny(permissionIDs)
	curFilter := []commonStorage.QueryCondition{
		commonStorage.In("id", curPermIds...)}

	bitwiseValue, err := storage.GetTotalBitwisePermission(ctx, db, curFilter)

	if err != nil {
		return 0, err
	}

	return bitwiseValue, nil
}

func constructUpdatedPermissionMatrix(ctx context.Context, db *sql.DB, req *api.UpdateRoleRequest, userID int64) ([]*storage.RoleElementPermissionDTO, []*storage.RoleElementPermissionDTO, map[int64]*storage.RoleElementPermissionDTO, error) {
	// get current permission by role
	curPerm, err := getExistingRolePermission(ctx, db, req.Id)

	if err != nil {
		return nil, nil, nil, err
	}

	var updateDTO []*storage.RoleElementPermissionDTO
	var insertDTO []*storage.RoleElementPermissionDTO

	if len(req.ElementPermissions) > 0 {
		for _, perm := range req.ElementPermissions {
			bitwiseValue, errRow := getElementPermissionBitwise(ctx, db, perm.PermissionsIDs)
			if errRow != nil {
				return nil, nil, nil, errRow
			}
			// if permission exists
			if value, ok := curPerm[perm.ElementID]; ok {
				if bitwiseValue == value.BitwiseValue {
					delete(curPerm, perm.ElementID)
					continue
				}
				curRow := structUpdatedPermissionMatrixDTO(value, bitwiseValue, userID)
				updateDTO = append(updateDTO, curRow)
				delete(curPerm, perm.ElementID)
			} else {
				dto := &storage.RoleElementPermissionDTO{
					RoleID:       req.Id,
					ElementID:    perm.ElementID,
					BitwiseValue: bitwiseValue,
					CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
					CreatedBy:    sql.NullInt64{Int64: userID, Valid: true},
				}
				insertDTO = append(insertDTO, dto)
			}
		}
	}

	return updateDTO, insertDTO, curPerm, nil
}

func updatePermissionMatrix(ctx context.Context, db *sql.DB, req *api.UpdateRoleRequest, userID int64) error {
	//construct for update, insert, and remove
	updateDTO, insertDTO, removePerm, err := constructUpdatedPermissionMatrix(ctx, db, req, userID)
	if err != nil {
		return err
	}

	if len(updateDTO) > 0 {
		err = updateBatchPermissionMatrixToDB(ctx, db, updateDTO)
		if err != nil {
			return err
		}
	}

	if len(insertDTO) > 0 {
		err = insertBatchPermissionMatrixToDB(ctx, db, insertDTO)
		if err != nil {
			return err
		}
	}

	if len(removePerm) > 0 {
		err = removePermissionMatrixDB(ctx, db, removePerm)
		if err != nil {
			return err
		}
	}
	return nil
}

func removePermissionMatrixDB(ctx context.Context, db *sql.DB, matrix map[int64]*storage.RoleElementPermissionDTO) error {
	removeIds := make([]any, 0)
	for cur := range matrix {
		removeIds = append(removeIds, matrix[cur].ID)
	}

	curFilter := []commonStorage.QueryCondition{
		commonStorage.In("id", removeIds...)}

	err := storage.RemovePermissions(ctx, db, curFilter)
	if err != nil {
		return err
	}

	return nil
}

func insertBatchPermissionMatrixToDB(ctx context.Context, db *sql.DB, dto []*storage.RoleElementPermissionDTO) error {
	for _, row := range dto {
		_, err := storage.CreatePermissionMatrix(ctx, db, row)
		if err != nil {
			return err
		}
	}
	return nil
}

func updateBatchPermissionMatrixToDB(ctx context.Context, db *sql.DB, dto []*storage.RoleElementPermissionDTO) error {
	for _, row := range dto {
		err := storage.UpdatePermissionMatrix(ctx, db, row)

		if err != nil {
			return err
		}
	}
	return nil
}

func structUpdatedPermissionMatrixDTO(dto *storage.RoleElementPermissionDTO, bitwise int64, userID int64) *storage.RoleElementPermissionDTO {
	dto.BitwiseValue = bitwise
	dto.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	dto.UpdatedBy = sql.NullInt64{Int64: userID, Valid: true}

	return dto
}
