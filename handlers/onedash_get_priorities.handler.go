package handlers

import (
	"context"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetPriorities is API to get priorities
func (o *OnedashService) GetPriorities(ctx context.Context, req *api.GetPrioritiesRequest) (*api.GetPrioritiesResponse, error) {
	result, err := logic.Process.GetPriorities(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get priorities")
	}

	return result, nil
}
