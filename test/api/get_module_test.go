package api

import (
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonConstants "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/constants"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Module", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("GetModule", func() {
		When("request list module with limit", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
				"limit": 10,
				"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
				}
				queryList, args := commonStorage.BuildQuery(TestQueriesGetModuleList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				mocker.ExpectQuery(regexp.QuoteMeta(queryList)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetModuleList(mocker, resources.SampleDataGetModuleList()))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModulesCount)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
            "modules": [
              {
                "id": 1,
                "name": "name-1",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1
              },
              {
                "id": 2,
                "name": "name-2",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-2",
                "updatedBy": "updated_by-2",
                "status": 1
              },
              {
                "id": 3,
                "name": "name-3",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-3",
                "updatedBy": "updated_by-3",
                "status": 1
              }
            ],
            "count": 3
          }`

				res, err := client.Get(CreateModule, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list module without limit", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(``)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{}
				queryList, args := commonStorage.BuildQuery(TestQueriesGetModuleList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				mocker.ExpectQuery(regexp.QuoteMeta(queryList)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetModuleList(mocker, resources.SampleDataGetModuleList()))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModulesCount)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
            "modules": [
              {
                "id": 1,
                "name": "name-1",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1
              },
              {
                "id": 2,
                "name": "name-2",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-2",
                "updatedBy": "updated_by-2",
                "status": 1
              },
              {
                "id": 3,
                "name": "name-3",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-3",
                "updatedBy": "updated_by-3",
                "status": 1
              }
            ],
            "count": 3
          }`

				res, err := client.Get(CreateModule, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element filter by search_key", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
					"searchKey": "name-1",
					"limit": 10,
					"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.Like("m.name", "%name-1%", commonStorage.QueryConditionTypeWHERE),
				}
				queryList, args := commonStorage.BuildQuery(TestQueriesGetModuleList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				moduleSamples := resources.SampleDataGetModuleList()
				modules := []*storage.ModuleListDTO{moduleSamples[0]}
				mocker.ExpectQuery(regexp.QuoteMeta(queryList)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetModuleList(mocker, modules))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModulesCount)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))

				expectedResponse := `{
            "modules": [
              {
                "id": 1,
                "name": "name-1",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1
              }
            ],
            "count": 1
          }`

				res, err := client.Get(CreateModule, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element filter by name", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
					"name": "name-1",
					"limit": 10,
					"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.EqualTo("m.name", "name-1"),
				}
				queryList, args := commonStorage.BuildQuery(TestQueriesGetModuleList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				moduleSamples := resources.SampleDataGetModuleList()
				modules := []*storage.ModuleListDTO{moduleSamples[0]}
				mocker.ExpectQuery(regexp.QuoteMeta(queryList)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetModuleList(mocker, modules))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModulesCount)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))

				expectedResponse := `{
            "modules": [
              {
                "id": 1,
                "name": "name-1",
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1
              }
            ],
            "count": 1
          }`

				res, err := client.Get(CreateModule, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

	})
})
