package customerexperiencehttp

import (
	"context"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/initialize"
)

const (
	logTag  = "customerExperienceHttpClient"
	service = "customer-experience"
)

// CustomerExperienceHTTP defines methods to interact with the Customer Experience service.
type CustomerExperienceHTTP interface {
	GetVideoCallOps(ctx context.Context, applicationID string, userID string) (*GetVideoCallOpsResponse, error)
}

// CustomerExperienceClient implements CustomerExperienceHTTP using HTTP requests.
type CustomerExperienceClient struct {
	machinery klient.RoundTripper
}

// NewCustomerExperienceHTTPClient initializes a new CustomerExperienceClient.
func NewCustomerExperienceHTTPClient(baseURL string, options ...klient.Option) (*CustomerExperienceClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &CustomerExperienceClient{
		machinery: roundTripper,
	}, nil
}
