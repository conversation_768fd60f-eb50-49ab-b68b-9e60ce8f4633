package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	customerJournalAPI "gitlab.myteksi.net/bersama/customer-journal/api"
	PayAuthZApi "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"

	"strconv"

	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	productMasterAPI "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	txHistoryAPI "gitlab.myteksi.net/bersama/transaction-history/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	txnLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p process) mappingFundingRelatedData(ctx context.Context, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	var err error

	switch req.Key {
	case constants.KeyListOfFundingAccount:
		result, err = p.GetAccountListCustomerSearch(ctx, req)
	case constants.KeyMainCasaAndSakuAccount, constants.KeyPiggybank, constants.KeyTermDeposit:
		result, err = p.GetAccountDetailCustomerSearch(ctx, req)
	case constants.KeyTermDepositRenewalHistory:
		result, err = p.GetTermDepositRenewalHistory(ctx, req)
	case constants.KeyTermDepositParameterHistory:
		result, err = p.GetDepositParameterHistory(ctx, req)
	// transaction configuration data
	case constants.KeyCustomerLevelTxLimit, constants.KeyBankWideLevelTxLimit:
		result, err = p.GetTransactionConfigurationData(ctx, req, user)
	case constants.KeyExternalLinkages, constants.KeyPartnerLinkage:
		result, err = p.GetExternalLinkageData(ctx, req)
	case constants.KeyLinkingHistory:
		result, err = p.GetLinkingHistory(ctx, req)
	case constants.KeyReauthHistory:
		result, err = p.GetReauthenticationHistory(ctx, req)
	}
	if err != nil {
		return nil, err
	}

	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		result[constants.DataTypeSafeID] = req.Identifier
	} else if req.IdentifierType == api.IdentifierType_CIF {
		result[constants.DataTypeCif] = req.Identifier
	}

	return result, nil
}

// GetAccountListCustomerSearch ...
func (p process) GetAccountListCustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var cifNumber string

	if req.IdentifierType == api.IdentifierType_CIF {
		cifNumber = req.Identifier
	} else if req.Payload != nil && req.Payload[constants.DataTypeCif] != "" {
		cifNumber = req.Payload[constants.DataTypeCif]
	} else {
		resp, err := p.GetCustomerSafeID(ctx, req.Identifier, string(req.IdentifierType), req.Key)
		if err != nil {
			return nil, err
		}
		cifNumber = resp[constants.DataTypeCif].(string)
	}

	if cifNumber == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "user has no valid cif number")
	}

	request := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber: cifNumber,
	}

	resp, err := helper.GetAccountList(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := mappingAccountListField(resp, req.Key)
	// include cif in response
	result[constants.DataTypeCif] = cifNumber
	return result, nil
}

// mappingAccountListField ...
func mappingAccountListField(data *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse, key string) map[string]interface{} {
	result := make(map[string]interface{})

	if data == nil || len(data.Accounts) == 0 {
		return result
	}

	switch key {
	case constants.KeyListOfFundingAccount:
		setListOfFundingAccounts(result, data)
	case constants.KeyLendingPAS:
		setListOfLendingAccounts(result, data)
	case constants.KeyTransactionList:
		setListOfTxAccountFilter(result, data)
	}
	return result
}

// setListOfLendingAccounts ...
func setListOfLendingAccounts(data map[string]interface{}, accData *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse) {
	accountMap := filterByVariantForLending(accData.Accounts)
	lendingInfo := map[string]interface{}{
		constants.KeyLocInformation: getAccountBySubTab(accountMap, constants.KeyLendingPAS),
	}

	for k, v := range lendingInfo {
		data[k] = v
	}
}

// setListOfTxAccountFilter ...
func setListOfTxAccountFilter(data map[string]any, accData *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse) {
	accountMap := filterByVariantForFunding(accData.Accounts)
	fundingInfo := map[string]any{
		constants.KeyMainCasa:    getAccountBySubTab(accountMap, constants.KeyMainCasa),
		constants.KeySaku:        getAccountBySubTab(accountMap, constants.KeySaku),
		constants.KeyOVONabung:   getAccountBySubTab(accountMap, constants.KeyOVONabung),
		constants.KeyPiggybank:   getAccountBySubTab(accountMap, constants.KeyPiggybank),
		constants.KeyTermDeposit: getAccountBySubTab(accountMap, constants.KeyTermDeposit),
	}

	for k, v := range fundingInfo {
		data[k] = v
	}
}

func filterByVariantForLending(accounts []accountServiceAPI.CASAAccountDetail) map[string][]interface{} {
	filteredAccounts := make(map[string][]interface{})
	for _, acc := range accounts {
		curAcc := map[string]interface{}{
			"accountID":        acc.Id,
			"productVariantID": acc.ProductVariantID,
			"parentAccountID":  acc.ParentAccountID,
			"createdAt":        acc.OpeningTimestamp,
			"status":           acc.Status,
		}
		if listAcc, ok := filteredAccounts[acc.ProductVariantID]; ok {
			filteredAccounts[acc.ProductVariantID] = append(listAcc, curAcc)
		} else {
			filteredAccounts[acc.ProductVariantID] = []interface{}{curAcc}
		}
	}
	return filteredAccounts
}

// setListOfFundingAccounts ...
func setListOfFundingAccounts(data map[string]interface{}, accData *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse) {
	accountMap := filterByVariantForFunding(accData.Accounts)
	fundingInfo := map[string]interface{}{
		constants.KeyMainCasaAndSakuAccount: getAccountBySubTab(accountMap, constants.KeyMainCasaAndSakuAccount),
		constants.KeyPiggybank:              getAccountBySubTab(accountMap, constants.KeyPiggybank),
		constants.KeyTermDeposit:            getAccountBySubTab(accountMap, constants.KeyTermDeposit),
	}

	for k, v := range fundingInfo {
		data[k] = v
	}
}

func getAccountBySubTab(data map[string][]interface{}, subTab string) []interface{} {
	accounts := make([]interface{}, 0)
	productVariantIDs := constants.ProductVariantMappingBySubTab[subTab]
	if len(productVariantIDs) == 0 {
		return accounts
	}

	for _, varID := range productVariantIDs {
		if listAcc, ok := data[varID]; ok {
			accounts = append(accounts, listAcc...)
		}
	}
	return accounts
}

func filterByVariantForFunding(accounts []accountServiceAPI.CASAAccountDetail) map[string][]interface{} {
	filteredAccounts := make(map[string][]interface{})
	for _, acc := range accounts {
		curAcc := map[string]interface{}{
			"accountID":        acc.Id,
			"productVariantID": acc.ProductVariantID,
			"status":           acc.Status,
			"accountName":      acc.AccountName,
		}
		if listAcc, ok := filteredAccounts[acc.ProductVariantID]; ok {
			filteredAccounts[acc.ProductVariantID] = append(listAcc, curAcc)
		} else {
			filteredAccounts[acc.ProductVariantID] = []interface{}{curAcc}
		}
	}
	return filteredAccounts
}

// GetAccountDetailCustomerSearch ...
func (p process) GetAccountDetailCustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["accountID"]
	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "invalid account id")
	}

	request := &accountServiceAPI.GetAccountRequest{
		AccountID:         accountID,
		FetchBalance:      true,
		FetchInterestRate: true,
	}
	resp, err := helper.GetAccountDetail(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	return p.mappingGetAccountDetail(ctx, resp, req.Key, req)
}

func (p *process) mappingGetAccountDetail(ctx context.Context, data *accountServiceAPI.GetAccountResponse, key string, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	if data == nil {
		return result, nil
	}

	switch key {
	case constants.KeyMainCasaAndSakuAccount:
		err := p.setMainCasaAndPocketAccount(ctx, result, data)
		if err != nil {
			return nil, err
		}
	case constants.KeyPiggybank:
		err := p.setPiggybankAccount(ctx, result, data, req)
		if err != nil {
			return nil, err
		}
	case constants.KeyTermDeposit:
		setTermDepositAccount(result, data)
	}
	return result, nil
}

func (p *process) setMainCasaAndPocketAccount(ctx context.Context, result map[string]interface{}, data *accountServiceAPI.GetAccountResponse) error {
	accountInfo := data.Account
	accountName := accountInfo.Name

	var blockHoldcodes []string
	err := json.Unmarshal([]byte(accountInfo.ProductSpecificParameters["applicableHoldcodes"]), &blockHoldcodes)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to unmarshal account holdcodes", utils.GetTraceID(ctx))
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to unmarshal data")
	}

	// get account name when CASA
	if accountInfo.ProductVariantID == constants.ProductVariantMainCasa {
		custRes, custErr := p.getCustomerData(ctx, accountInfo.Id, string(api.IdentifierType_ACCOUNT_NUMBER), []string{constants.PersonalInfoType})
		if custErr != nil {
			return custErr
		}
		accountName = custRes["name"].(string)
	}

	info := map[string]interface{}{
		constants.KeyMainCasaAccountID:              accountInfo.Id,
		constants.KeyMainCasaAccountType:            constants.MappingWordingAccountType[accountInfo.ProductVariantID],
		constants.KeyMainCasaAccountStatus:          accountInfo.Status,
		constants.KeyMainCasaAccountName:            accountName,
		constants.KeyMainCasaBlockHoldcodes:         blockHoldcodes,
		constants.KeyMainCasaMakerName:              "",
		constants.KeyMainCasaMakerJustification:     "",
		constants.KeyMainCasaLastUpdated:            "", //the data not ready
		constants.KeyMainCasaCurrency:               accountInfo.AvailableBalance.CurrencyCode,
		constants.KeyMainCasaAvailableBalance:       utils.HumanizeBalance(accountInfo.AvailableBalance.Val, true),
		constants.KeyMainCasaBaseInterestRate:       utils.FormatWithPercentage(utils.HumanizeBalance(accountInfo.ApplicableInterestRate.BaseInterestRate, false)),
		constants.KeyMainCasaTotalInterestRate:      utils.FormatWithPercentage(utils.HumanizeBalance(accountInfo.ApplicableInterestRate.TotalInterestRate, false)),
		constants.KeyMainCasaRateSpreadInterestRate: utils.FormatWithPercentage(utils.HumanizeBalance(accountInfo.ApplicableInterestRate.RateSpreadInterestRate, false)),
	}

	for k, v := range info {
		result[k] = v
	}
	return nil
}

func (p *process) setPiggybankAccount(ctx context.Context, result map[string]interface{}, data *accountServiceAPI.GetAccountResponse, req *api.CustomerSearchRequest) error {
	accountInfo := data.Account

	var (
		goalAmount string
		gaErr      error
		lastDeduct string
		ldErr      error
		wg         sync.WaitGroup
	)
	wg.Add(2)

	go func() {
		defer wg.Done()
		goalAmount, gaErr = p.getCustomerSearchPiggyGoalAmount(ctx)
	}()

	go func() {
		defer wg.Done()
		lastDeduct, ldErr = p.getCustomerSearchPiggyLastDeduct(ctx, req)
	}()

	wg.Wait()

	if gaErr != nil {
		return gaErr
	}

	if ldErr != nil {
		return ldErr
	}

	piggyInfo := map[string]interface{}{
		constants.KeyPiggybankAccountID:                   accountInfo.Id,
		constants.KeyPiggybankInterestRate:                utils.FormatInterestValue(utils.HumanizeBalance(accountInfo.ApplicableInterestRate.TotalInterestRate, false)),
		constants.KeyPiggybankBalanceWithInterest:         utils.HumanizeBalance(accountInfo.AvailableBalance.Val, true),
		constants.KeyPiggybankOpeningTime:                 accountInfo.OpeningTimestamp,
		constants.KeyPiggybankBreakDate:                   accountInfo.ClosingTimestamp,
		constants.KeyPiggybankNearestDeductionAmount:      utils.HumanizeBalance(accountInfo.ProductSpecificParameters["nominalAmount"], false),
		constants.KeyPiggybankStatus:                      accountInfo.Status,
		constants.KeyPiggybankSourceOfFund:                accountInfo.ProductSpecificParameters["microsaverFundsSourceAccount"],
		constants.KeyPiggybankLevel:                       accountInfo.ProductSpecificParameters["tierName"],
		constants.KeyPiggybankBalance:                     "",
		constants.KeyPiggybankNearestDeductionSettingDate: "",
		constants.KeyPiggybankAutoIsi:                     constants.MappingPiggybankBooster[accountInfo.ProductSpecificParameters["isBoosterFlagEnabled"]],
		constants.KeyPiggybankAutoIsiSettingDate:          "",
		constants.KeyPiggybankClosureType:                 "",
		constants.KeyPiggybankGoalAmount:                  goalAmount,
		constants.KeyPiggybankLastSuccessDeduction:        lastDeduct,
	}

	for k, v := range piggyInfo {
		result[k] = v
	}
	return nil
}

func (p *process) getCustomerSearchPiggyGoalAmount(ctx context.Context) (string, error) {
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyPiggybankGoalAmount, constants.ProductVariantPiggybank)
	// get redisKey
	redisData, hasData, err := getStringDataPointFromRedis(ctx, redisKey)
	if hasData {
		var goalAmount string
		errMarshal := json.Unmarshal([]byte(redisData), &goalAmount)
		if errMarshal != nil {
			return "", errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return goalAmount, nil
	}
	if err != nil {
		return "", nil
	}

	request := &productMasterAPI.ListEffectiveProductVariantParametersRequest{
		ProductVariantCode: constants.ProductVariantPiggybank,
	}

	resp, err := helper.GetEffectiveProductVariant(ctx, request, p.ProductMasterClient, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return "", err
	}

	parameters := resp.ProductVariantParameters
	if len(parameters) == 0 {
		return "", nil
	}

	for _, param := range parameters {
		if param.ParameterKey == constants.PiggybankGoalAmountParameterKey {
			paramValue, errConv := strconv.ParseInt(param.ParameterValue, 10, 64)
			if errConv != nil {
				return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error formatting data point")
			}
			humanizeVal := utils.HumanizeBalance(paramValue, false)
			// set data to redis
			errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, humanizeVal, constants.CustomerSearchDataPointLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyPiggybankGoalAmount])
			if errRedis != nil {
				return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
			}
			return humanizeVal, nil
		}
	}

	return "", nil
}

func getStringDataPointFromRedis(ctx context.Context, redisKey string) (string, bool, error) {
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return "", false, err
	}

	if redisData != "" {
		var data string
		errMarshal := json.Unmarshal([]byte(redisData), &data)
		if errMarshal != nil {
			return "", false, errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return redisData, true, nil
	}

	return "", false, nil
}

// getLastSuccessDeduction ...
func (p *process) getCustomerSearchPiggyLastDeduct(ctx context.Context, req *api.CustomerSearchRequest) (string, error) {
	accountID := req.Payload["accountID"]
	var safeID string
	if accountID == "" {
		return "", errorwrapper.Error(apiError.BadRequest, "request has no valid accountID")
	}

	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		resp, err := p.GetCustomerSafeID(ctx, req.Identifier, string(req.IdentifierType), req.Key)
		if err != nil {
			return "", err
		}
		safeID = resp[constants.DataTypeSafeID].(string)
	}

	request := &txHistoryAPI.GetTransactionsHistoryRequest{
		AccountID: accountID,
		PageSize:  1,
	}

	resp, err := helper.GetTransactionList(ctx, safeID, request, p.TransactionHistoryClient, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error getting data point")
	}

	if len(resp.Data) == 0 {
		return "", nil
	}
	data := resp.Data[0].UpdateTimestamp.String()
	return data, nil
}

func setTermDepositAccount(result map[string]interface{}, data *accountServiceAPI.GetAccountResponse) {
	accountInfo := data.Account
	var tdPrincipalAmount, tdEarnedInterest, tdBalanceWithInterest float64
	aro := getAroMechanismMap(accountInfo.ProductSpecificParameters["tdMaturityInstructionType"])
	var isNonARO bool
	tdPrincipalAmount, _ = strconv.ParseFloat(accountInfo.ProductSpecificParameters["tdPrincipalAmount"], 64)
	tdEarnedInterest, _ = strconv.ParseFloat(accountInfo.ProductSpecificParameters["expectedTDInterest"], 64)
	tdBalanceWithInterest = tdPrincipalAmount + tdEarnedInterest

	if aro == constants.NonAROType {
		isNonARO = true
	}

	tdInfo := map[string]interface{}{
		constants.KeyTermDepositAccountNumber:               accountInfo.Id,
		constants.KeyTermDepositPrincipalAmount:             utils.HumanizeBalance(tdPrincipalAmount, false),
		constants.KeyTermDepositBalanceWithEstimateInterest: utils.HumanizeBalance(tdBalanceWithInterest, false),
		constants.KeyTermDepositEstimatedEarnedInterest:     utils.HumanizeBalance(tdEarnedInterest, false),
		constants.KeyTermDepositEstimatedTaxDeduction:       "",
		constants.KeyTermDepositSourceOfFund:                accountInfo.ProductSpecificParameters["tdSourceOfFund"],
		constants.KeyTermDepositStatus:                      accountInfo.Status,
		constants.KeyTermDepositInterestRate:                utils.FormatInterestValue(utils.HumanizeBalance(accountInfo.ProductSpecificParameters["applicableInterestRate"], false)),
		constants.KeyTermDepositInterestRateType:            "",
		constants.KeyTermDepositAroMechanism:                aro,
		constants.KeyTermDepositEarliestManualBreakDate:     "",
		constants.KeyTermDepositClosureType:                 accountInfo.ProductSpecificParameters["tdCloseType"],
		constants.KeyTermDepositIssuanceDate:                utils.DateAsString(accountInfo.OpeningTimestamp),
		constants.KeyTermDepositMaturityDate:                accountInfo.ProductSpecificParameters["tdMaturityDate"],
		constants.KeyTermDepositPlacementTenure:             accountInfo.ProductSpecificParameters["tdTenor"],
		constants.KeyTermDepositPlacementTenureType:         accountInfo.ProductSpecificParameters["tdTenorType"],
		constants.KeyTermDepositBreakTimestamp:              accountInfo.ClosingTimestamp,
		constants.KeyTermDepositRenewalHistory:              isNonARO,
	}

	for k, v := range tdInfo {
		result[k] = v
	}
}

// GetDepositParameterHistory ...
func (p process) GetDepositParameterHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["accountID"]

	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need account id as payload to request")
	}
	request := &accountServiceAPI.GetDepositsParameterHistoryRequest{
		AccountID:    accountID,
		ParameterKey: constants.TermDepositParameterKeyMaturityInstruction,
	}
	resp, err := helper.GetDepositParameterHistory(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	return setTDDepositParameterHistory(resp), nil
}

func setTDDepositParameterHistory(data *accountServiceAPI.GetDepositsParameterHistoryResponse) map[string]interface{} {
	result := make(map[string]interface{})
	if len(data.DepositsParameterHistory) == 0 {
		return result
	}

	var finalData []map[string]interface{}
	for i := len(data.DepositsParameterHistory) - 1; i >= 0; i-- {
		val := data.DepositsParameterHistory[i]
		fmt.Println(val.NewParameterValue)
		curData := map[string]interface{}{
			constants.KeyDepositParamTimestamp:      utils.ParsingAndFormatTime(val.UpdatedAt, time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyDepositParamMaturityBefore: val.OldParameterValue,
			constants.KeyDepositParamMaturityAfter:  val.NewParameterValue,
			constants.KeyDepositParamUpdatedBy:      val.UpdatedBy,
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyTermDepositParameterHistory] = finalData
	return result
}

// GetTermDepositRenewalHistory ...
func (p process) GetTermDepositRenewalHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["accountID"]

	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need account id as payload to request")
	}
	request := &accountServiceAPI.DepositsParameterRenewalHistoryRequest{
		AccountID: accountID,
	}
	resp, err := helper.GetRenewalHistory(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	return setTDRenewalHistory(resp), nil
}

func setTDRenewalHistory(data *accountServiceAPI.DepositsParameterRenewalHistoryResponse) map[string]interface{} {
	result := make(map[string]interface{})
	if data.Length == 0 {
		return result
	}

	var finalData []map[string]interface{}
	for _, val := range data.DepositsParameterRenewal {
		curData := map[string]interface{}{
			constants.KeyRenewalHistoryTimestamp:          utils.ParsingAndFormatTime(val.DepositsRenewalHistory["valueTimestamp"], time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyRenewalHistoryAmount:             utils.HumanizeBalance(val.DepositsRenewalHistory["tdUpdatedPrincipalAmount"], false),
			constants.KeyRenewalHistoryMaturityDate:       val.DepositsRenewalHistory["tdMaturityDate"],
			constants.KeyRenewalHistoryApplicableInterest: utils.FormatWithPercentage(utils.HumanizeBalance(val.DepositsRenewalHistory["tdAplicableInterest"], false)),
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyTermDepositRenewalHistory] = finalData

	return result
}

func (p process) GetTransactionConfigurationData(ctx context.Context, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (map[string]interface{}, error) {
	var safeID string

	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	switch req.Key {
	case constants.KeyCustomerLevelTxLimit:
		return p.GetCustomerLevelTxLimitHistory(ctx, safeID)
	case constants.KeyBankWideLevelTxLimit:
		return p.GetBankLevelTxLimitHistory(ctx, user.Email)
	}

	return nil, nil
}

func (p process) GetCustomerLevelTxLimitHistory(ctx context.Context, safeID string) (map[string]interface{}, error) {
	request := &txnLimitAPI.GetTransactionLimitRequestV2{
		LimitNames: []interface{}{constants.FetchTransferLimitNameBIFast},
	}
	resp, err := helper.FetchCustomerTransferLimit(ctx, request, safeID, p.TransactionLimitClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	if len(resp.Data) == 0 {
		return result, nil
	}

	var finalData []map[string]interface{}
	for _, val := range resp.Data {
		curData := map[string]interface{}{
			constants.KeyCustLimitTimestamp:   val.CreatedAt,
			constants.KeyCustLimitType:        constants.MappingTxLimitNameAndFrequency[val.LimitName],
			constants.KeyCustLimitFrequency:   constants.MappingTxLimitNameAndFrequency[val.Frequency],
			constants.KeyCustLimitMinAmount:   utils.HumanizeBalance(val.MinimumAmount, true),
			constants.KeyCustLimitMaxAmount:   utils.HumanizeBalance(val.MaximumAmount, true),
			constants.KeyCustLimitMinAllowed:  utils.HumanizeBalance(val.MinimumAllowed, true),
			constants.KeyCustLimitMaxAllowed:  utils.HumanizeBalance(val.MaximumAllowed, true),
			constants.KeyCustCumulativeAmount: utils.HumanizeBalance(val.CumulativeAmount, true),
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyCustomerLevelTxLimit] = finalData

	return result, nil
}

func (p process) GetBankLevelTxLimitHistory(ctx context.Context, email string) (map[string]interface{}, error) {
	request := &txnLimitAPI.GetTransactionLimitRulesRequest{
		LimitNames:        []string{constants.FetchTransferLimitNameBIFast},
		LimitRuleStatuses: []string{constants.LimitStatusActive},
	}

	resp, err := helper.GetTransferLimit(ctx, request, email, p.AppConfig.PaymentServiceConfig.PartnerID, p.TransactionLimitClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	if len(resp.Data) == 0 {
		return result, nil
	}

	var finalData []map[string]interface{}
	for _, val := range resp.Data {
		curData := map[string]interface{}{
			constants.KeyBankLimitTimestamp:  val.CreatedAt,
			constants.KeyBankLimitType:       constants.MappingTxLimitNameAndFrequency[val.LimitName],
			constants.KeyBankLimitFrequency:  constants.MappingTxLimitNameAndFrequency[val.Frequency],
			constants.KeyBankLimitMinAmount:  utils.HumanizeBalance(val.MinimumAmount, true),
			constants.KeyBankLimitMaxAmount:  utils.HumanizeBalance(val.MaximumAmount, true),
			constants.KeyBankLimitMinAllowed: utils.HumanizeBalance(val.MinimumAllowed, true),
			constants.KeyBankLimitMaxAllowed: utils.HumanizeBalance(val.MaximumAllowed, true),
			constants.KeyBankLimitStatus:     val.Status,
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyBankWideLevelTxLimit] = finalData

	return result, nil
}

func (p process) GetExternalLinkageData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	partners, err := helper.GetListPartner(ctx, safeID, p.PayAuthZClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	switch req.Key {
	case constants.KeyExternalLinkages:
		return composeRequestExternalLinkages(partners), nil
	case constants.KeyPartnerLinkage:
		linkedAcc, errCheck := p.GetLinkedAccountByPartner(ctx, safeID)
		if errCheck != nil {
			slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get ecosystem linked account : "+err.Error(), utils.GetTraceID(ctx))
		}
		onboardingChannel, errCheck := p.GetOnboardingChannelForEcosystem(ctx, safeID)
		if errCheck != nil {
			slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get ecosystem onboarding channel : "+err.Error(), utils.GetTraceID(ctx))
		}
		return composeRequestPartnerDetail(partners, linkedAcc, onboardingChannel, req)
	}
	return nil, nil
}

func composeRequestExternalLinkages(data *PayAuthZApi.ListPartnerResponse) map[string]interface{} {
	result := make(map[string]interface{})
	if len(data.ListPartner) == 0 {
		return result
	}

	var listPartner []interface{}
	for _, val := range data.ListPartner {
		partner := map[string]interface{}{
			constants.KeyEcosystemPartnerUserID: val.BillingAgreementID,
			constants.KeyEcosystemPartnerName:   val.PartnerName,
			constants.KeyEcosystemPartnerID:     val.PartnerID,
		}
		listPartner = append(listPartner, partner)
	}
	result[constants.KeyPartnerLinkage] = listPartner
	return result
}

func composeRequestPartnerDetail(data *PayAuthZApi.ListPartnerResponse, linkedAcc map[string]interface{}, onboardingChannel string, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	partnerID := req.Payload[constants.KeyEcosystemPartnerID]
	if partnerID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need partner user id as payload to request")
	}

	result := make(map[string]interface{})
	for _, val := range data.ListPartner {
		if val.PartnerID == partnerID {
			partnerDetail := map[string]interface{}{
				constants.KeyEcosystemPartnerName:             val.PartnerName,
				constants.KeyEcosystemPartnerID:               val.PartnerID,
				constants.KeyEcosystemPartnerUserID:           val.BillingAgreementID,
				constants.KeyEcosystemPartnerDailyTxLimitSet:  utils.HumanizeBalance(val.DailyTransactionLimit, true),
				constants.KeyEcosystemPartnerDailyTxLimitUsed: utils.HumanizeBalance(val.UsedDailyTransactionLimit, true),
				constants.KeyEcosystemPartnerLinkedAccount:    linkedAcc[val.PartnerName],
				constants.KeyEcosystemPartnerUnlinkReason:     "", // TODO
				constants.KeyEcosystemPartnerStatusNotes:      getBillingAgreementStatusNotes(val.Status),
				constants.KeyEcosystemPartnerStatus:           val.Status,
				constants.KeyEcosystemOnboardingChannel:       onboardingChannel,
			}
			return partnerDetail, nil
		}
	}
	return result, nil
}

// getBillingAgreementStatusNotes return mapping of the billing agreement status to the expected value to be displayed
func getBillingAgreementStatusNotes(status string) string {
	if note, ok := constants.BillingAgreementStatusNotesMap[status]; ok {
		return note
	}
	return status
}

// getAroMechanismMap return mapping of the aro mechanism
func getAroMechanismMap(aro string) string {
	if aroMap, ok := constants.AroMechanismMap[aro]; ok {
		return aroMap
	}
	return aro
}

// GetLinkingHistory ...
func (p process) GetLinkingHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 32)
	if pageSize == 0 {
		pageSize = constants.DefaultPaginationSize
	}

	partnerID := req.Payload[constants.KeyEcosystemPartnerID]
	if partnerID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need partner user id as payload to request")
	}

	request := &customerJournalAPI.Request{
		UserSafeID:     fmt.Sprintf("%s_%s", safeID, partnerID),
		PageSize:       int32(pageSize),
		StartDate:      req.Payload["startDate"],
		EndDate:        req.Payload["endDate"],
		StartingBefore: req.Payload["startingBefore"],
		EndingAfter:    req.Payload["endingAfter"],
		Endpoint:       constants.CustomerJournalLogTypeEcosystem,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)
	if err != nil {
		return nil, err
	}
	return composeEcosystemEventLog(ctx, resp), nil
}

func composeEcosystemEventLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata, deviceInfo map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			if convDeviceInfo, ok := metadata["deviceInfo"].(map[string]interface{}); ok {
				deviceInfo = convDeviceInfo
			}

			currentData := map[string]interface{}{
				constants.KeyLinkingHistoryTimestamp:     transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyLinkingHistoryPreviousLimit: utils.HumanizeBalance(metadata["previousLimit"].(string), true),
				constants.KeyLinkingHistoryUpdatedLimit:  utils.HumanizeBalance(metadata["currentLimit"].(string), true),
				constants.KeyLinkingHistoryAccountID:     metadata["accountID"],
				constants.KeyLinkingHistoryAction:        metadata["action"],
				constants.KeyLinkingHistoryUnlinkReason:  metadata["unlinkReason"],
				constants.KeyLinkingHistoryActionBy:      constructEcosystemActionBy(metadata["actionBy"]),
				constants.KeyLinkingHistoryActionSystem:  metadata["actionSystem"],
				constants.KeyLinkingHistoryActionStatus:  metadata["status"],
				constants.KeyLinkingHistoryPartnerName:   metadata["partnerName"],
				constants.KeyLinkingHistoryDeviceID:      deviceInfo["deviceID"],
				constants.KeyLinkingHistoryDeviceBrand:   deviceInfo["deviceBrand"],
				constants.KeyLinkingHistoryDeviceModel:   deviceInfo["deviceModel"],
				constants.KeyLinkingHistoryOSName:        deviceInfo["deviceOSName"],
				constants.KeyLinkingHistoryOSVersion:     deviceInfo["deviceOSVersion"],
				constants.KeyLinkingErrorMessage:         metadata["failureReason"],
			}
			finalData = append(finalData, currentData)
		}
	}

	result[constants.KeyLinkingHistory] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func constructEcosystemActionBy(actionBy interface{}) string {
	if action, ok := actionBy.(string); ok {
		if action == "" {
			return ""
		}
		splits := strings.Split(action, "-")
		if strings.Contains(action, "Ops") {
			return fmt.Sprintf("Ops - %s", splits[len(splits)-1])
		} else if strings.Contains(action, "CRM") {
			return fmt.Sprintf("CRM - %s", splits[len(splits)-1])
		} else {
			return splits[len(splits)-1]
		}
	}

	return ""
}

// GetReauthenticationHistory ...
func (p process) GetReauthenticationHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 32)
	if pageSize == 0 {
		pageSize = constants.DefaultPaginationSize
	}

	request := &customerJournalAPI.Request{
		UserSafeID:     safeID,
		PageSize:       int32(pageSize),
		StartDate:      req.Payload["startDate"],
		EndDate:        req.Payload["endDate"],
		StartingBefore: req.Payload["startingBefore"],
		EndingAfter:    req.Payload["endingAfter"],
		Endpoint:       constants.CustomerJournalLogTypeOvoNabung,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)
	if err != nil {
		return nil, err
	}
	return composeReauthenticationEventLog(ctx, resp), nil
}

func composeTableWithPaginationResponse(data []map[string]interface{}, pagination interface{}) map[string]interface{} {
	result := map[string]interface{}{
		"data":       data,
		"pagination": pagination,
	}
	return result
}

func composeReauthenticationEventLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			currentData := map[string]interface{}{
				constants.KeyReauthHistoryTimestamp:        transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyReauthHistoryPrevMobileNumber: metadata["oldPhoneNumber"],
				constants.KeyReauthHistoryNewMobileNumber:  metadata["newPhoneNumber"],
				constants.KeyReauthHistoryStatus:           metadata["status"],
				constants.KeyReauthHistoryStatusReason:     metadata["statusReason"],
			}
			finalData = append(finalData, currentData)
		}
	}

	result[constants.KeyReauthHistory] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func (p process) GetLinkedAccountByPartner(ctx context.Context, safeID string) (map[string]interface{}, error) {
	linkedAccMapping := make(map[string]interface{})
	// get from redis
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemPartnerLinkedAccount, safeID)

	// get redisKey
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	if redisData != "" {
		errMarshal := json.Unmarshal([]byte(redisData), &linkedAccMapping)
		if errMarshal != nil {
			return nil, errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return linkedAccMapping, nil
	}

	// get from API
	cifNumber, err := p.getGlobalDataByIdentifier(ctx, safeID, api.IdentifierType_SAFE_ID, constants.DataTypeCif)
	if err != nil {
		return linkedAccMapping, err
	}

	request := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber: cifNumber,
	}

	resp, err := helper.GetAccountList(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return linkedAccMapping, err
	}

	filteredAcc := filterByVariantForFunding(resp.Accounts)
	// get grab
	if len(filteredAcc[constants.ProductVariantMainCasa]) > 0 {
		mainCasa := filteredAcc[constants.ProductVariantMainCasa]
		if acc, ok := mainCasa[len(mainCasa)-1].(map[string]interface{}); ok {
			linkedAccMapping[constants.PartnerNameGrab] = acc["accountID"]
		}
	}
	// get ovo
	if len(filteredAcc[constants.ProductVariantOvoNabung]) > 0 {
		for _, item := range filteredAcc[constants.ProductVariantOvoNabung] {
			if acc, ok := item.(map[string]interface{}); ok {
				if acc["status"] != constants.AccountStatusClosed {
					linkedAccMapping[constants.PartnerNameOvo] = acc["accountID"]
				}
			}
		}
	}
	// set to redis
	errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, linkedAccMapping, constants.CustomerSearchDataPointLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyEcosystemPartnerLinkedAccount])
	if errRedis != nil {
		return linkedAccMapping, errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
	}

	return linkedAccMapping, nil
}

func (p process) GetOnboardingChannelForEcosystem(ctx context.Context, safeID string) (string, error) {
	// get from redis
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemOnboardingChannel, safeID)

	// get redisKey
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchLogTag)
	if err != nil {
		return "", err
	}
	if redisData != "" {
		var onboardingChannel string
		errMarshal := json.Unmarshal([]byte(redisData), &onboardingChannel)
		if errMarshal != nil {
			return "", errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return onboardingChannel, nil
	}

	// get from API
	resp, err := p.GetCustomerByIdentifier(ctx, safeID, string(api.IdentifierType_SAFE_ID), 1)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer related data by identifier")
	}

	var onboardingChannel string
	onboardingInfo := resp.Items[0].Applications
	if len(onboardingInfo) == 0 {
		return "", nil
	}
	onboardingChannel = string(onboardingInfo[0].Channel)

	// set to redis
	errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, onboardingChannel, constants.CustomerSearchLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyEcosystemOnboardingChannel])
	if errRedis != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
	}

	return onboardingChannel, nil
}

func transformEpochTimestamp(ctx context.Context, timestamp interface{}) string {
	if eventTimestamp, ok := timestamp.(string); ok {
		intEventTimestamp, err := strconv.ParseInt(eventTimestamp, 10, 64)
		if err != nil {
			slogwrapper.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "error to parse eventTimestamp : "+err.Error(), utils.GetTraceID(ctx))
			return ""
		}

		milliseconds := intEventTimestamp / 1e6 // Convert nanoseconds to milliseconds
		t := time.Unix(0, milliseconds*int64(time.Millisecond))
		return t.Format(constants.ISO8601Layout)
	}
	return ""
}
