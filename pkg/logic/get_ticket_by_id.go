package logic

import (
	"context"
	"database/sql"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetTicketByID ...
//
// nolint: funlen
func (p *process) GetTicketByID(ctx context.Context, req *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// get ticket by id
	ticketDTO, err := storage.GetTicketByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket by id: %v", err))
	}

	//Authenticate the request
	user, bitwiseValue, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestForElement(ctx, ticketDTO.ElementID, constants.BitwiseValueReadTicket)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// get ticket chain to determine possible action id
	ticketChain, err := storage.GetTicketChainByElementIDAndCurrentStatusID(ctx, db, ticketDTO.ElementID, ticketDTO.TicketStatusID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket chain by element id and current status id: %v", err))
	}
	ticketActionsMap := make(map[string]int64)
	for _, chain := range ticketChain {
		if bitwiseValue&chain.BitwiseRequired == chain.BitwiseRequired {
			ticketActionsMap[chain.ActionName] = chain.NextStatusID
		}
	}

	ticketActions, rolesRequired := p.determineTicketActions(ctx, ticketDTO, user.ID, ticketActionsMap, ticketChain, db)
	slog.FromContext(ctx).Info(constants.GetTicketLogTag, fmt.Sprintf("[DEBUG] rolesRequired: %+v", rolesRequired))

	// Get role names for required roles
	roleNames, err := p.getRoleNames(ctx, db, rolesRequired)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get role names: %v", err))
	}

	// Get ticket histories
	ticketHistories, err := storage.GetTicketHistoriesByTicketID(ctx, db, req.Id)
	if err != nil {
		return nil, err
	}
	resTicketHistories := make([]api.TicketHistory, 0)
	for _, ticketHistory := range ticketHistories {
		resTicketHistories = append(resTicketHistories, api.TicketHistory{
			Id:           ticketHistory.ID,
			TicketID:     ticketHistory.TicketID,
			Action:       ticketHistory.ActionName,
			Data:         ticketHistory.Data,
			CreatedAt:    utils.DateAsString(ticketHistory.CreatedAt.Time),
			CreatedBy:    ticketHistory.CreatedBy.Int64,
			Note:         ticketHistory.Note.String,
			PrevStatusID: ticketHistory.PrevStatusID,
			NextStatusID: ticketHistory.NextStatusID,
		})
	}

	// Get linked tickets
	var linkedTickets []api.LinkedTicket

	// If this ticket has a parent, fetch the parent ticket
	if ticketDTO.ParentTicketID.Valid && ticketDTO.ParentTicketID.Int64 != 0 {
		parentDTO, err := storage.GetTicketByID(ctx, db, ticketDTO.ParentTicketID.Int64)
		if err == nil {
			linkedTickets = append(linkedTickets, api.LinkedTicket{
				Id:              parentDTO.ID,
				CreatedAt:       utils.DateAsString(parentDTO.CreatedAt.Time),
				CreatedBy:       fmt.Sprint(parentDTO.CreatedBy.Int64),
				CaseCategory:    parentDTO.CaseCategory.String,
				CaseSubcategory: parentDTO.CaseSubcategory.String,
				ElementID:       parentDTO.ElementID,
				Relationship:    "Parent",
			})
		}
	}

	// If this ticket is a parent, fetch all child tickets
	childConditions := []commonStorage.QueryCondition{
		commonStorage.EqualTo("parent_ticket_id", ticketDTO.ID),
	}
	childDTOs, err := storage.GetTicketList(ctx, db, childConditions)
	if err != nil {
		return nil, err
	}

	if len(childDTOs) > 0 {
		for _, child := range childDTOs {
			linkedTickets = append(linkedTickets, api.LinkedTicket{
				Id:              child.ID,
				ElementID:       child.ElementID,
				StatusID:        child.TicketStatusID,
				CreatedAt:       utils.DateAsString(child.CreatedAt.Time),
				CreatedBy:       fmt.Sprint(child.CreatedBy.Int64),
				CaseCategory:    child.CaseCategory.String,
				CaseSubcategory: child.CaseSubcategory.String,
				ParentTicketID:  child.ParentTicketID.Int64,
				Relationship:    "Child",
			})
		}
	}

	return &api.GetTicketByIDResponse{
		Ticket: &api.Ticket{
			Id:                  ticketDTO.ID,
			ElementID:           ticketDTO.ElementID,
			Data:                ticketDTO.Data,
			StatusID:            ticketDTO.TicketStatusID,
			CreatedAt:           utils.DateAsString(ticketDTO.CreatedAt.Time),
			UpdatedAt:           utils.DateAsString(ticketDTO.UpdatedAt.Time),
			CreatedBy:           fmt.Sprint(ticketDTO.CreatedBy.Int64),
			UpdatedBy:           fmt.Sprint(ticketDTO.UpdatedBy.Int64),
			PriorityID:          ticketDTO.PriorityID,
			Source:              api.TicketSource(ticketDTO.Source),
			DeadlineTime:        utils.DateAsString(ticketDTO.DeadlineTime.Time),
			AssigneeUserID:      ticketDTO.AssigneeUserID.Int64,
			AssigneeUserName:    ticketDTO.AssigneeUserName.String,
			TicketRequestorName: ticketDTO.TicketRequestorName.String,
			CaseCategory:        ticketDTO.CaseCategory.String,
			CaseSubcategory:     ticketDTO.CaseSubcategory.String,
			DomainID:            ticketDTO.DomainID.String,
			Channel:             ticketDTO.Channel.String,
			CustomerSegmentName: ticketDTO.CustomerSegmentName.String,
			TicketCloseDatetime: utils.DateAsString(ticketDTO.TicketCloseDatetime.Time),
			ParentTicketID:      ticketDTO.ParentTicketID.Int64,
		},
		Histories:            resTicketHistories,
		ActionsNextStatusMap: ticketActions,
		RolesRequired:        roleNames,
		LinkedTickets:        linkedTickets,
	}, nil
}

func (p *process) determineTicketActions(ctx context.Context, ticketDTO *storage.TicketDTO, userID int64, ticketActionsMap map[string]int64, ticketChain []*storage.TicketChainDTO, db *sql.DB) (map[string]int64, []int64) {
	// Ticket is assigned to someone else
	if ticketDTO.AssigneeUserID.Int64 != 0 && ticketDTO.AssigneeUserID.Int64 != userID {
		return make(map[string]int64), []int64{}
	}

	// Get user's roles
	userRolesIds, err := p.getUserRolesIds(ctx, userID)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("[DEBUG] getUserRoles error: %+v", err))
		return make(map[string]int64), []int64{}
	}

	// Create a map of action to chain for easy lookup
	chainMap := make(map[string]*storage.TicketChainDTO)
	for _, chain := range ticketChain {
		chainMap[chain.ActionName] = chain
	}

	// Filter actions based on conditions and role
	filteredActions := make(map[string]int64)
	rolesRequiredMap := make(map[int64]bool) // Map to track unique role IDs
	var rolesRequired []int64
	for action, nextStatusID := range ticketActionsMap {
		chain := chainMap[action]
		canAction, roleRequiredForAction := checkActionPermission(ctx, db, action, ticketDTO, userRolesIds, chain)
		if chain != nil && canAction {
			filteredActions[action] = nextStatusID
		}

		// Add unique roles to the map and list
		for _, roleID := range roleRequiredForAction {
			if !rolesRequiredMap[roleID] {
				rolesRequiredMap[roleID] = true
				rolesRequired = append(rolesRequired, roleID)
			}
		}
	}

	if len(ticketActionsMap) == 0 {
		// No possible actions, but still collect all roles required for this ticket's status
		for _, chain := range ticketChain {
			_, roleRequiredForAction := checkActionPermission(ctx, db, chain.ActionName, ticketDTO, userRolesIds, chain)
			for _, roleID := range roleRequiredForAction {
				if !rolesRequiredMap[roleID] {
					rolesRequiredMap[roleID] = true
					rolesRequired = append(rolesRequired, roleID)
				}
			}
		}
	}

	slog.FromContext(ctx).Info(constants.GetTicketLogTag, fmt.Sprintf("[DEBUG] ticketActionsMap: %+v", ticketActionsMap))
	slog.FromContext(ctx).Info(constants.GetTicketLogTag, fmt.Sprintf("[DEBUG] filteredActions: %+v", filteredActions))

	// Ticket is unassigned but have action
	if ticketDTO.AssigneeUserID.Int64 == 0 && len(filteredActions) > 0 {
		// Return empty action if ticket is under execute_system
		if _, hasSystemExecute := ticketActionsMap[constants.ActionSystemExecute]; hasSystemExecute {
			return make(map[string]int64), []int64{}
		}
		// Return assign to self action
		return map[string]int64{
			constants.ActionAssignSelf: ticketDTO.TicketStatusID,
		}, rolesRequired
	}

	// Ticket is assigned to current user, then user can deassign
	if ticketDTO.AssigneeUserID.Int64 == userID {
		if _, hasDraftAction := ticketActionsMap[constants.ActionMakerDraft]; !hasDraftAction {
			filteredActions[constants.ActionDeassignSelf] = ticketDTO.TicketStatusID
		}
	}

	return filteredActions, rolesRequired
}

func (p *process) getUserRolesIds(ctx context.Context, userID int64) ([]int64, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get user roles from database
	roles, err := permissionManagementStorage.GetUserRole(ctx, db, userID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user roles")
	}

	// Convert role DTOs to role ids
	roleIds := make([]int64, 0, len(roles))
	for _, role := range roles {
		roleIds = append(roleIds, role.ID)
	}

	return roleIds, nil
}

// getRoleNames converts a slice of role IDs to their corresponding role names
func (p *process) getRoleNames(ctx context.Context, db *sql.DB, roleIDs []int64) ([]string, error) {
	roleNames := make([]string, 0)
	if len(roleIDs) == 0 {
		return roleNames, nil
	}

	for _, roleID := range roleIDs {
		role, err := permissionManagementStorage.GetRoleByID(ctx, db, roleID)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get role name for ID %d: %v", roleID, err))
			continue
		}
		roleNames = append(roleNames, role.Name)
	}

	return roleNames, nil
}
