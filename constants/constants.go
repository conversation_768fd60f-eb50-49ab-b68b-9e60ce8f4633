// Package constants provides the constants used in the application.
package constants

const (
	// BitwiseValueReadTicket is the bitwise value for viewing a ticket.
	BitwiseValueReadTicket int64 = 1

	// BitwiseValueCreateTicket is the bitwise value for creating a ticket.
	BitwiseValueCreateTicket int64 = 2

	// BitwiseValueUpdateTicket is the bitwise value for updating a ticket.
	BitwiseValueUpdateTicket int64 = 4
)

const (
	// ActionNew ...
	ActionNew string = "NEW"

	// ActionMakerSubmit ...
	ActionMakerSubmit string = "MAKER_SUBMIT"

	// ActionCheckerApprove ...
	ActionCheckerApprove string = "CHECKER_APPROVE"

	// ActionCheckerReject ...
	ActionCheckerReject string = "CHECKER_REJECT"

	// ActionCheckerReturnToMaker ...
	ActionCheckerReturnToMaker string = "CHECKER_RETURN_TO_MAKER"

	// ActionSystemExecute ...
	ActionSystemExecute string = "SYSTEM_EXECUTE"

	// ActionAssignSelf ...
	ActionAssignSelf string = "ASSIGN_SELF"

	// ActionDeassignSelf ...
	ActionDeassignSelf string = "DEASSIGN_SELF"

	// ActionMakerDraft ...
	ActionMakerDraft string = "MAKER_DRAFT"

	// ActionMakerSubmitDraft ...
	ActionMakerSubmitDraft string = "MAKER_SUBMIT_DRAFT"
)

const (
	// XGrabkitClientID ...
	XGrabkitClientID = "x-grabkit-clientid"

	// OnedashAPI ...
	OnedashAPI = "onedash-api"

	// ID ..
	ID = "ID"

	// DIGIBANK ...
	DIGIBANK = "DIGIBANK"

	// SuccessStatus ...
	SuccessStatus = "Success"
)

// ticket type
const (
	// UnlinkAccountTicketType is ticket type for unlink account
	UnlinkAccountTicketType = "Unlink Account"

	// TransferOnBehalfTicketType is ticket type for unlink account
	TransferOnBehalfTicketType = "Transfer On Behalf"
)

// Product variant code
const (
	// ProductVariantCodeLOC ...
	ProductVariantCodeLOC = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
)

// locAccountDeactivationStatuses
const (
	// LOCDeactivationFailed ...
	LOCDeactivationFailed = "FAILED"
)

// QueueEventName ...
const (
	// DeactivateLOC ...
	DeactivateLOC = "DEACTIVATE_LOC"
)

// Streams IDs.
const (
	// DepositsAccountDetailsStreamHandlerLogTag ...
	DepositsAccountDetailsStreamHandlerLogTag = "handler.depositsAccountDetailsStreamHandlerLogTag"
)

// kafka log tags
const (
	// DepositsAccountDetailsStreamConsumerLogTag kafka consumer and logic layer log tag
	DepositsAccountDetailsStreamConsumerLogTag = "consumer.depositsAccountDetailsStreamConsumerLogTag"
)

// ElementCodes is the type for element codes
type ElementCodes string

// element codes
const (
	// BlockAccount ...
	BlockAccount ElementCodes = "BLOCK_ACCOUNT"

	// UnblockAccount ...
	UnblockAccount ElementCodes = "UNBLOCK_ACCOUNT"

	// RoleManagement ...
	RoleManagement ElementCodes = "ROLE_MANAGEMENT"

	// UserManagement ...
	UserManagement ElementCodes = "USER_MANAGEMENT"

	// FeatureFlag ...
	FeatureFlag ElementCodes = "FEATURE_FLAG"

	// ModuleConfig ...
	ModuleConfig ElementCodes = "MODULE_CONFIG"

	// CustomerSearch ...
	CustomerSearch ElementCodes = "CUSTOMER_SEARCH"

	// DataSegregation ...
	DataSegregation ElementCodes = "DATA_SEGREGATION"

	// Chatbot System Prompt ...
	ChatbotSystemPrompt ElementCodes = "CHATBOT_SYSTEM_PROMPT"

	// Chatbot Knowledge Base ...
	ChatbotKnowledgeBase ElementCodes = "CHATBOT_KNOWLEDGE_BASE"

	// Chatbot Escape Keywords ...
	ChatbotEscapeKeywords ElementCodes = "CHATBOT_ESCAPE_KEYWORDS"

	// Chatbot Config ...
	ChatbotConfig ElementCodes = "CHATBOT_CONFIG"

	// Chatbot Whitelist ...
	ChatbotWhitelist ElementCodes = "CHATBOT_WHITELIST"

	// Chatbot Tag ...
	ChatbotTag ElementCodes = "CHATBOT_TAG"

	// ReCDD ...
	ReCDD ElementCodes = "RECDD"

	// AuditTrail ...
	AuditTrail ElementCodes = "AUDIT_TRAIL"

	// ChangeAccountBlock ...
	ChangeAccountBlock ElementCodes = "CHANGE_ACCOUNT_BLOCK"

	// UpdateCustomerData ...
	UpdateCustomerData ElementCodes = "UPDATE_CUSTOMER_DATA"
)

// Authorization
const (
	// CtxAuthorization ...
	CtxAuthorization = "Authorization"
)

// Redis key
const (
	// UserIDRedisKey ...
	UserIDRedisKey = "user_id_"

	// FeatureFlagHashRedisKey ...
	FeatureFlagHashRedisKey = "feature_flag"

	// ConcurrentLoginRedisKey ...
	ConcurrentLoginRedisKey = "concurrentlogin"
)

const (
	// BitwiseValueGeneralRead is the bitwise value for read endpoint.
	BitwiseValueGeneralRead int64 = 1

	// BitwiseValueGeneralCreate is the bitwise value for create endpoint.
	BitwiseValueGeneralCreate int64 = 2

	// BitwiseValueGeneralUpdate is the bitwise value for update endpoint.
	BitwiseValueGeneralUpdate int64 = 4
)

// General Status
const (
	// StatusInactive ...
	StatusInactive = 0
	// StatusActive ...
	StatusActive = 1
)

// StatusWordingMap ...
var StatusWordingMap = map[int]string{
	1: "Active",
	0: "Inactive",
}

const (
	// SystemUserID is the user id for system user
	SystemUserID = 0
)

// AuditStatusWordingMap ...
var AuditStatusWordingMap = map[int]string{
	1: "Activated",
	0: "Deactivated",
}

// ChatbotAction ...
type ChatbotAction string

var (
	// ChatbotAdd represents the action of adding a new chatbot item
	ChatbotAdd ChatbotAction = "Add"
	// ChatbotUpdate represents the action of updating an existing chatbot item
	ChatbotUpdate ChatbotAction = "Update"
	// ChatbotDelete represents the action of deleting a chatbot item
	ChatbotDelete ChatbotAction = "Delete"
	// ChatbotImport represents the action of importing chatbot data
	ChatbotImport ChatbotAction = "Import"
)

const (
	CustomerSegementRetail int64 = 1
	CustomerSegementBIZ    int64 = 2
	CustomerSegementCommon int64 = 3
)

// CustomerJourneyExperience ...
const (
	// ConsentTypePush
	ConsentTypePush = "PUSH"
	// ConsentTypeEmail
	ConsentTypeEmail = "EMAIL"
	// ConsentTypeSms
	ConsentTypeSms = "SMS"
	// ConsentTypeWhatsapp
	ConsentTypeWhatsapp = "WHATSAPP"
)

// WhitelistedSearchAccountApi is a map of element codes that are whitelisted for search account API
var WhitelistedSearchAccountApi = map[ElementCodes]bool{
	ChangeAccountBlock: true,
	UpdateCustomerData: true,
}

const (
	HomeCountryMY = "MY"
	HomeCountryID = "ID"
)
