package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// UpdateTicket Updates a ticket
//
// nolint: funlen, dupl, errcheck
func (p *process) UpdateTicket(ctx context.Context, req *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, err
	}

	// get ticket by id
	ticketDTO, err := storage.GetTicketByID(ctx, master, req.Id)
	if err != nil {
		return nil, err
	}
	prevStatusID := ticketDTO.TicketStatusID

	// Authenticate the request
	user, bitwiseValue, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestForElement(ctx, ticketDTO.ElementID, constants.BitwiseValueUpdateTicket)
	if err != nil {
		return nil, err
	}

	// for ticket advancement only will check ticket chain
	chainBitwiseRequired, err := storage.GetTicketChainBitwiseRequired(ctx, master, req.Id, req.NextStatusID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain bitwise required")
	} else if bitwiseValue&chainBitwiseRequired != chainBitwiseRequired {
		return nil, errorwrapper.Error(apiError.Forbidden, "user is not authorized to update ticket")
	}

	dto := makeUpdateTicketDTO(ticketDTO, req, user.ID)

	// Begin transaction
	tx, err := master.Begin()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	// update the ticket
	err = storage.UpdateTicket(ctx, tx, dto)
	if err != nil {
		return nil, err
	}

	// Insert the ticket history
	historyID, err := storage.CreateTicketHistory(ctx, tx, &storage.TicketHistoryDTO{
		TicketID:     req.Id,
		Data:         req.Data,
		ActionName:   req.Action,
		Note:         sql.NullString{String: req.Note, Valid: true},
		PrevStatusID: prevStatusID,
		NextStatusID: req.NextStatusID,
		CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:    sql.NullInt64{Int64: user.ID, Valid: true},
	})
	if err != nil {
		return nil, err
	}

	// commit the transaction
	err = tx.Commit()
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("failed to commit transaction: %v", err))
	}

	title, description := getNotificationDetails(req.Action, user.Name, dto)

	// Determine event type based on status change
	var eventType string
	if prevStatusID != req.NextStatusID {
		// Use status IDs to determine event type
		if req.NextStatusID == constants.TicketStatusCompleted {
			eventType = constants.CaseCompleted
		} else if req.NextStatusID == constants.TicketStatusRejected {
			eventType = constants.CaseRejected
		} else {
			eventType = constants.CaseStatusChanged
		}
	} else {
		eventType = constants.CaseUpdated
	}

	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":      dto.ID,
		"element_id":     dto.ElementID,
		"priority_id":    dto.PriorityID,
		"prev_status_id": prevStatusID,
		"next_status_id": dto.TicketStatusID,
		"source":         dto.Source,
		"deadline_time":  dto.DeadlineTime.Time,
		"assignee_id":    dto.AssigneeUserID.Int64,
		"event_type":     eventType,
		"note":           req.Note,
		"action":         req.Action,
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     strconv.Itoa(int(dto.ID)),
		IdentifierType: constants.TicketID,
		Title:          title,
		Description:    description,
		ActivityType:   constants.Ticket,
		ReferenceID:    strconv.Itoa(int(historyID)),
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	// check auto-assign
	// _ = p.CheckTicketAutoAssign(ctx, dto)

	if req.Action == constants.ActionCheckerReject {
		auditTrailReq.Title = "Ticket rejected"
		auditTrailReq.Description = fmt.Sprintf("User %s rejected ticket with id %d", user.Name, dto.ID)
		auditTrailReq.ReferenceID = ""
		_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
		if auditErr != nil {
			slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing reject audit trail req: %v, error: %v", auditTrailReq, auditErr))
		}
	}

	// check whether to trigger system execution
	err = p.CheckTicketExecution(ctx, dto)
	if err != nil {
		slog.FromContext(ctx).Error(updateTicketLogTag, fmt.Sprintf("error checking ticket execution: %v", err))
	}

	return &api.UpdateTicketResponse{
		Id: dto.ID,
	}, nil
}

func makeUpdateTicketDTO(current *storage.TicketDTO, req *api.UpdateTicketRequest, userID int64) *storage.TicketDTO {
	var updated = *current

	updated.TicketStatusID = req.NextStatusID
	updated.Data = req.Data
	updated.UpdatedBy = sql.NullInt64{Int64: userID, Valid: true}
	updated.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	updated.AssigneeUserID = sql.NullInt64{Int64: 0, Valid: false}
	if req.Action == constants.ActionMakerDraft {
		updated.AssigneeUserID = sql.NullInt64{Int64: userID, Valid: true}
	}

	return &updated
}

func getNotificationDetails(action string, userName string, dto *storage.TicketDTO) (title, description string) {
	switch action {
	case constants.ActionMakerDraft, constants.ActionMakerSubmitDraft:
		title = "Ticket draft updated"
		description = fmt.Sprintf("User %s updated ticket draft with id %d", userName, dto.ID)

	case constants.ActionCheckerReturnToMaker:
		title = "Ticket revised"
		description = fmt.Sprintf("User %s revised ticket back to maker with id %d", userName, dto.ID)

	case constants.ActionCheckerApprove:
		title = "Ticket approved"
		description = fmt.Sprintf("User %s approved ticket with id %d", userName, dto.ID)

	default:
		title = "Ticket updated"
		description = fmt.Sprintf("User %s updated ticket with id %d", userName, dto.ID)
	}
	return title, description
}
