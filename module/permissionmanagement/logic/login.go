package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"crypto/rand"
	"encoding/base64"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/secret"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// Login ...
func (p *PermissionManagementImpl) Login(ctx context.Context, req *api.LoginRequest) (*api.LoginResponse, error) {
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to connect database")
	}

	var user *storage.UserDTO
	if req.Type == api.LoginType_JUMPCLOUD {
		user, err = p.verifyJumpCloud(ctx, db, req)
	} else if req.Type == api.LoginType_SERVICE {
		user, err = p.verifyServiceLogin(ctx, db, req)
	} else {
		if !p.AppConfig.FeatureFlag.UseNativeLogin {
			return nil, errorwrapper.Error(apiError.Forbidden, "Login type not supported (Native)")
		}
		// Get the user by email
		user, err = storage.GetUserByEmail(ctx, db, req.Email)
		if err != nil {
			return nil, err
		}
		err = verifyNativeType(user.Password, req.Password)
	}
	if err != nil {
		return nil, err
	}
	if user.Status == constants.StatusInactive {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is inactive")
	}

	// check concurrent login
	err = p.checkConcurrentLogin(ctx, user.UserID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "concurrent login error")
	}

	token, refreshToken, err := p.generateToken(user.UserID)
	if err != nil {
		return nil, err
	}

	// Insert refresh token to DB
	user.RefreshToken = sql.NullString{String: refreshToken, Valid: true}
	err = storage.UpdateUserExpiryToken(ctx, db, refreshToken, user.UserID, user.ID)
	if err != nil {
		return nil, err
	}

	// get and set user permission
	_, err = p.ResetPermissionByUserID(ctx, user.UserID, constants.LoginLogTag)
	if err != nil {
		return nil, err
	}

	// write concurrent login
	err = p.writeConcurrentLogin(ctx, user.UserID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to write concurrent login")
	}

	// write audit trail
	err = helper.WriteLogAuditTrailUser(ctx, user, "Login", "Login Successful", constants.Login, user.ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoginLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	return &api.LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
	}, nil
}

func (p *PermissionManagementImpl) verifyJumpCloud(ctx context.Context, db *sql.DB, req *api.LoginRequest) (*storage.UserDTO, error) {
	userInfo, err := jumpcloud.JCClient.VerifyEmail(ctx, req.Token, constants.LoginLogTag)
	if err != nil {
		return nil, err
	}

	user, getUserErr := storage.GetUserByEmail(ctx, db, req.Email)
	if getUserErr != nil {
		if errorwrapper.IsErrorCodeExist(getUserErr, apiError.ResourceNotFound) {
			// User not found, create new user
			newUser, createErr := p.createJumpCloudUser(ctx, db, &userInfo)
			if createErr != nil {
				return nil, createErr
			}
			return newUser, nil
		}
		return nil, getUserErr
	}

	return user, nil
}

func verifyNativeType(userPassword, reqPassword string) error {
	if reqPassword == "" {
		return errorwrapper.Error(apiError.FieldMissing, "missing password")
	}
	return secret.MatchPassword(reqPassword, userPassword)
}

func (p *PermissionManagementImpl) generateToken(userID string) (string, string, error) {
	// Return token (expiry in 30 min)
	token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
		"id": userID,
	}, p.AppConfig.TokenKey.AccessTokenExpiryTime, p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return "", "", err
	}

	// Create refresh token (expiry in 12 hours)
	refreshToken, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
		"id": userID,
	}, p.AppConfig.TokenKey.RefreshTokenExpiryTime, p.AppConfig.TokenKey.RefreshTokenKey)
	if err != nil {
		return "", "", err
	}

	return token, refreshToken, nil
}

func (p *PermissionManagementImpl) checkConcurrentLogin(ctx context.Context, userID string) error {
	if !p.AppConfig.FeatureFlag.UseConcurrentLoginLimit {
		return nil
	}
	val, err := redis.GetRedisValue(ctx, fmt.Sprintf("%s:%s", constants.ConcurrentLoginRedisKey, userID), "checkConcurrentLogin")
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to check concurrent login")
	}
	if val != "" {
		return errorwrapper.Error(apiError.Forbidden, "User is already logged in")
	}
	return nil
}

func (p *PermissionManagementImpl) writeConcurrentLogin(ctx context.Context, userID string) error {
	if !p.AppConfig.FeatureFlag.UseConcurrentLoginLimit {
		return nil
	}
	err := redis.SetRedisValue(ctx, fmt.Sprintf("%s:%s", constants.ConcurrentLoginRedisKey, userID), "1", int64(p.AppConfig.TokenKey.AccessTokenExpiryTime), "writeConcurrentLogin")
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to set concurrent login")
	}
	return nil
}

func (p *PermissionManagementImpl) deleteConcurrentLogin(ctx context.Context, userID string) error {
	if !p.AppConfig.FeatureFlag.UseConcurrentLoginLimit {
		return nil
	}
	err := redis.DeleteRedisKey(ctx, fmt.Sprintf("%s:%s", constants.ConcurrentLoginRedisKey, userID), "deleteConcurrentLogin")
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to delete concurrent login")
	}
	return nil
}

func (p *PermissionManagementImpl) verifyServiceLogin(ctx context.Context, db *sql.DB, req *api.LoginRequest) (*storage.UserDTO, error) {
	service := ""
	for k, v := range p.AppConfig.ServicesCredential {
		if v.ClientID == req.ClientID && v.ClientSecret == req.ClientSecret {
			service = k
			break
		}
	}

	user, err := storage.GetUserByEmail(ctx, db, service+sbEmailSuffix)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user by email")
	}

	return user, nil
}

const (
	sbEmailSuffix = "@superbank.id"
)

// createJumpCloudUser creates a new user in the database for JumpCloud authentication
func (p *PermissionManagementImpl) createJumpCloudUser(ctx context.Context, db *sql.DB, userInfo *jumpcloud.UserInfo) (*storage.UserDTO, error) {
	fullName := userInfo.FirstName + " " + userInfo.LastName
	randomPassword, err := generateRandomPassword(32)

	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to generate random password")
	}

	hashedPassword, err := secret.GeneratePassword(randomPassword)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to hash random password")
	}

	newUser := &storage.UserDTO{
		Name:      fullName,
		Email:     userInfo.Email,
		Password:  hashedPassword,
		Status:    constants.StatusActive,
		UserID:    uuid.New().String(),
		CreatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy: sql.NullInt64{Int64: 0, Valid: true},
		UpdatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy: sql.NullInt64{Int64: 0, Valid: true},
	}
	userID, createErr := storage.CreateUser(ctx, db, newUser)

	if createErr != nil {
		return nil, createErr
	}
	newUser.ID = userID

	return newUser, nil
}

func generateRandomPassword(n int) (string, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}
