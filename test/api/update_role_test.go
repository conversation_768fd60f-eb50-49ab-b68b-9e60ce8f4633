package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Update Role", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := "PASSED"
		if desc.Failed {
			result = "FAILED"
		}
		fmt.Printf("Update Role: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 when success on update role", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()

		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"status": %d
		}`, updatedRole.ID, updatedRole.Name, updatedRole.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock get role by id
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// mock the update role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock get permission by role id
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "created_by", "updated_by",
				"element_id", "bitwise_value", "role_id",
			}))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		// Validate response
		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).Should(Equal(200))
		Expect(int64(responseBody["id"].(float64))).To(Equal(updatedRole.ID))
		Expect(int64(responseBody["status"].(float64))).To(Equal(updatedRole.Status))
		Expect(responseBody["name"]).To(Equal(updatedRole.Name))
		Expect(responseBody["updatedBy"]).To(Equal("Test User"))
		Expect(responseBody).To(HaveKey("updatedAt"))
	})

	It("should return 200 when success on update role status to 0", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()
		updatedRole.Status = 0

		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"status": %d
		}`, updatedRole.ID, updatedRole.Name, updatedRole.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock get role by id
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// mock the update role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock get permission by role id
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "created_by", "updated_by",
				"element_id", "bitwise_value", "role_id",
			}))

		// start here
		// errRow := redis.DeleteRedisKey(ctx, constants.UserIDRedisKey+userRow.UserID, constants.UpdateStatusRoleLogTag)
		user := resources.SampleDataUser()
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserByRoleID)).WithArgs(updatedRole.ID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

		mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(true, nil)

		mocker.ExpectExec(TestQueryRemoveUserRoleByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		// Validate response
		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).Should(Equal(200))
		Expect(int64(responseBody["id"].(float64))).To(Equal(updatedRole.ID))
		Expect(responseBody["name"]).To(Equal(updatedRole.Name))
		Expect(responseBody["updatedBy"]).To(Equal("Test User"))
		Expect(responseBody).To(HaveKey("updatedAt"))
	})

	It("should return 200 when updating role with ElementPermissionsRequest format (insert, update, delete)", func() {
		db, mocker, err := sqlmock.New()
		Expect(err).ShouldNot(HaveOccurred())

		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		Expect(err).ShouldNot(HaveOccurred())

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()

		// Existing permissions in DB
		existingPermissions := resources.SampleDataExistingRolesPermission()

		permissions := resources.SampleDataForRoleElementPermissions()
		permissionsJSON, _ := json.Marshal(permissions)

		// fmt.Print(body)
		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"status": %d,
			"elementPermissions": %s
		}`, updatedRole.ID, updatedRole.Name, updatedRole.Status, string(permissionsJSON)))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// Mock Redis
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// Mock get role
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// Update roles table first
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).
			WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// --- NEW: Get current permissions (roles_elements_permissions) ---
		rows := sqlmock.NewRows([]string{
			"id", "element_id", "bitwise_value", "role_id", "created_at", "updated_at", "created_by", "updated_by",
		})
		for _, p := range *existingPermissions {
			rows.AddRow(p.ID, p.ElementID, p.BitwiseValue, p.RoleID, nil, nil, nil, nil)
		}
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(role.ID).
			WillReturnRows(rows)

		// 1. SELECT total bitwise permission
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryTotalBitwisePermission)).
			WithArgs(0, 1).
			WillReturnRows(sqlmock.NewRows([]string{"bitwise_value"}).AddRow(3))

		// 2. SELECT total bitwise permission
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryTotalBitwisePermission)).
			WithArgs(2).
			WillReturnRows(sqlmock.NewRows([]string{"bitwise_value"}).AddRow(4))

		// --- Permission Actions ---
		// UPDATE
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRolesElementsPermissions)).WithArgs(int64(3), sqlmock.AnyArg(), sqlmock.AnyArg(), int64(1)).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// INSERT
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRolesElementsPermissions)).WithArgs(role.ID, int64(103), int64(4), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(3, 1))

		// DELETE
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryDeleteRolesElementsPermissions)).
			WithArgs(int64(2)).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// Audit Trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)
		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(),
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(),
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Send request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).To(Equal(200))
		Expect(int64(responseBody["id"].(float64))).To(Equal(updatedRole.ID))
		Expect(responseBody["name"]).To(Equal(updatedRole.Name))
		Expect(responseBody).To(HaveKey("updatedAt"))
	})

	It("should return 403: failed to create permission due to invalid access, when user lacks ROLE_MANAGEMENT permission", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()

		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"status": %d
		}`, updatedRole.ID, updatedRole.Name, updatedRole.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()

		delete(permissionDetail.Permissions, string(constants.RoleManagement)) // Simulate missing permission

		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock get role by id
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// mock the update role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock get permission by role id
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "created_by", "updated_by",
				"element_id", "bitwise_value", "role_id",
			}))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
	})

	It("should return 401: failed to create permission due to unauthorized user", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()

		body := hcl.JSON(fmt.Sprintf(`{
			"id": %d,
			"name": "%s",
			"status": %d
		}`, updatedRole.ID, updatedRole.Name, updatedRole.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

		// mock no user id on db
		mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

		// mock get role by id
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// mock the update role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock get permission by role id
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "created_by", "updated_by",
				"element_id", "bitwise_value", "role_id",
			}))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
	})

	It("should return 400: failed to update role due to missing fields", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		updatedRole := resources.SampleDataForUpdateRole()

		body := hcl.JSON(`{
		}`)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock get role by id
		mocker.ExpectQuery(TestQueryGetRoleByID).
			WithArgs(role.ID).
			WillReturnRows(mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).
				AddRow(role.ID, role.Name, role.Status, role.CreatedAt, role.UpdatedAt, role.CreatedBy, role.UpdatedBy))

		// mock the update role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateRole)).WithArgs(updatedRole.Name, updatedRole.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), updatedRole.ID).
			WillReturnResult(sqlmock.NewResult(updatedRole.ID, 1))

		// mock get permission by role id
		mocker.ExpectQuery(TestQueryGetPermissionByRoleID).
			WithArgs(updatedRole.ID).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "created_by", "updated_by",
				"element_id", "bitwise_value", "role_id",
			}))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Update Role", updatedRole.ID, updatedRole.CreatedBy.Int64, updatedRole.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		expectedResponse := `
			{
				"code": "badRequest",
				"message": "failed to validate request",
				"errors": [
					{
						"errorCode": "badRequest",
						"message": "failed to validate request Key: 'UpdateRoleRequest.Name' Error:Field validation for 'Name' failed on the 'required' tag"
					}
				]
			}
		`

		// Perform request
		res, err := permissionManagemeneClient.Put(UpdateRole, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})
})
