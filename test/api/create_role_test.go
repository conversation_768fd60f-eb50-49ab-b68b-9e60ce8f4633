package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Create Role", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := testResultPassed
		if desc.Failed {
			result = testResultFailed
		}
		fmt.Printf("Create Role: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 when success on create role without permission", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"status": %d
		}`, role.Name, role.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock the create role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRole)).WithArgs(role.Name, role.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(role.ID, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Create Role", role.ID, role.CreatedBy.Int64, role.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Post(CreateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		// Validate response
		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).Should(Equal(200))
		Expect(int64(responseBody["id"].(float64))).To(Equal(role.ID))
		Expect(int64(responseBody["status"].(float64))).To(Equal(role.Status))
		Expect(responseBody["name"]).To(Equal(role.Name))
		Expect(responseBody["createdBy"]).To(Equal("Test User"))
		Expect(responseBody).To(HaveKey("createdAt"))
	})

	It("should return 200 when success on create role with ElementPermissionsRequest", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()

		permissions := resources.SampleDataForRoleElementPermissions()
		permissionsJSON, _ := json.Marshal(permissions)

		// fmt.Print(body)
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"status": %d,
			"elementPermissions": %s
		}`, role.Name, role.Status, string(permissionsJSON)))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// 1. mock total bitwise permission
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryTotalBitwisePermission)).
			WithArgs(0, 1).
			WillReturnRows(sqlmock.NewRows([]string{"bitwise_value"}).AddRow(3))
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryTotalBitwisePermission)).
			WithArgs(2).
			WillReturnRows(sqlmock.NewRows([]string{"bitwise_value"}).AddRow(4))

		// 2. Then expect the role INSERT
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRole)).WithArgs(role.Name, role.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(role.ID, 1))

		// 3. Then permission matrix INSERT
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRolesElementsPermissions)).WithArgs(
			role.ID, permissions[0].ElementID, 3, sqlmock.AnyArg(), sqlmock.AnyArg(),
		).WillReturnResult(sqlmock.NewResult(1, 1))
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRolesElementsPermissions)).WithArgs(
			role.ID, permissions[1].ElementID, 4, sqlmock.AnyArg(), sqlmock.AnyArg(),
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Create Role", role.ID, role.CreatedBy.Int64, role.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Post(CreateRole, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())

		// Validate response
		var responseBody map[string]interface{}
		err = json.Unmarshal([]byte(res.Body.String), &responseBody)
		Expect(err).ShouldNot(HaveOccurred())

		Expect(res.StatusCode).Should(Equal(200))
		Expect(int64(responseBody["id"].(float64))).To(Equal(role.ID))
		Expect(int64(responseBody["status"].(float64))).To(Equal(role.Status))
		Expect(responseBody["name"]).To(Equal(role.Name))
		Expect(responseBody["createdBy"]).To(Equal("Test User"))
		Expect(responseBody).To(HaveKey("createdAt"))
	})

	It("should return 403: failed to create permission due to invalid access, when user lacks ROLE_MANAGEMENT permission", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"status": %d
		}`, role.Name, role.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()

		delete(permissionDetail.Permissions, string(constants.RoleManagement)) // Simulate missing permission

		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock the create role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRole)).WithArgs(role.Name, role.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(role.ID, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Create Role", role.ID, role.CreatedBy.Int64, role.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Post(CreateRole, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
	})

	It("should return 401: failed to create permission due to unauthorized user", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		body := hcl.JSON(fmt.Sprintf(`{
			"name": "%s",
			"status": %d
		}`, role.Name, role.Status))

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// return empty from redis
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

		// mock no user id on db
		mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Create Role", role.ID, role.CreatedBy.Int64, role.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		// Perform request
		res, err := permissionManagemeneClient.Post(CreateRole, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
	})

	It("should return 400: failed to create role due to missing fields", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)

		role := resources.SampleDataForCreateRole()
		body := hcl.JSON(`{
		}`)

		// mock db
		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		// mock permission
		permissionDetail := resources.SampleDataUserPermissionsDetail()
		binaryPermissionData, _ := json.Marshal(permissionDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryPermissionData), nil)

		// mock the create role query
		mocker.ExpectExec(regexp.QuoteMeta(TestQueryCreateRole)).WithArgs(role.Name, role.Status, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(role.ID, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailRole("Create Role", role.ID, role.CreatedBy.Int64, role.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		expectedResponse := `
			{
				"code":"badRequest",
				"message":"failed to validate request",
				"errors":[
					{
						"errorCode":"badRequest",
						"message":"failed to validate request Key: 'CreateRoleRequest.Name' Error:Field validation for 'Name' failed on the 'required' tag"
					}
				]
			}
		`

		// Perform request
		res, err := permissionManagemeneClient.Post(CreateRole, body, xfccHeader)

		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.BadRequest.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

})
