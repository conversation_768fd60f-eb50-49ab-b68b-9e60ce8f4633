package logic

import (
	"context"
	"encoding/base64"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetDocument gets a document
func (p *process) GetDocument(ctx context.Context, req *api.GetDocumentRequest) (*api.GetDocumentResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// get slave
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// get document by name
	document, err := storage.GetDocumentByName(ctx, slave, req.Name)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get document")
	}

	var ticket *storage.TicketDTO
	var valid bool
	if document.TicketID.Valid {
		// get ticket by id
		ticket, err = storage.GetTicketByID(ctx, slave, document.TicketID.Int64)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket")
		}
	}
	if ticket != nil {
		_, _, err = permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestForElement(ctx, ticket.ElementID, constants.BitwiseValueGeneralRead)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
		}

		// valid if user has general read access to the element
		valid = true
	} else {
		var user *permissionManagementStorage.UserDTO
		user, err = permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
		}

		// valid if user is the owner of the document
		valid = user.ID == document.CreatedBy.Int64
	}

	if !valid {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	obj, err := p.S3Client.GetObjectWithContext(ctx, p.AppConfig.S3Config.BucketName, "onedash-documents/"+document.Name)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to download document")
	}

	return &api.GetDocumentResponse{
		Id:      document.ID,
		Payload: base64.StdEncoding.EncodeToString(obj),
	}, nil
}
