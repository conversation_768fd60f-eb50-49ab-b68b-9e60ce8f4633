package logic

import (
	"context"
	"strconv"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
)

// mappingTransactionData maps the transaction data based on the request key
func (p process) mappingTransactionData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]any, error) {
	var (
		result = make(map[string]any)
		err    error
	)

	if req.IdentifierType != api.IdentifierType_SAFE_ID {
		return nil, errorwrapper.Error(apiError.BadRequest, "identifier type not supported")
	}

	switch req.Key {
	case constants.KeyTransactionList:
		if req.Payload["accountID"] == "" {
			// nolint: govet
			accountList, err := p.GetAccountListCustomerSearch(ctx, req)
			if err != nil {
				return nil, err
			}
			attachMeta(result, accountList)
			break
		}

		txReq := helper.GetTransactionListRequest{
			SafeID:         req.Identifier,
			AccountID:      req.Payload["accountID"],
			EndDate:        req.Payload["endDate"],
			StartDate:      req.Payload["startDate"],
			EndingAfter:    req.Payload["endingAfter"],
			StartingBefore: req.Payload["startingBefore"],
		}
		pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 64)
		if pageSize == 0 {
			pageSize = 20 // default page size
		}
		txReq.PageSize = pageSize

		if req.Payload["clientBatchID"] != "" {
			txReq.ClientBatchIDs = []string{req.Payload["clientBatchID"]}
		}
		// nolint: govet
		list, pagination, err := helper.GetTransactionListMapped(ctx, txReq, p.TransactionHistoryClient)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting transaction list")
		}
		result[constants.KeyTransactionList] = composeTableWithPaginationResponse(list, pagination)

	case constants.KeyTransactionDetails:
		result, err = helper.GetTransactionDetailsMapped(ctx, helper.GetTransactionDetailsRequest{
			SafeID:        req.Identifier,
			TransactionID: req.Payload["transactionID"],
			AccountID:     req.Payload["accountID"],
		}, p.TransactionHistoryClient)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}
