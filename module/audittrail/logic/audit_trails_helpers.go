// Package logic provide the business logic to handle requests.
package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
)

func (p *AuditTrailImpl) ConstructAndInsertAuditTrails(ctx context.Context, createdBy int64, identifier string, identifierType string, title string, description string, activityType string) (int64, error) {
	auditTrailDTO := p.constructAuditTrailDTO(createdBy, identifier, identifierType, title, description, activityType)
	return p.InsertAuditTrails(ctx, auditTrailDTO)
}

func (p *AuditTrailImpl) InsertAuditTrails(ctx context.Context, data *auditTrailStorage.AuditTrailsDTO) (int64, error) {
	master, err := storage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}
	ID, err := auditTrailStorage.CreateAuditTrails(ctx, master, data)
	if err != nil {
		slog.FromContext(ctx).Error("logic.InsertAuditTrails", fmt.Sprintf("error creating audit trails: %v", err.Error()))
		return 0, err
	}

	// Return the created permission
	return ID, nil
}

func (p *AuditTrailImpl) constructAuditTrailDTO(createdBy int64, identifier string, identifierType string, title string, description string, activityType string) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: createdBy, Valid: true},
		Identifier:     identifier,
		IdentifierType: identifierType,
		Title:          title,
		Description:    description,
		ActivityType:   activityType,
	}
}
