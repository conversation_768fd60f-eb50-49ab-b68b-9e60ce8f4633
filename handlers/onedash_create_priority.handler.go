package handlers

import (
	"context"

	apiError "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// CreatePriority is API to create priority
func (o *OnedashService) CreatePriority(ctx context.Context, req *api.CreatePriorityRequest) (*api.CreatePriorityResponse, error) {
	res, err := logic.Process.CreatePriority(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create priority")
	}
	return res, nil
}
