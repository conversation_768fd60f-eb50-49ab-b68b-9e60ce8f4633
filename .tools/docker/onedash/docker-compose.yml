services:
  redis-od-node:
    image: redis:6.2
    container_name: redis-od-node
    ports:
      - "30002:6379"  # Expose Redis on port 30002
    volumes:
      - redis-od-data:/data  # Persist Redis data
    networks:
      - onedash-net
    command: ["redis-server", "--appendonly", "yes"]  # Optional: Enable AOF persistence
  
  mysql:
    image: mysql:8.0
    container_name: mysql-od-container
    environment:
      MYSQL_DATABASE: onedash
      MYSQL_ALLOW_EMPTY_PASSWORD: true
    ports:
      - "30001:3306" # Expose MySQL on port 30001
    volumes:
      - mysql-od-data:/var/lib/mysql
    networks:
      - onedash-net
  
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    hostname: kafka
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      # KRaft settings
      KAFKA_NODE_ID: 1
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka:9093'
      
      # Critical listener configuration
      KAFKA_LISTENERS: INTERNAL://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093,EXTERNAL://0.0.0.0:29092  
      KAFKA_ADVERTISED_LISTENERS: INTERNAL://kafka:9092,EXTERNAL://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: INTERNAL
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      
      # Basic configuration
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_LOG_DIRS: '/tmp/kraft-combined-logs'
      
      # Cluster ID
      CLUSTER_ID: "MkU3OEVBNTcwNTJENDM2Qg"
    volumes:
    - ./kafka-data:/tmp/kraft-combined-logs
    command: >
      bash -c "
        echo 'Starting Kafka...' &&
        kafka-storage format -t MkU3OEVBNTcwNTJENDM2Qg -c /etc/kafka/kraft/server.properties --ignore-formatted || true &&
        /etc/confluent/docker/run
      "
    networks:
      - onedash-net
      
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "8077:8077"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: PLAINTEXT
      KAFKA_CLUSTERS_1_NAME: dev-backend-cluster
      KAFKA_CLUSTERS_1_BOOTSTRAPSERVERS: b-1.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094,b-2.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094,b-3.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094
      KAFKA_CLUSTERS_1_PROPERTIES_SECURITY_PROTOCOL: SSL
      SERVER_PORT: 8077
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_COM_PROVECTUS: DEBUG
    depends_on:
      kafka:
        condition: service_started  # Use service_healthy if you add healthchecks
    networks:
      - onedash-net
    restart: unless-stopped
  
networks:
  onedash-net:
    driver: bridge

volumes:
  mysql-od-data:
  redis-od-data: