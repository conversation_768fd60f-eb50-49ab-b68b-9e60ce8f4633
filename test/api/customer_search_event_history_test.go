package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	customerExperience "gitlab.myteksi.net/bersama/customer-experience/api"
	customerExperienceMock "gitlab.myteksi.net/bersama/customer-experience/api/mock"
	customerJournal "gitlab.myteksi.net/bersama/customer-journal/api"
	customerJournalMock "gitlab.myteksi.net/bersama/customer-journal/api/mock"
	customerMasterMock "gitlab.myteksi.net/bersama/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	commonStorage "gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage/mocks"
	customerJourneyExperiencePreference "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	customerJourneyExperiencePreferenceMock "gitlab.super-id.net/bersama/corex/customer-journey-experience/api/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search Event History", func() {
	var (
		db     *sql.DB
		mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockCustomerJournal = &customerJournalMock.CustomerJournal{}
		mockCustomerJourneyExperiencePreference = &customerJourneyExperiencePreferenceMock.PreferenceCenter{}

		config := &logic.MockProcessConfig{
			CustomerMaster:                      mockCustomerMaster,
			CustomerExperience:                  mockCustomerExperience,
			CustomerJournal:                     mockCustomerJournal,
			CustomerJourneyExperiencePreference: mockCustomerJourneyExperiencePreference,
			AppConfig:                           service.AppConfig,
		}
		logic.MockInitLogic(config)

		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch Event History", func() {
		When("user has valid permissions and getting activity log", func() {
			It("should successfully return activity log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "activityLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("activityLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Activity Log", "activityLog", nil, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Triggered Date", "activityLogTriggeredDate", "oneliner_datetime", 1, 1).
						AddRow(2, "Event", "activityLogEvent", "oneliner", 1, 2).
						AddRow(3, "Activity", "activityLogActivity", "oneliner", 1, 3).
						AddRow(4, "Before Value", "activityLogBeforeValue", "oneliner", 1, 4).
						AddRow(5, "After Value", "activityLogAfterValue", "oneliner", 1, 5).
						AddRow(6, "Trigger", "activityLogTrigger", "oneliner", 1, 6).
						AddRow(7, "Status", "activityLogStatus", "oneliner", 1, 7).
						AddRow(8, "Failed Reason", "activityLogFailedReason", "oneliner", 1, 8))

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"activityType": "UPDATE_EMAIL",
								"actionType": "EMAIL_UPDATE",
								"beforeValue": "<EMAIL>",
								"afterValue": "<EMAIL>",
								"trigger": "APP",
								"status": "COMPLETED",
								"error": "[ERR] Some error message appears here"
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"activityType": "UPDATE_PROFILE",
								"actionType": "ADDRESS_UPDATE",
								"beforeValue": "Old Address, Jakarta",
								"afterValue": "New Address, Jakarta",
								"trigger": "APP",
								"status": "COMPLETED",
								"error": ""
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeActivity,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "activityLog",
						"type": "table",
						"label": "Activity Log",
						"children": [
							{
								"key": "activityLogTriggeredDate",
								"type": "oneliner_datetime",
								"label": "Triggered Date"
							},
							{
								"key": "activityLogEvent",
								"type": "oneliner",
								"label": "Event"
							},
							{
								"key": "activityLogActivity",
								"type": "oneliner",
								"label": "Activity"
							},
							{
								"key": "activityLogBeforeValue",
								"type": "oneliner",
								"label": "Before Value"
							},
							{
								"key": "activityLogAfterValue",
								"type": "oneliner",
								"label": "After Value"
							},
							{
								"key": "activityLogTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "activityLogStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "activityLogFailedReason",
								"type": "oneliner",
								"label": "Failed Reason"
							}
						]
					},
					"data": {
						"activityLog": {
							"data": [
								{
									"activityLogActivity": "EMAIL_UPDATE",
									"activityLogAfterValue": "<EMAIL>",
									"activityLogBeforeValue": "<EMAIL>",
									"activityLogEvent": "UPDATE_EMAIL",
									"activityLogFailedReason": "Some error message appears here",
									"activityLogStatus": "COMPLETED",
									"activityLogTrigger": "APP",
									"activityLogTriggeredDate": "2024-07-05T08:43:16Z"
								},
								{
									"activityLogActivity": "ADDRESS_UPDATE",
									"activityLogAfterValue": "New Address, Jakarta",
									"activityLogBeforeValue": "Old Address, Jakarta",
									"activityLogEvent": "UPDATE_PROFILE",
									"activityLogFailedReason": "",
									"activityLogStatus": "COMPLETED",
									"activityLogTrigger": "APP",
									"activityLogTriggeredDate": "2024-05-08T14:45:13Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"cif": null,
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting MFA log", func() {
			It("should successfully return MFA log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "mfaLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("mfaLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "MFA Log", "mfaLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "mfaLogTimestamp", "oneliner_datetime", 1, 1).
						AddRow(2, "MFA Type", "mfaLogMfaType", "oneliner", 1, 2).
						AddRow(3, "Status", "mfaLogStatus", "oneliner", 1, 3).
						AddRow(4, "FMLC Transaction ID", "mfaLogFmlcTransactionId", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"mfaType": "FMLC",
								"status": "SUCCESS",
								"cooldownPeriod": "1720157796278244734",
								"failReason": "[ERR_FMLC_MATCH_SCORE] Score is below threshold",
								"fmlcFaceMatchScore": "0.65",
								"controlTriggered": "HIGH_AMOUNT",
								"transactionId": "mfa-12345-abcde",
								"latestSelfie": "yes",
								"trigger": "POST /identity/v1/user/login"
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"mfaType": "OTP",
								"status": "SUCCESS",
								"cooldownPeriod": "1715158313117959093",
								"failReason": "",
								"controlTriggered": "DEVICE_CHANGE",
								"trigger": "POST /identity/v1/user/email"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeMFA,
				}).Return(mockJournalResponse, nil).Once()

				// mock for GetSelfieImagePresignedURL
				mockCustomerExperience.On("GetSelfieImagePresignedURL", mock.Anything, mock.Anything).Return(&customerExperience.GetSelfieImagePresignedURLResponse{
					Data: "https://example.com/selfie/123456.jpg",
				}, nil).Once()

				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
					"items": [
						{
							"applications": [
								{
									"selfieFile": [
										{
											"URL": "https://example.com/selfie/123456.jpg",
										}
									]
								}
							]
						}
					]
				}`

				var mockCustomerExperienceResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockCustomerExperienceResponse)

				// mock for GetCustomerOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, mock.Anything).Return(mockCustomerExperienceResponse, nil).Times(2)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "mfaLog",
						"type": "table",
						"label": "MFA Log",
						"children": [
							{
								"key": "mfaLogTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "mfaLogMfaType",
								"type": "oneliner",
								"label": "MFA Type"
							},
							{
								"key": "mfaLogStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "mfaLogFmlcTransactionId",
								"type": "oneliner",
								"label": "FMLC Transaction ID"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"mfaLog": {
							"data": [
								{
									"mfaLogFmlcTransactionId": "mfa-12345-abcde",
									"mfaLogMfaType": "FMLC - Login",
									"mfaLogStatus": "SUCCESS",
									"mfaLogTimestamp": "2024-07-05T08:43:16Z"
								},
								{
									"mfaLogFmlcTransactionId": "",
									"mfaLogMfaType": "OTP - Email",
									"mfaLogStatus": "SUCCESS",
									"mfaLogTimestamp": "2024-05-08T14:45:13Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting device login info", func() {
			It("should successfully return device login info results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "deviceLoginInfo"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("deviceLoginInfo", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Device Login Info", "deviceLoginInfo", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Device ID", "deviceLoginInfoDeviceId", "oneliner", 1, 1).
						AddRow(2, "Device Brand", "deviceLoginInfoDeviceBrand", "oneliner", 1, 2).
						AddRow(3, "Device Model", "deviceLoginInfoDeviceModel", "oneliner", 1, 3).
						AddRow(4, "OS Name", "deviceLoginInfoOsName", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"deviceInfo": {
									"deviceID": "device-123456",
									"deviceBrand": "Samsung",
									"deviceModel": "Galaxy S23",
									"osName": "Android",
									"osVersion": "13.0"
								},
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"deviceInfo": {
									"deviceID": "device-abcdef",
									"deviceBrand": "Apple",
									"deviceModel": "iPhone 15 Pro",
									"osName": "iOS",
									"osVersion": "17.4"
								},
								"status": "FAILED",
								"failReason": "Invalid credentials"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeLogin,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "deviceLoginInfo",
						"type": "table",
						"label": "Device Login Info",
						"children": [
							{
								"key": "deviceLoginInfoDeviceId",
								"type": "oneliner",
								"label": "Device ID"
							},
							{
								"key": "deviceLoginInfoDeviceBrand",
								"type": "oneliner",
								"label": "Device Brand"
							},
							{
								"key": "deviceLoginInfoDeviceModel",
								"type": "oneliner",
								"label": "Device Model"
							},
							{
								"key": "deviceLoginInfoOsName",
								"type": "oneliner",
								"label": "OS Name"
							}
						]
					},
					"data": {
						"cif": null,
						"deviceLoginInfo": {
							"data": [
								{
									"deviceLoginInfoDeviceBrand": "Samsung",
									"deviceLoginInfoDeviceId": "device-123456",
									"deviceLoginInfoDeviceModel": "Galaxy S23",
									"deviceLoginInfoOsName": "Android"
								},
								{
									"deviceLoginInfoDeviceBrand": "Apple",
									"deviceLoginInfoDeviceId": "device-abcdef",
									"deviceLoginInfoDeviceModel": "iPhone 15 Pro",
									"deviceLoginInfoOsName": "iOS"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("user has valid permissions and getting facial matching log", func() {
			It("should successfully return facial matching log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "facialMatchingLivenessCheckLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("facialMatchingLivenessCheckLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Facial Matching & Liveness Check Log", "facialMatchingLivenessCheckLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Triggered Date", "facialMatchingTriggeredDate", "oneliner", 1, 1).
						AddRow(2, "Trigger", "facialMatchingTrigger", "oneliner", 1, 2).
						AddRow(3, "Type", "facialMatchingType", "oneliner", 1, 3).
						AddRow(4, "Status", "facialMatchingStatus", "oneliner", 1, 4).
						AddRow(5, "Failed Reason", "facialMatchingFailedReason", "oneliner", 1, 5))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"type": "",
								"trigger": "POST /identity/v1/user/login",
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"type": "",
								"trigger": "POST /identity/v1/user/login",
								"status": "FAILED",
								"failReason": "Invalid credentials"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeFacialAndLiveness,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "facialMatchingLivenessCheckLog",
						"type": "table",
						"label": "Facial Matching \u0026 Liveness Check Log",
						"children": [
							{
								"key": "facialMatchingTriggeredDate",
								"type": "oneliner",
								"label": "Triggered Date"
							},
							{
								"key": "facialMatchingTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "facialMatchingType",
								"type": "oneliner",
								"label": "Type"
							},
							{
								"key": "facialMatchingStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "facialMatchingFailedReason",
								"type": "oneliner",
								"label": "Failed Reason"
							}
						]
					},
					"data": {
						"cif": null,
						"facialMatchingLivenessCheckLog": {
							"data": [
								{
									"facialMatchingFailedReason": "",
									"facialMatchingStatus": "SUCCESS",
									"facialMatchingTrigger": "Login",
									"facialMatchingTriggeredDate": "2024-07-05T08:43:16Z",
									"facialMatchingType": ""
								},
								{
									"facialMatchingFailedReason": "Invalid credentials",
									"facialMatchingStatus": "FAILED",
									"facialMatchingTrigger": "Login",
									"facialMatchingTriggeredDate": "2024-05-08T14:45:13Z",
									"facialMatchingType": ""
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting interaction log", func() {
			It("should successfully return interaction log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "interactionLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("interactionLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Interaction Log", "interactionLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Delivery Date", "interactionLogDeliveryDate", "oneliner_datetime", 1, 1).
						AddRow(2, "Trigger", "interactionLogTrigger", "oneliner", 1, 2).
						AddRow(3, "Content", "interactionLogContent", "oneliner", 1, 3).
						AddRow(4, "Type", "interactionLogType", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"trigger": "SYSTEM",
								"content": {
									"title": "Your Transaction is Successful!",
									"value": ""
								},
								"type": "NOTIFICATION",
								"channel": "PUSH",
								"status": "DELIVERED",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"trigger": "USER",
								"content": {
									"value": "Your OTP code is 123456"
								},
								"type": "OTP",
								"channel": "SMS",
								"status": "FAILED",
								"failReason": "[ERR_DELIVERY_FAILED] Failed to deliver SMS, invalid phone number"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeInteraction,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "interactionLog",
						"type": "table",
						"label": "Interaction Log",
						"children": [
							{
								"key": "interactionLogDeliveryDate",
								"type": "oneliner_datetime",
								"label": "Delivery Date"
							},
							{
								"key": "interactionLogTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "interactionLogContent",
								"type": "oneliner",
								"label": "Content"
							},
							{
								"key": "interactionLogType",
								"type": "oneliner",
								"label": "Type"
							}
						]
					},
					"data": {
						"cif": null,
						"interactionLog": {
							"data": [
								{
									"interactionLogContent": "Your Transaction is Successful!",
									"interactionLogDeliveryDate": "2024-07-05T08:43:16Z",
									"interactionLogTrigger": "SYSTEM",
									"interactionLogType": "NOTIFICATION"
								},
								{
									"interactionLogContent": "Your OTP code is 123456",
									"interactionLogDeliveryDate": "2024-05-08T14:45:13Z",
									"interactionLogTrigger": "USER",
									"interactionLogType": "OTP"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting marketing preference log", func() {
			It("should successfully return marketing preference log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "marketingPreferenceLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("marketingPreferenceLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Marketing Preference Log", "marketingPreferenceLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "marketingPreferenceLogTimestamp", "oneliner_datetime", 1, 1).
						AddRow(2, "Channel", "marketingPreferenceLogChannel", "oneliner", 1, 2).
						AddRow(3, "Preference", "marketingPreferenceLogPreference", "oneliner", 1, 3).
						AddRow(4, "Status", "marketingPreferenceLogStatus", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"consentType": "PUSH",
								"consentValue": "True",
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"consentType": "EMAIL",
								"consentValue": "False",
								"status": "FAILED",
								"failReason": "[ERR_UPDATE] Failed to update preference"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeMarketingPreference,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "marketingPreferenceLog",
						"type": "table",
						"label": "Marketing Preference Log",
						"children": [
							{
								"key": "marketingPreferenceLogTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "marketingPreferenceLogChannel",
								"type": "oneliner",
								"label": "Channel"
							},
							{
								"key": "marketingPreferenceLogPreference",
								"type": "oneliner",
								"label": "Preference"
							},
							{
								"key": "marketingPreferenceLogStatus",
								"type": "oneliner",
								"label": "Status"
							}
						]
					},
					"data": {
						"cif": null,
						"marketingPreferenceLog": {
							"data": [
								{
									"marketingPreferenceLogChannel": "PUSH",
									"marketingPreferenceLogPreference": "Turn Off",
									"marketingPreferenceLogStatus": "SUCCESS",
									"marketingPreferenceLogTimestamp": "2024-07-05T08:43:16Z"
								},
								{
									"marketingPreferenceLogChannel": "EMAIL",
									"marketingPreferenceLogPreference": "Turn Off",
									"marketingPreferenceLogStatus": "FAILED",
									"marketingPreferenceLogTimestamp": "2024-05-08T14:45:13Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting marketing preference current", func() {
			It("should successfully return marketing preference current results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "marketingPreferenceCurrent"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("marketingPreferenceCurrent", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Marketing Preference Current", "marketingPreferenceCurrent", nil, 1, 3, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Push Notification", "marketingPreferenceCurrentPushNotification", "oneliner", 1, 1).
						AddRow(2, "Email", "marketingPreferenceCurrentEmail", "oneliner", 1, 2).
						AddRow(3, "SMS", "marketingPreferenceCurrentSms", "oneliner", 1, 3).
						AddRow(4, "Whatsapp", "marketingPreferenceCurrentWhatsapp", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				//// mock for GetConsentListInternal
				mockCustomerJourneyExperiencePreference.On("GetConsentListInternal", mock.Anything, mock.Anything).Return(&customerJourneyExperiencePreference.GetUserConsentResponse{
					Marketing: []customerJourneyExperiencePreference.ConsentData{
						{
							ConsentType: "PUSH",
							Value:       true,
						},
						{
							ConsentType: "EMAIL",
							Value:       false,
						},
						{
							ConsentType: "SMS",
							Value:       true,
						},
						{
							ConsentType: "WHATSAPP",
							Value:       false,
						},
					},
				}, nil)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "marketingPreferenceCurrent",
						"type": "section",
						"label": "Marketing Preference Current",
						"children": [
							{
								"key": "marketingPreferenceCurrentPushNotification",
								"type": "oneliner",
								"label": "Push Notification"
							},
							{
								"key": "marketingPreferenceCurrentEmail",
								"type": "oneliner",
								"label": "Email"
							},
							{
								"key": "marketingPreferenceCurrentSms",
								"type": "oneliner",
								"label": "SMS"
							},
							{
								"key": "marketingPreferenceCurrentWhatsapp",
								"type": "oneliner",
								"label": "Whatsapp"
							}
						]
					},
					"data": {
						"cif": null,
						"marketingPreferenceCurrentEmail": "Turn Off",
						"marketingPreferenceCurrentPushNotification": "Turn On",
						"marketingPreferenceCurrentSms": "Turn On",
						"marketingPreferenceCurrentWhatsapp": "Turn Off",
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
