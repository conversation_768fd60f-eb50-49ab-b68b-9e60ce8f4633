// Package pagination provides basic pagination cursor functions
package pagination

import (
	"encoding/base64"
	"encoding/json"

	"gitlab.myteksi.net/dbmy/ops-support/onedash-be/common/storage"
)

// DecodeCursor decodes a base64 string into a json
func DecodeCursor(encodedCursor string) (storage.PaginationCursor, error) {
	var result storage.PaginationCursor

	cursorByte, err := base64.StdEncoding.DecodeString(encodedCursor)
	if err != nil {
		return result, err
	}

	err = json.Unmarshal(cursorByte, &result)
	if err != nil {
		return result, err
	}

	return result, nil
}

// EncodeCursor encodes a json cursor into base64
func EncodeCursor(cursor storage.PaginationCursor) (string, error) {
	cursorByte, err := json.Marshal(cursor)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(cursorByte), nil
}

// ParsePaginationCursorData Get the cursor data from the given parameter
func ParsePaginationCursorData(startingBefore string, endingAfter string) (storage.PaginationCursor, error) {
	var cursorData storage.PaginationCursor
	var err error
	switch {
	case endingAfter != "":
		cursorData, err = DecodeCursor(endingAfter)
	case startingBefore != "":
		cursorData, err = DecodeCursor(startingBefore)
	}
	if err != nil {
		return cursorData, err
	}
	return cursorData, nil
}

// MapPaginationParameters maps the request parameters to the pagination parameters.
func MapPaginationParameters(userID string, startingBefore string, endingAfter string, startDate string, endDate string, pageSize int64) storage.PaginationParams {
	return storage.PaginationParams{
		UserID:         userID,
		StartingBefore: startingBefore,
		EndingAfter:    endingAfter,
		StartDate:      startDate,
		EndDate:        endDate,
		PageSize:       pageSize,
	}
}
