// Code generated by mockery v2.44.1. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.myteksi.net/dbmy/ops-support/onedash-be/audittrail/api"

	mock "github.com/stretchr/testify/mock"
)

// AuditTrail is an autogenerated mock type for the AuditTrail type
type AuditTrail struct {
	mock.Mock
}

// CreateAuditTrails provides a mock function with given fields: ctx, req
func (_m *AuditTrail) CreateAuditTrails(ctx context.Context, req *api.AuditTrailRequest) (*api.AuditTrailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateAuditTrails")
	}

	var r0 *api.AuditTrailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AuditTrailRequest) (*api.AuditTrailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AuditTrailRequest) *api.AuditTrailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AuditTrailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AuditTrailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAuditTrails provides a mock function with given fields: ctx, req
func (_m *AuditTrail) GetAuditTrails(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetAuditTrails")
	}

	var r0 *api.GetAuditTrailsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetAuditTrailsRequest) *api.GetAuditTrailsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetAuditTrailsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetAuditTrailsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewAuditTrail creates a new instance of AuditTrail. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAuditTrail(t interface {
	mock.TestingT
	Cleanup(func())
}) *AuditTrail {
	mock := &AuditTrail{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
